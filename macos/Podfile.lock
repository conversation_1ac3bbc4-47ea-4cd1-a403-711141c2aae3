PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - printing (1.0.0):
    - FlutterMacOS
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - printing (from `Flutter/ephemeral/.symlinks/plugins/printing/macos`)
  - sentry_flutter (from `Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - Sentry

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  printing:
    :path: Flutter/ephemeral/.symlinks/plugins/printing/macos
  sentry_flutter:
    :path: Flutter/ephemeral/.symlinks/plugins/sentry_flutter/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 4c41c08fc6d7c91f63bc7aec70ffe3730b04f563
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  package_info_plus: 12f1c5c2cfe8727ca46cbd0b26677728972d9a5b
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  printing: 1dd6a1fce2209ec240698e2439a4adbb9b427637
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 2df8b0aab7e4aba81261c230cbea31c82a62dd1b
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404

PODFILE CHECKSUM: bcfc42c9d1c0e3c07e86ba83573738b4ba2170a7

COCOAPODS: 1.16.2
