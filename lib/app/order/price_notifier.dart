import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/order_item.dart';

final mdqPriceNotifierProvider =
    StateNotifierProvider.autoDispose<PriceNotifier, PriceState>(
        (ref) => PriceNotifier());

final oosPriceNotifierProvider =
    StateNotifierProvider.autoDispose<PriceNotifier, PriceState>(
        (ref) => PriceNotifier());

final itemsPriceNotifierProvider =
    StateNotifierProvider.autoDispose<PriceNotifier, PriceState>(
        (ref) => PriceNotifier());

class PriceNotifier extends StateNotifier<PriceState> {
  PriceNotifier() : super(PriceState(price: 0.0, priceItems: {}));

  void addPrice(num amount) {
    state = state.copyWith(price: state.price + amount);
  }

  void subtractPrice(num amount) {
    state = state.copyWith(price: state.price - amount);
  }

  void setPrice(num amount) {
    state = state.copyWith(price: amount);
  }

  void addItem(PriceItem item, {bool dry = false}) {
    final key = '${item.item.variantId}';
    final updatedItems = Map<String, PriceItem>.from(state.priceItems)
      ..[key] = item;
    state = state.copyWith(priceItems: updatedItems);
    if (!dry) {
      // Triggering state update if not a dry run
    }
  }
}

class PriceState {
  final num price;
  final Map<String, PriceItem> priceItems;

  PriceState({
    required this.price,
    required this.priceItems,
  });

  // Helper method to calculate the total price
  num get total {
    double sum = 0.0;
    priceItems.forEach((_, item) {
      final itemPrice = item.item.price ?? 0.0;
      sum += item.count * itemPrice;
    });
    return sum;
  }

  // Calculate the number of items
  int get itemsLength {
    return priceItems.values
        .fold(0, (total, item) => total + int.parse('${item.count}'));
  }

  PriceState copyWith({
    num? price,
    Map<String, PriceItem>? priceItems,
  }) {
    return PriceState(
      price: price ?? this.price,
      priceItems: priceItems ?? this.priceItems,
    );
  }
}

class PriceItem {
  final OrderItem item;
  final num count;
  PriceItem({
    required this.item,
    required this.count,
  });
}
