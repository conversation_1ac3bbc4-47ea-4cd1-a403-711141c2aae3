import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/order_preview.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/driver.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_commons_flutter/utils/index.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

enum CatalogViewType { grid, menu }

enum FocusedField { quantity, pallet }

enum OrderType {
  sales('Sales Order'),
  purchase('Purchase Order'),
  advanceInvoice('Advance Invoice');

  final String name;

  const OrderType(this.name);
}

class FetchTransactionsParam {
  final String orderStatus;
  final List<DateTime?> selectedDates;
  final List<String> selectedOutlets;
  final String searchText;
  final int currentPage;
  int perPage;
  int totalPages;
  bool loaded;
  bool loadingMore;
  final int activeIndex;

  FetchTransactionsParam({
    required this.orderStatus,
    required this.selectedDates,
    required this.selectedOutlets,
    required this.searchText,
    required this.currentPage,
    this.perPage = 10,
    this.totalPages = 1,
    this.loaded = false,
    this.loadingMore = false,
    this.activeIndex = 0,
  });

  factory FetchTransactionsParam.defaultValue() {
    return FetchTransactionsParam(
      orderStatus: 'all',
      selectedDates: [],
      selectedOutlets: [],
      searchText: '',
      currentPage: 1,
      perPage: 10,
      totalPages: 0,
      loaded: false,
      loadingMore: false,
      activeIndex: 0,
    );
  }

  int get totalCount => totalPages * perPage;

  FetchTransactionsParam copyWith({
    String? orderStatus,
    List<DateTime?>? selectedDates,
    List<String>? selectedOutlets,
    String? searchText,
    int? currentPage,
    int? perPage,
    int? totalPages,
    bool? loaded,
    bool? loadingMore,
    int? activeIndex,
  }) {
    return FetchTransactionsParam(
      orderStatus: orderStatus ?? this.orderStatus,
      selectedDates: selectedDates ?? this.selectedDates,
      selectedOutlets: selectedOutlets ?? this.selectedOutlets,
      searchText: searchText ?? this.searchText,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
      loaded: loaded ?? this.loaded,
      loadingMore: loadingMore ?? this.loadingMore,
      activeIndex: activeIndex ?? this.activeIndex,
    );
  }

  FetchTransactionsParam fromQueryParams(QueryParameters params) {
    return FetchTransactionsParam(
      orderStatus: orderStatus,
      selectedDates: selectedDates,
      selectedOutlets: selectedOutlets,
      searchText: searchText,
      loaded: loaded,
      loadingMore: loadingMore,
      currentPage: params.page,
      perPage: params.perPage,
      totalPages: params.totalPages,
      activeIndex: activeIndex,
    );
  }
}

class FetchTransactionsResponse {
  final List<Transaction> transactions;
  final QueryParameters queryParams;

  FetchTransactionsResponse({
    required this.transactions,
    required this.queryParams,
  });
}

class ShipmentResult {
  final List<ExportOrderTracking> exportOrderTracking;
  final String trackingCode;
  ShipmentResult({
    required this.exportOrderTracking,
    required this.trackingCode,
  });

  ShipmentResult copyWith({
    List<ExportOrderTracking>? ukOrderTracking,
    String? trackingCode,
  }) {
    return ShipmentResult(
      exportOrderTracking: ukOrderTracking ?? exportOrderTracking,
      trackingCode: trackingCode ?? this.trackingCode,
    );
  }

  factory ShipmentResult.fromMap(Map<String, dynamic> map) {
    return ShipmentResult(
      exportOrderTracking: (map['orderStatus'] != null &&
              map['orderStatus'] is List &&
              map['orderStatus'].isNotEmpty)
          ? List<ExportOrderTracking>.from(
              map['orderStatus']?.map((x) => ExportOrderTracking.fromMap(x)))
          : [],
      trackingCode: map['trackingCode'] ?? '',
    );
  }
}

class OrderCalculationResult {
  final List<CalculatedItem> items;
  final num discounts;
  final num taxes;
  final num total;
  final num shippingFees;
  final num subtotal;
  final num promoItemTotal;
  final num promoSubtotal;
  final num promoTotal;

  OrderCalculationResult({
    required this.items,
    required this.discounts,
    required this.taxes,
    required this.total,
    required this.shippingFees,
    required this.subtotal,
    required this.promoItemTotal,
    required this.promoSubtotal,
    required this.promoTotal,
  });
}

class CalculatedItem {
  final num price;
  final num count;
  final num discount;
  final num total;
  final num tax;
  final Currency currency;
  final String name;
  final DateTime? createdAt;
  final num? shippingFee;
  final num promoItemTotal;
  final num promoSubtotal;
  final num promoTotal;

  CalculatedItem({
    required this.price,
    required this.count,
    required this.discount,
    required this.total,
    required this.tax,
    required this.currency,
    required this.name,
    this.createdAt,
    this.shippingFee,
    required this.promoItemTotal,
    required this.promoSubtotal,
    required this.promoTotal,
  });
}

class PrintOrderItem {
  final String name;
  final num price;
  final num discount;
  final num tax;
  final num total;
  final num quantity;
  final Currency currency;
  final DateTime? createdAt;

  PrintOrderItem({
    required this.name,
    required this.price,
    required this.discount,
    required this.tax,
    required this.total,
    required this.quantity,
    required this.currency,
    this.createdAt,
  });
}

class PrintOrderData {
  final String poOrderReference;
  final List<PrintOrderItem> poOrders;
  final PrintOrderCalculation poOrderCalculation;
  final Currency currency;
  final String status;
  final String? approvalStatus;
  final String? shippingStatus;
  final String? paymentStatus;

  PrintOrderData({
    required this.poOrderReference,
    required this.poOrders,
    required this.poOrderCalculation,
    required this.currency,
    required this.status,
    this.approvalStatus,
    this.shippingStatus,
    this.paymentStatus,
  });
}

class PrintOrderCalculation {
  final num subtotal;
  final num promoItemTotal;
  final num discount;
  final num total;
  final num amountDue;
  final num taxes;
  final num shippingFees;
  final num? quantityShipped;
  final num? shippingAdjustment;
  final num promoSubtotal;
  final num promoTotal;

  PrintOrderCalculation({
    required this.subtotal,
    required this.promoItemTotal,
    required this.discount,
    required this.total,
    required this.amountDue,
    required this.taxes,
    required this.shippingFees,
    this.quantityShipped,
    this.shippingAdjustment,
    required this.promoSubtotal,
    required this.promoTotal,
  });
}

class OrderPrepareError {
  final String tag;
  final String title;
  final String message;
  final String? type;

  const OrderPrepareError({
    required this.tag,
    required this.title,
    required this.message,
    this.type,
  });
}

class ProductsManager {
  final List<Variant> items;

  ProductsManager(this.items);

  // Group items by category
  Map<String, List<Variant>> groupByCategory() {
    final groupedItems = {
      for (var category in items.map((item) => item.category).toSet())
        category ?? 'Others': items
            .where((item) =>
                item.category?.toLowerCase() == category?.toLowerCase())
            .toList()
    };

    final sortedGroupedItems = Map<String, List<Variant>>.fromEntries(
      groupedItems.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    return sortedGroupedItems;
  }

  // Get items by category, or all items if "all" is specified
  List<Variant> getItemsByCategory(String categoryName, String? searchTerm) {
    List<Variant> filteredItems;

    if (categoryName.toLowerCase() == 'all') {
      // Start with all items if category is "all"
      filteredItems = items;
    } else {
      // Filter by category name
      filteredItems =
          items.where((item) => item.category == categoryName).toList();
    }

    // If searchTerm is not null, filter by name or category
    if (searchTerm != null && searchTerm.isNotEmpty) {
      final lowerCaseSearchTerm = searchTerm.toLowerCase();
      filteredItems = filteredItems
          .where((item) =>
              (item.name ?? '').toLowerCase().contains(lowerCaseSearchTerm) ||
              (item.category ?? '').toLowerCase().contains(lowerCaseSearchTerm))
          .toList();
    }

    return filteredItems;
  }

  // Get grouped items by category, filtered by categoryName and/or searchTerm
  Map<String, List<Variant>> getGroupedItemsByCategory(
      String categoryName, String? searchTerm) {
    final filteredItems = getItemsByCategory(categoryName, searchTerm);
    final groupedItems = {
      for (var category in filteredItems.map((item) => item.category).toSet())
        category ?? 'Others': filteredItems
            .where((item) =>
                item.category?.toLowerCase() == category?.toLowerCase())
            .toList()
    };

    final sortedGroupedItems = Map<String, List<Variant>>.fromEntries(
      groupedItems.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    return sortedGroupedItems;
  }

  // List<Variant> getItemsByCategory(String categoryName, String? searchTerm) {
  //   if (categoryName.toLowerCase() == 'all') {
  //     return items;
  //   }
  //   return items.where((item) => item.category == categoryName).toList();
  // }

  List<String> getCategoryNames() {
    final categories = <String>{};
    for (var item in items) {
      categories.add(item.category ?? '');
    }
    final categoryList =
        categories.where((category) => category.isNotEmpty).toList();
    categoryList.sort();
    return categoryList;
  }

  List<String> getCategoryFilter() {
    final categoryList = getCategoryNames();
    categoryList.insert(0, 'All');
    return categoryList.where((category) => category.isNotEmpty).toList();
  }

  List<String> getBrandFilter() {
    final brands = <String>{};
    for (var item in items) {
      brands.add(item.brandName ?? '');
    }
    final brandList = brands.where((brand) => brand.isNotEmpty).toList();
    brandList.sort();
    return brandList;
  }
}

class PrepareOrderParams {
  final String? outletId;
  final List<CartItem>? cartItems;
  final List<OrderItem>? orderItems;
  final Map<String, dynamic>? rawOrder;
  final String? extChannel;
  final String? contactPhone;
  final bool? preview;
  final String? draftOrderId;
  final bool isExportCountry;

  /// { userId, body }
  final Map<String, dynamic>? note;

  PrepareOrderParams({
    this.outletId,
    this.cartItems,
    this.orderItems,
    this.rawOrder,
    this.contactPhone,
    this.extChannel = 'SHOP.WEB',
    this.preview = true,
    this.draftOrderId,
    this.note,
    required this.isExportCountry,
  }) : assert(
            (cartItems != null && cartItems.isNotEmpty) ||
                (orderItems != null && orderItems.isNotEmpty) ||
                (rawOrder != null),
            'One of rawOrder or orderItems or cartItems must be provided');

  List<Map> get items => (orderItems != null && orderItems!.isNotEmpty)
      ? orderItems!.map((item) {
          return {
            ...item.toMap(),
            // 'variantId': item.variantId,
            // 'price': item.price ?? 0,
            // 'quantity': item.quantity,
            // 'name': item.name,
            // 'code': item.code,
            'status': 'open',
            // 'couponCode': '',
            // 'palletCount': '',
          };
        }).toList()
      : cartItems!.map((item) {
          return {
            ...item.variant.toMap(),
            // 'variantId': item.variant.variantId,
            // 'price': item.variant.price ?? 0,
            'quantity': item.count,
            // 'name': item.variant.name,
            // 'code': item.variant.code,
            'status': 'open',
            // 'couponCode': '',
            if (item.variant.supplierId != null)
              'supplier': item.variant.supplierId,
          };
        }).toList();

  Map<String, dynamic> toMap() {
    if (rawOrder != null) {
      return rawOrder!;
    }
    return {
      'items': items,
      'preview': true,
      'contactPhone': contactPhone,
      'issuedAt': DateTime.now().toIso8601String(),
      'retailOutletId': outletId,
      'extChannel': extChannel,
      if (draftOrderId != null) 'draftOrderId': draftOrderId!,
      if (note != null) 'notes': note,
    };
  }

  @override
  String toString() => '${toMap()}';

  PrepareOrderParams copyWith({
    String? outletId,
    List<CartItem>? cartItems,
    List<OrderItem>? orderItems,
    Map<String, dynamic>? rawOrder,
    String? extChannel,
    String? contactPhone,
    bool? preview,
    String? draftOrderId,
    Map<String, dynamic>? note,
    bool? isExportCountry,
  }) {
    return PrepareOrderParams(
      outletId: outletId ?? this.outletId,
      cartItems: cartItems ?? this.cartItems,
      orderItems: orderItems ?? this.orderItems,
      rawOrder: rawOrder ?? this.rawOrder,
      extChannel: extChannel ?? this.extChannel,
      contactPhone: contactPhone ?? this.contactPhone,
      preview: preview ?? this.preview,
      draftOrderId: draftOrderId ?? this.draftOrderId,
      note: note ?? this.note,
      isExportCountry: isExportCountry ?? this.isExportCountry,
    );
  }
}

class ShippingRateParams {
  final Map<String, dynamic> orders;
  final String outletId;
  ShippingRateParams(this.orders, this.outletId);

  Map<String, dynamic> toMap() {
    return {
      'orders': orders,
      'retailOutletId': outletId,
    };
  }

  @override
  String toString() => '${toMap()}';
}

class UpdateDraftParams {
  final String id;
  final String outletId;
  final List<OrderItem> items;
  final String? note;
  final String draftOrderId;
  UpdateDraftParams({
    required this.id,
    required this.outletId,
    required this.items,
    this.note,
    required this.draftOrderId,
  });

  Map<String, dynamic> toMap() {
    return {
      '_id': id,
      'retailOutletId': outletId,
      'items': items.map((e) => e.toMap()).toList(),
      if (note != null) 'note': note,
      'draftOrderId': draftOrderId,
    };
  }
}

class ExportCartOrderParams {
  List<OrderPreview>? orders;
  final String? userId;
  final String? outletId;
  final String domain;
  final String? couponId;
  final Map<String, dynamic>? shippingDetails;
  final Map<String, dynamic>? processingCost;
  final String? shippingType;
  ExportCartOrderParams({
    required this.orders,
    required this.userId,
    required this.outletId,
    this.domain = 'SHOP.B2B',
    this.couponId,
    this.shippingDetails,
    this.processingCost,
    this.shippingType,
  });

  Map<String, dynamic> toMap() {
    return {
      'orders': orders?.map((OrderPreview? x) => x?.toMap()).toList(),
      if (userId != null) 'extUserId': userId,
      if (outletId != null) 'retailOutletId': outletId,
      'domain': domain,
      // 'extChannel': getExtChannel(),
      if (couponId != null) 'couponId': couponId,
      if (shippingDetails != null) 'shippingDetails': shippingDetails,
      if (processingCost != null) 'processingCost': processingCost,
      if (shippingType != null) 'shippingType': shippingType,
    };
  }

  @override
  String toString() => '${toMap()}';
}

class CreateOrderParams {
  Map<String, dynamic> orders;
  CreateOrderParams({
    required this.orders,
  });

  Map<String, dynamic> toMap() {
    return orders;
  }

  @override
  String toString() => '${toMap()}';
}

class AssignOrdersParams {
  final String locationId;
  final String driverId;
  final List<String> orderIds;
  AssignOrdersParams({
    required this.locationId,
    required this.driverId,
    required this.orderIds,
  });

  Map<String, dynamic> toMap() {
    return {
      'driverId': driverId,
      'orderIds': orderIds,
    };
  }

  @override
  String toString() => '${toMap()}';
}

class ChargeOrderParams {
  final num? creditBalance;
  final num? availableBalance;
  final num orderTotal;
  final bool payWithWallet;
  final bool payWithCredit;
  final bool payWithBoth;

  /// needed when doing a post charge
  final String? orderPaymentReference;
  final num? amount;
  // final String? paymentMethod;

  ChargeOrderParams({
    required this.payWithWallet,
    required this.payWithCredit,
    required this.payWithBoth,
    this.creditBalance,
    this.availableBalance,
    required this.orderTotal,
    this.orderPaymentReference,
    this.amount,
    // this.paymentMethod,
  });

  Map<String, dynamic> toMap() {
    if (payWithWallet) {
      // {availableBalance: 5290.11, creditBalance: 0, orderPaymentReference: 01-2200108875}
      return {
        'availableBalance': orderTotal > 0 ? orderTotal : (amount ?? 0),
        'creditBalance': 0,
        if (orderPaymentReference != null)
          'orderPaymentReference': orderPaymentReference,
      };
    }

    if (payWithCredit) {
      return {
        'creditBalance': orderTotal > 0 ? orderTotal : (amount ?? 0),
        'availableBalance': 0,
        if (orderPaymentReference != null)
          'orderPaymentReference': orderPaymentReference,
      };
    }

    if (payWithBoth) {
      return {
        'creditBalance': orderTotal - (availableBalance ?? 0),
        'availableBalance': availableBalance ?? 0,
        if (orderPaymentReference != null)
          'orderPaymentReference': orderPaymentReference,
      };
    }

    return {};
  }

  ChargeOrderParams copyWith({
    num? creditBalance,
    num? availableBalance,
    String? orderPaymentReference,
    num? amount,
    num? orderTotal,
    bool? payWithWallet,
    bool? payWithCredit,
    bool? payWithBoth,
  }) {
    return ChargeOrderParams(
      payWithWallet: payWithWallet ?? this.payWithWallet,
      payWithCredit: payWithCredit ?? this.payWithCredit,
      payWithBoth: payWithBoth ?? this.payWithBoth,
      orderTotal: orderTotal ?? this.orderTotal,
      creditBalance: creditBalance ?? this.creditBalance,
      availableBalance: availableBalance ?? this.availableBalance,
      orderPaymentReference:
          orderPaymentReference ?? this.orderPaymentReference,
      amount: amount ?? this.amount,
      // paymentMethod: paymentMethod ?? this.paymentMethod,
    );
  }
}

// class OrderPreviewManger {
//   final List<OrderPreview> previews;
//   final dynamic ref;

//   OrderPreviewManger(this.previews, this.ref);

//   String get currencyCode => previews.isNotEmpty
//       ? previews.first.currency?.iso ?? ref.read(currencyCodeProvider)
//       : ref.read(currencyCodeProvider);

//   num get total => previews.fold(
//       0.0, (num sum, OrderPreview current) => sum + (current.total ?? 0));

//   num get subTotal => previews.fold(
//       0.0, (num sum, OrderPreview current) => sum + (current.subTotal ?? 0));

//   num get discount => previews.fold(
//       0.0, (num sum, OrderPreview current) => sum + (current.discounts ?? 0));

//   num get taxes => previews.fold(
//       0.0, (num sum, OrderPreview current) => sum + (current.taxes ?? 0));
// }

class CreateOrderResponse {
  final String? reference;
  final PaymentTransaction? paymentTransaction;

  CreateOrderResponse({
    this.reference,
    this.paymentTransaction,
  });

  CreateOrderResponse copyWith({
    String? reference,
    PaymentTransaction? paymentTransaction,
  }) {
    return CreateOrderResponse(
      reference: reference ?? this.reference,
      paymentTransaction: paymentTransaction ?? this.paymentTransaction,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'reference': reference,
      'paymentTransaction': paymentTransaction?.toMap(),
    };
  }

  factory CreateOrderResponse.fromMap(Map<String, dynamic> map) {
    return CreateOrderResponse(
      reference: map['reference'],
      paymentTransaction: map['paymentTransaction'] != null
          ? PaymentTransaction.fromMap(map['paymentTransaction'])
          : null,
    );
  }

  @override
  String toString() => '${toMap()}';
}

class PaymentTransaction {
  final String? id;
  final Currency? currency;
  final String? retailOutletId;
  final num? amount;
  final num? deposit;
  final String? objectType;
  final num? shippingCost;
  final num? discounts;
  final num? processingCost;
  final num? itemTotal;
  final num? tax;
  final String? summary;
  final String? reference;
  final String? country;
  final String? state;
  final String? shippingType;
  final DateTime? postingDate;
  final DateTime? updatedAt;
  final DateTime? createdAt;
  final String? status;
  final String? shippingStatus;
  final int? pickupCode;
  final bool? isGlobal;

  PaymentTransaction({
    this.id,
    this.currency,
    this.retailOutletId,
    this.amount,
    this.deposit,
    this.objectType,
    this.shippingCost,
    this.discounts,
    this.processingCost,
    this.itemTotal,
    this.tax,
    this.summary,
    this.reference,
    this.country,
    this.state,
    this.shippingType,
    this.postingDate,
    this.updatedAt,
    this.createdAt,
    this.status,
    this.shippingStatus,
    this.pickupCode,
    this.isGlobal,
  });

  PaymentTransaction copyWith({
    String? id,
    Currency? currency,
    String? retailOutletId,
    num? amount,
    num? deposit,
    String? objectType,
    num? shippingCost,
    num? discounts,
    num? processingCost,
    num? itemTotal,
    num? tax,
    String? summary,
    String? reference,
    String? country,
    String? state,
    String? shippingType,
    DateTime? postingDate,
    DateTime? updatedAt,
    DateTime? createdAt,
    String? status,
    String? shippingStatus,
    int? pickupCode,
    bool? isGlobal,
  }) {
    return PaymentTransaction(
      id: id ?? this.id,
      currency: currency ?? this.currency,
      retailOutletId: retailOutletId ?? this.retailOutletId,
      amount: amount ?? this.amount,
      deposit: deposit ?? this.deposit,
      objectType: objectType ?? this.objectType,
      shippingCost: shippingCost ?? this.shippingCost,
      discounts: discounts ?? this.discounts,
      processingCost: processingCost ?? this.processingCost,
      itemTotal: itemTotal ?? this.itemTotal,
      tax: tax ?? this.tax,
      summary: summary ?? this.summary,
      reference: reference ?? this.reference,
      country: country ?? this.country,
      state: state ?? this.state,
      shippingType: shippingType ?? this.shippingType,
      postingDate: postingDate ?? this.postingDate,
      updatedAt: updatedAt ?? this.updatedAt,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      shippingStatus: shippingStatus ?? this.shippingStatus,
      pickupCode: pickupCode ?? this.pickupCode,
      isGlobal: isGlobal ?? this.isGlobal,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'currency': currency?.toMap(),
      'retailOutletId': retailOutletId,
      'amount': amount,
      'deposit': deposit,
      'objectType': objectType,
      'shippingCost': shippingCost,
      'discounts': discounts,
      'processingCost': processingCost,
      'itemTotal': itemTotal,
      'tax': tax,
      'summary': summary,
      'reference': reference,
      'country': country,
      'state': state,
      'shippingType': shippingType,
      'postingDate': postingDate?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'status': status,
      'shippingStatus': shippingStatus,
      'pickupCode': pickupCode,
      'isGlobal': isGlobal,
    };
  }

  factory PaymentTransaction.fromMap(Map<String, dynamic> map) {
    return PaymentTransaction(
      id: map['_id'],
      currency:
          map['currency'] != null ? Currency.fromMap(map['currency']) : null,
      retailOutletId: map['retailOutletId'],
      amount: map['amount'],
      deposit: map['deposit'],
      objectType: map['objectType'],
      shippingCost: map['shippingCost'],
      discounts: map['discounts'],
      processingCost: map['processingCost'],
      itemTotal: map['itemTotal'],
      tax: map['tax'],
      summary: map['summary'],
      reference: map['reference'],
      country: map['country'],
      state: map['state'],
      shippingType: map['shippingType'],
      postingDate: parseDate(map['postingDate']),
      updatedAt: parseDate(map['updatedAt']),
      createdAt: parseDate(map['createdAt']),
      status: map['status'],
      shippingStatus: map['shippingStatus'],
      pickupCode: parseInt(map['pickupCode']),
      isGlobal: map['isGlobal'],
    );
  }

  @override
  String toString() => '${toMap()}';
}

class FetchSalesOrdersParams {
  final String status;
  final List<DateTime?> selectedDates;
  final String searchText;
  final int currentPage;
  int perPage;
  int totalPages;
  bool loaded;
  bool loadingMore;
  final int activeIndex;

  FetchSalesOrdersParams({
    required this.status,
    required this.selectedDates,
    required this.searchText,
    required this.currentPage,
    this.perPage = 10,
    this.totalPages = 1,
    this.loaded = false,
    this.loadingMore = false,
    this.activeIndex = 0,
  });

  factory FetchSalesOrdersParams.defaultValue() {
    return FetchSalesOrdersParams(
      status: 'all',
      selectedDates: [],
      searchText: '',
      currentPage: 1,
      perPage: 10,
      totalPages: 0,
      loaded: false,
      loadingMore: false,
      activeIndex: 0,
    );
  }

  int get totalCount => totalPages * perPage;

  FetchSalesOrdersParams copyWith({
    String? status,
    List<DateTime?>? selectedDates,
    String? searchText,
    int? currentPage,
    int? perPage,
    int? totalPages,
    bool? loaded,
    bool? loadingMore,
    int? activeIndex,
  }) {
    return FetchSalesOrdersParams(
      status: status ?? this.status,
      selectedDates: selectedDates ?? this.selectedDates,
      searchText: searchText ?? this.searchText,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
      loaded: loaded ?? this.loaded,
      loadingMore: loadingMore ?? this.loadingMore,
      activeIndex: activeIndex ?? this.activeIndex,
    );
  }

  FetchSalesOrdersParams fromQueryParams(QueryParameters params) {
    return FetchSalesOrdersParams(
      status: status,
      selectedDates: selectedDates,
      searchText: searchText,
      loaded: loaded,
      loadingMore: loadingMore,
      currentPage: params.page,
      perPage: params.perPage,
      totalPages: params.totalPages,
      activeIndex: activeIndex,
    );
  }
}

class FetchSalesOrdersResponse {
  final List<Order> orders;
  final QueryParameters queryParams;

  FetchSalesOrdersResponse({
    required this.orders,
    required this.queryParams,
  });
}

class FetchSalesLocationDriverParams {
  final String locationId;

  FetchSalesLocationDriverParams({
    required this.locationId,
  });

  factory FetchSalesLocationDriverParams.defaultValue() {
    return FetchSalesLocationDriverParams(locationId: '');
  }

  FetchSalesLocationDriverParams copyWith({
    String? locationId,
  }) {
    return FetchSalesLocationDriverParams(
      locationId: locationId ?? this.locationId,
    );
  }
}

class FetchSalesLocationDriversResponse {
  final List<Driver> drivers;

  FetchSalesLocationDriversResponse({
    required this.drivers,
  });
}
