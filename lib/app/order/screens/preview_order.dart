import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:td_commons_flutter/models/order_confirmation.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/order_preview.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class PreviewOrderScreen extends ConsumerStatefulWidget {
  const PreviewOrderScreen({
    super.key,
    required this.fulfilledOrders,
    required this.unfulfilledOrders,
    required this.isDraftOrder,
    required this.updatingDraftOrder,
    required this.preparedOrder,
    required this.isDraftOrigin,
    this.orderConfirmation,
    this.isSalesOrderOrigin = false,
  });

  final List<OrderPreview> fulfilledOrders;
  final List<OrderPreview> unfulfilledOrders;
  final bool isDraftOrder;

  final bool isDraftOrigin;

  final bool updatingDraftOrder;
  final OrderPreviewDetail preparedOrder;
  final OrderConfirmation? orderConfirmation;
  final bool? isSalesOrderOrigin;

  @override
  ConsumerState<PreviewOrderScreen> createState() => PreviewOrderScreenState();
}

class PreviewOrderScreenState extends ConsumerState<PreviewOrderScreen> {
  late List<OrderPreview> fulfilledOrders = widget.fulfilledOrders;
  late List<OrderPreview> unfulfilledOrders = widget.unfulfilledOrders;
  late bool isDraftOrder = widget.isDraftOrder;
  late OrderPreviewDetail preparedOrder = widget.preparedOrder;
  bool shouldPrepareOrder = false;

  late final _disableCreateOrder = ValueNotifier<bool>(fulfilledOrders.isEmpty);
  final _isLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverHeaderDelegate(
              minHeight: 70,
              maxHeight: 70,
              child: Container(
                color: Colors.white,
                child: Row(
                  children: [
                    IconButton(
                      icon: SvgPicture.asset('$kSvgDir/order/close.svg'),
                      onPressed: () => context.pop(),
                    ),
                    const Gap(10),
                    Text(
                      'Confirm Order',
                      style: textTheme.bodyLarge?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 75),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (unfulfilledOrders.isNotEmpty) ...[
                    ..._buildUnfulfilledOrdersWidgets(),
                    _buildFulfilledOrdersWidgets(),
                    const Gap(10),
                    CustomFilledButton(
                      text: 'Proceed to create order',
                      disabledNotifier: _disableCreateOrder,
                      loaderNotifier: _isLoading,
                      onPressed: () {
                        shouldPrepareOrder = true;
                        handleOrderCreation();
                      },
                    ),
                    const Gap(10),
                    Center(
                      child: TextButton(
                        onPressed: () => context.pop(),
                        style: ButtonStyle(
                          shape: WidgetStateProperty.all<OutlinedBorder>(
                              const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(30.0),
                            ),
                          )),
                        ),
                        child: const Text('Cancel order creation'),
                      ),
                    ),
                  ] else ...[
                    _buildConfirmOrderWidget(),
                    const Gap(20),
                    _summaryRow('Subtotal', subTotal),
                    _summaryRow('Discount', discount),
                    _summaryRow('Taxes', taxes),
                    _summaryRow('Amount due', total),
                    const Gap(20),
                    SizedBox(
                      width: double.maxFinite,
                      child: CustomFilledButton(
                        text:
                            '${widget.updatingDraftOrder ? 'Update' : 'Create'} ${isDraftOrder ? 'draft' : 'order'}',
                        loaderNotifier: _isLoading,
                        onPressed: handleOrderCreation,
                      ),
                    ),
                    const Gap(10),
                    Center(
                      child: TextButton(
                        onPressed: () {
                          if (widget.unfulfilledOrders.isNotEmpty) {
                            setState(() {
                              fulfilledOrders = widget.fulfilledOrders;
                              unfulfilledOrders = widget.unfulfilledOrders;
                              preparedOrder = widget.preparedOrder;
                            });
                          } else {
                            context.pop();
                          }
                        },
                        style: ButtonStyle(
                          shape: WidgetStateProperty.all<OutlinedBorder>(
                              const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(30.0),
                            ),
                          )),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String get currencyCode => fulfilledOrders.isNotEmpty
      ? fulfilledOrders.first.currency?.iso ?? ref.read(currencyCodeProvider)
      : ref.read(currencyCodeProvider);

  List<OrderItem> get orderItems => fulfilledOrders
      .expand((order) => order.items ?? [])
      .whereType<OrderItem>()
      .toList();

  num get discount => fulfilledOrders.fold<num>(0.0, (sum, current) {
        if (current.hasPromotions != true) return sum;

        final hasDirectDiscount =
            current.discounts != null && current.discounts! > 0;

        final itemDiscount = hasDirectDiscount
            ? current.discounts!
            : (current.items ?? []).fold<num>(0.0, (itemSum, item) {
                if (item.promoDiscount == null) return itemSum;
                return itemSum + (item.promoDiscount! * (item.quantity ?? 0));
              });

        return sum + itemDiscount;
      });

  num get subTotal {
    num subtotal = fulfilledOrders.fold<num>(
      0.0,
      (sum, current) => sum + (current.subTotal ?? 0),
    );
    return subtotal + discount;
  }

  num get taxes => fulfilledOrders.fold<num>(
      0.0, (sum, current) => sum + (current.taxes ?? 0));

  num get total {
    num total = fulfilledOrders.fold(
        0.0, (num sum, OrderPreview current) => sum + (current.total ?? 0));

    if (widget.orderConfirmation != null) {
      final oc = widget.orderConfirmation;

      final promoTaxes = fulfilledOrders.fold<num>(0.0,
          (num sum, OrderPreview current) => sum + (current.promoTaxes ?? 0));

      final salesTax = (oc?.shippingDetails.standardCost?.tax ?? 0) +
          (oc?.shippingDetails.processingCost?.tax ?? 0) +
          promoTaxes;

      total = subTotal +
          (oc?.shippingDetails.standardCost?.amount ?? 0) +
          (oc?.shippingDetails.processingCost?.amount ?? 0) +
          salesTax -
          discount;
    }

    // total = total + taxes;

    return total;
  }

  TextTheme get textTheme => Theme.of(context).textTheme;

  Widget _buildFulfilledOrdersWidgets() {
    if (fulfilledOrders.isEmpty) return const SizedBox.shrink();

    return OrderItemsWidget(
      items: orderItems,
      currencyCode: currencyCode,
      title: 'Available Items',
      message: 'The following items are available for purchase',
      isSalesOrderOrigin: widget.isSalesOrderOrigin!,
    );
  }

  List<Widget> _buildUnfulfilledOrdersWidgets() {
    final groupedByError = <String, List<OrderPreview>>{};

    for (var orderPreview in unfulfilledOrders) {
      final error = orderPreview.metadata?.error ?? 'error';
      groupedByError.putIfAbsent(error, () => []).add(orderPreview);
    }

    return groupedByError.entries.map((entry) {
      final errorTag = getOrderPrepareErrorByTag(entry.key);
      final orderItems = entry.value
          .expand((order) => order.items ?? [])
          .whereType<OrderItem>()
          .toList();

      return OrderItemsWidget(
        items: orderItems,
        currencyCode: currencyCode,
        title: errorTag?.title ?? '',
        message: errorTag?.message ?? '',
        errorText: entry.key == belowMdv ? errorTag?.title : null,
        fixable: entry.key == belowMdv,
        onOrderQuantityChanged: (value) {
          shouldPrepareOrder = value;
          _disableCreateOrder.value = false;
        },
        isSalesOrderOrigin: widget.isSalesOrderOrigin!,
      );
    }).toList();
  }

  Widget _buildConfirmOrderWidget() {
    final orderItems = fulfilledOrders
        .expand((e) => e.items ?? [])
        .whereType<OrderItem>()
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(20),
        Text('Confirm order details', style: textTheme.headlineMedium),
        const Gap(8),
        Text(
          'Review and reconfirm your order details',
          style: textTheme.bodyLarge?.copyWith(
            color: Palette.blackSecondary,
          ),
        ),
        const Gap(16),
        Text(
          'Order details',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const Gap(26),
        ...mapIndexed(
          orderItems,
          (index, order) => OrderItemWidget(order, currencyCode),
        ),
      ],
    );
  }

  Widget _summaryRow(
    String name,
    num amount,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            name,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          CurrencyWidget(amount, currencyCode)
        ],
      ),
    );
  }

  Future<void> handleOrderCreation() async {
    _isLoading.value = true;
    if (shouldPrepareOrder) {
      await handlePrepareOrder(false, preparedOrder);
    } else if (widget.isSalesOrderOrigin!) {
      final outlet = await getOutlet();
      final data = await createSalesOrder(outlet);
      _isLoading.value = false;
      if (data != null) {
        _isLoading.value = false;
        // ignore: use_build_context_synchronously
        context.goNamed(kSalesOrdersRoute, extra: true);
        resetCart(ref);
        resetSalesOrderCustomer(ref);
        Toast.success(
            'Your order has been successfully created!',
            // ignore: use_build_context_synchronously
            context);
      }
    } else {
      await createCart();
    }
    // _isLoading.value = false;
  }

  Future<void> handlePrepareOrder(
    bool? useRawOrder,
    OrderPreviewDetail? orderPrepared,
  ) async {
    _isLoading.value = true;
    final preparedOrders = await prepareOrder(
        context, ref, widget.isDraftOrigin, useRawOrder, orderPrepared);

    if (preparedOrders != null) {
      setState(() {
        preparedOrder = preparedOrders;
        fulfilledOrders = getFulfilledOrders(preparedOrders);
        unfulfilledOrders = getUnFulfilledOrders(preparedOrders);
        shouldPrepareOrder = false;
      });
    }
    _isLoading.value = false;
  }

  Future<void> createCart() async {
    _isLoading.value = true;
    final res = await ref.read(createNonExportCartUseCaseProvider(
        CreateOrderParams(orders: preparedOrder.rawData!)));

    res.when(
      success: (_) {
        isDraftOrder ? createOrUpdateDraftOrder() : checkOutletOptions();
      },
      failure: (error, code) async {
        if (code == 1251 && !shouldPrepareOrder) {
          await handlePrepareOrder(true, preparedOrder);
          createCart();
        } else {
          _isLoading.value = false;
          Toast.apiError(error, context, title: 'Error creating cart order');
        }
      },
    );
  }

  Future<bool> createOrUpdateDraftOrder([bool reRoute = true]) async {
    bool success = false;

    _isLoading.value = true;

    final params = widget.isDraftOrigin
        ? UpdateDraftParams(
            id: ref.read(cartProvider).transaction!.id,
            items: fulfilledOrders
                .expand((e) => e.items ?? [])
                .whereType<OrderItem>()
                .toList(),
            outletId: ref.read(cartProvider).branch!.id,
            note: ref.read(cartProvider).note,
            draftOrderId:
                ref.read(cartProvider).transaction?.draftOrderNumber ??
                    ref.read(cartProvider.notifier).getReference)
        : ref.read(cartProvider).branch!.id;

    final res = widget.isDraftOrigin
        ? await ref
            .read(updateDraftOrderUseCaseProvider(params as UpdateDraftParams))
        : await ref.read(createDraftOrderUseCaseProvider(params as String));

    res.when(
      success: (_) {
        success = true;

        if (reRoute) {
          final draftOrderMessage =
              'We have ${widget.isDraftOrigin ? 'updated your draft order' : 'saved your order as draft'}';

          goToOrders();

          Toast.success(
              isDraftOrder
                  ? draftOrderMessage
                  : 'Created your order successfully',
              context);
        }
      },
      failure: (e, code) async {
        if (code == 1251) {
          shouldPrepareOrder = true;
          await handleOrderCreation();
        } else {
          _isLoading.value = false;
          Toast.apiError(e, context,
              title:
                  'Error ${widget.isDraftOrigin ? 'updating' : 'creating'} draft order');
        }
      },
    );

    return success;
  }

  Future<void> checkOutletOptions() async {
    _isLoading.value = true;
    final outlet = await getOutlet();
    await processPayment(outlet);
    // if (outlet.paymentRequired == true) {
    //   await processPayment(outlet);
    // } else {
    //   final ref = await createOrder(outlet);
    //   if (ref != null) processPayment(outlet, ref);
    // }
  }

  // Future<bool> handlePaymentPlanWhenPaymentIsNotRequired(
  //     RetailOutlet outlet) async {
  //   if (outlet.loanContract?.status != "active" ||
  //       (outlet.loanContract?.creditBalance ?? 0) < total) {
  //     final res = await createOrUpdateDraftOrder(false);
  //     if (res) {
  //       showErrorToast(outlet);
  //       goToOrders();
  //     }
  //     return true;
  //   }
  //   return false;
  // }

  // Future<bool> handlePaymentPlanWhenPaymentIsNotRequired(
  //     RetailOutlet outlet) async {
  //   if (!((outlet.walletAccount?.currentBalance ?? 0) >= total) &&
  //       outlet.loanContract != null &&
  //       (outlet.loanContract?.status != "active" ||
  //           (outlet.loanContract?.creditBalance ?? 0) < total)) {
  //     final res = await createOrUpdateDraftOrder(false);
  //     if (res) {
  //       showErrorToast(outlet);
  //       goToOrders();
  //     }
  //     return true;
  //   }
  //   return false;
  // }

  Future<RetailOutlet> getOutlet() async {
    final res = await ref
        .read(getOutletUseCaseProvider(ref.read(cartProvider).branch!.id));
    return res.when(
      success: (outlet) => outlet,
      failure: (_, __) => ref.read(userControllerProvider)?.currentRetailOutlet,
    )!;
  }

  Future<void> processPayment(RetailOutlet outlet) async {
    _isLoading.value = true;

    final paymentRequiredBeforeOrder = outlet.paymentRequired ?? false;
    final walletBalance = outlet.walletAccount?.currentBalance ?? 0;
    final creditBalance = outlet.loanContract?.creditBalance ?? 0;

    final params = ChargeOrderParams(
      // orderPaymentReference: paymentReference,
      payWithWallet: walletBalance >= total,
      payWithCredit: creditBalance >= total,
      payWithBoth: (walletBalance + creditBalance) >= total,
      orderTotal: total,
      availableBalance: walletBalance,
      creditBalance: creditBalance,
    );

    final isThirdPartyOrder = !fulfilledOrders.any((x) =>
        (x.metadata?.supplier?.name ?? '')
            .toLowerCase()
            .contains('tradedepot'));

    if (params.payWithWallet ||
        params.payWithCredit ||
        params.payWithBoth ||
        isThirdPartyOrder) {
      final data = await createOrder(outlet);
      return await chargeOrder(
          params.copyWith(
              orderPaymentReference: data?.reference,
              amount: data?.paymentTransaction?.amount ?? total),
          outlet,
          true);
    } else if (hasCompletedAgreement(outlet)) {
      final res = await createOrUpdateDraftOrder(false);
      if (res) {
        final assigneeName = outlet.assignee.assigneeName;
        showErrorToast(outlet,
            'Hello, I need help with completing my order, I was asked to contact my account manager $assigneeName for assistance');
        goToOrders();
      }
    } else if (shouldSignAgreement(outlet)) {
      final res = await createOrUpdateDraftOrder(false);
      if (res) {
        final assigneeName = outlet.assignee.assigneeName;
        showAgreementErrorToast(outlet,
            'Hello, I am unable to see the customer agreement that was sent to my email. I was asked to contact my account manager $assigneeName for assistance`');
        goToOrders();
      }
    } else if (isMissingOnboardingInfo(outlet)) {
      final res = await createOrUpdateDraftOrder(false);
      if (res) {
        showMissingInfoErrorToast(outlet);
        goToOrders();
      }
    } else {
      final res = await createOrUpdateDraftOrder(false);
      if (res) {
        showErrorToast(outlet);
        goToOrders();
      }
    }

    _isLoading.value = false;
  }

  bool isDocuSignOutlet(RetailOutlet outlet) {
    return docuSignOutlets.contains(outlet.outletTypeId);
  }

  bool hasCompletedAgreement(RetailOutlet outlet) {
    final docuSignOutlet = isDocuSignOutlet(outlet);
    return docuSignOutlet && outlet.document?.termsAndCondition == "completed";
  }

  bool shouldSignAgreement(RetailOutlet outlet) {
    final docuSignOutlet = isDocuSignOutlet(outlet);
    return docuSignOutlet && outlet.document?.termsAndCondition != "completed";
  }

  bool isMissingOnboardingInfo(RetailOutlet outlet) {
    final docuSignOutlet = isDocuSignOutlet(outlet);

    return !docuSignOutlet &&
        (outlet.walletAccount?.accountNumber == null ||
            outlet.company?.kyb?.rcNumber == null);
  }

  Future<CreateOrderResponse?> createOrder(RetailOutlet outlet) async {
    // if (await handlePaymentPlanWhenPaymentIsNotRequired(outlet)) {
    //   return;
    // }

    _isLoading.value = true;
    final res = await ref.read(createNonExportOrderUseCaseProvider(
        CreateOrderParams(orders: preparedOrder.rawData!)));

    return res.when(
      success: (data) => data,
      failure: (error, code) {
        _isLoading.value = false;
        if (code == 1251) {
          // shouldPrepareOrder = true;
          // handleOrderCreation();
        } else if (error.error.toString() ==
            "Payment Required for this Outlet") {
        } else {
          Toast.apiError(error, context, title: 'Error creating order');
        }

        return null;
      },
    );
  }

  Future<dynamic> createSalesOrder(RetailOutlet outlet) async {
    _isLoading.value = true;
    final res = await ref.read(createSalesOrderUseCaseProvider(
        CreateOrderParams(orders: preparedOrder.rawData!)));

    return res.when(
      success: (data) => data,
      failure: (error, code) {
        Toast.apiError(error, context, title: 'Error creating order');
        return null;
      },
    );
  }

  Future<void> chargeOrder(ChargeOrderParams params, RetailOutlet outlet,
      [bool hasCreatedOrder = false]) async {
    _isLoading.value = true;
    final res = await ref.read(chargeOrderUseCaseProvider(params));

    res.when(
      success: (_) => goToOrdersWithSuccess(),
      failure: (error, code) {
        _isLoading.value = false;
        //5003, 5008, 5010
        if (!hasCreatedOrder && [5003, 5008, 5010].contains(code) ||
            (outlet.paymentRequired ?? false)) {
          showErrorToast(outlet);
          goToOrders();
        } else {
          goToOrdersWithSuccess();
        }
      },
    );
  }

  void goToOrders() {
    _isLoading.value = false;
    context.goNamed(kOrdersRoute, extra: {'isRefreshing': true});
    resetCart(ref);
  }

  void goToOrdersWithSuccess() {
    goToOrders();
    Toast.success('Created your order successfully', context);
  }

  void showErrorToast(RetailOutlet outlet, [String? intercomMessage]) {
    final draftErrorMessage = widget.isDraftOrigin
        ? 'Your draft will be updated'
        : 'It will be saved as a draft';

    Toast.error(
      'We are unable to place your order right now. $draftErrorMessage. Please contact your account manager or the support team for assistance to complete your order.',
      context,
      title: 'An Issue Occurred',
      action: CustomAction(
        'Contact Support',
        (_) {
          if (intercomMessage != null) {
            Intercom.instance.displayMessageComposer(intercomMessage);
          } else {
            Intercom.instance.displayMessenger();
          }
        },
      ),
      duration: 20,
    );

    _isLoading.value = false;
  }

  void showAgreementErrorToast(RetailOutlet outlet, [String? intercomMessage]) {
    Toast.error(
      'Please sign the customer agreement that was sent to your email address ${outlet.email}. If you did not receive this email, please contact your account manager or support for assistance. ${widget.updatingDraftOrder ? 'Your draft will be updated' : 'Your order will be saved as a draft'}',
      title: 'Action Required',
      context,
      action: CustomAction(
        'Contact Support',
        (_) {
          if (intercomMessage != null) {
            Intercom.instance.displayMessageComposer(intercomMessage);
          } else {
            Intercom.instance.displayMessenger();
          }
        },
      ),
      duration: 20,
    );

    _isLoading.value = false;
  }

  void showMissingInfoErrorToast(RetailOutlet outlet) {
    Toast.error(
      'Some information is missing in your onboarding. You need to provide this information before placing your order. ${widget.updatingDraftOrder ? 'Your draft will be updated' : 'Your order will be saved as a draft'}',
      context,
      title: 'Action Required',
      action: CustomAction(
        'Dismiss',
        (_) {
          Toast.dismissToast();
        },
      ),
      duration: 20,
    );

    _isLoading.value = false;
  }
}
