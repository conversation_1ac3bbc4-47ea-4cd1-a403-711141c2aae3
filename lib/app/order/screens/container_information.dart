import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/container_utilization.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class ContainerInformationScreen extends ConsumerWidget {
  const ContainerInformationScreen(this.previewDetail, {super.key});

  final OrderPreviewDetail previewDetail;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final cu = previewDetail.containerUtilization;
    final branch = ref.read(cartProvider).branch;

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return [
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 70,
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    decoration: BoxDecoration(
                      border: Border.all(color: Palette.stroke),
                    ),
                    child: Row(
                      children: [
                        Flexible(
                          child: IconButton(
                            icon: SvgPicture.asset('$kSvgDir/order/close.svg'),
                            onPressed: () {
                              context.pop();
                              // context.pop();
                              // resetCart(ref, forceClear: false);
                            },
                          ),
                        ),
                        // const Gap(10),
                        // Flexible(
                        //   child: InkWell(
                        //     focusColor: Colors.transparent,
                        //     highlightColor: Colors.transparent,
                        //     splashColor: Colors.transparent,
                        //     hoverColor: Colors.transparent,
                        //     onTap: () => context.pop(),
                        //     child: Text(
                        //       'Cart',
                        //       style: textTheme.bodyLarge
                        //           ?.copyWith(color: Palette.k6B797C),
                        //     ),
                        //   ),
                        // ),
                        // const Gap(16),
                        // SvgPicture.asset('$kSvgDir/packs/chevron_right.svg',
                        //     width: 14, height: 12),
                        // const Gap(10),
                        Flexible(
                          child: Text(
                            'Container information',
                            style: textTheme.bodyLarge
                                ?.copyWith(color: Palette.k6B797C),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 60),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(40),
                        Text(
                          'Delivery location',
                          style: textTheme.headlineSmall,
                        ),
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          title: Text(
                            branch?.outletBusinessName ?? '',
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Palette.blackSecondary,
                            ),
                          ),
                          subtitle: Text(
                            branch?.streetName ?? '',
                            style: textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w400,
                              color: Palette.blackSecondary,
                            ),
                          ),
                        ),
                        Divider(color: Palette.stroke, height: 60),
                        Text(
                          'Container information',
                          style: textTheme.headlineSmall,
                        ),
                        const Gap(8),
                        Text(
                          'Review container information and sourcing',
                          style: textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w400,
                            color: Palette.blackSecondary,
                          ),
                        ),
                        const Gap(40),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ];
        },
        body: DefaultTabController(
          length: 2,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Column(
              children: [
                Container(
                  height: 40,
                  decoration: BoxDecoration(
                      border: Border(top: BorderSide(color: Palette.stroke))),
                  child: TabBar(
                    isScrollable: true,
                    labelColor: Palette.primary,
                    unselectedLabelColor: Palette.primaryBlack,
                    indicatorColor: Palette.primary,
                    indicatorWeight: 1.0,
                    tabAlignment: TabAlignment.start,
                    dividerColor: Palette.stroke,
                    labelStyle: textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                    tabs: const [
                      Tab(text: "Direct Sourcing"),
                      Tab(text: "Truck Load"),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      ListView(
                        children: [
                          ...mapIndexed(
                              cu, (index, x) => ContainerWidget(x, index == 0)),
                        ],
                      ),
                      ListView(
                        children: [
                          ...mapIndexed(
                              cu, (index, x) => TruckWidget(x, index == 0)),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ContainerWidget extends StatelessWidget {
  const ContainerWidget(this.cUtil, this.isFirstIndex, {super.key});

  final ContainerUtilization cUtil;
  final bool isFirstIndex;

  @override
  Widget build(BuildContext context) {
    return Expandable(
      expanded: true,
      header: Text(cUtil.name.toString().toUpperCase()),
      content: Column(
        children: [
          _buildTableHeader(context),
          _buildTableRow(context, cUtil),
        ],
      ),
    );
  }

  Widget _buildTableHeader(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      // height: 60,
      padding: const EdgeInsets.symmetric(vertical: 5),
      // alignment: Alignment.bottomCenter,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
            top: BorderSide(color: Palette.stroke),
            bottom: BorderSide(color: Palette.stroke)),
      ),
      child: Table(
        defaultVerticalAlignment: TableCellVerticalAlignment.bottom,
        columnWidths: const {
          0: FlexColumnWidth(7),
          1: FlexColumnWidth(3),
          2: FlexColumnWidth(4),
          3: FlexColumnWidth(4),
          4: FlexColumnWidth(4),
          5: FlexColumnWidth(4),
        },
        children: [
          TableRow(
            children: [
              _buildHeaderCell('Description', textTheme,
                  showLimit: false, centered: false),
              _buildHeaderCell('Quantity (Cases)', textTheme,
                  hintText: 'Your current cart quantity', showLimit: false),
              _buildHeaderCell('Loadability limits', textTheme,
                  hintText: 'Number of cases that fill a container'),
              _buildHeaderCell('Container usage', textTheme,
                  hintText: 'Amount of space used up in the container'),
              _buildHeaderCell('Container required', textTheme,
                  hintText: 'Number of containers needed'),
              _buildHeaderCell('Utilization of last container', textTheme,
                  hintText: '% utilization of the last container'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme,
      {String? hintText, bool centered = true, bool showLimit = true}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment:
                centered ? MainAxisAlignment.center : MainAxisAlignment.start,
            children: [
              Text(
                text,
                style:
                    textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
                textAlign: centered ? TextAlign.center : TextAlign.start,
              ),
              if (hintText != null)
                Tooltip(
                  message: hintText,
                  preferBelow: false,
                  child: SvgPicture.asset('$kSvgDir/order/info.svg'),
                ),
            ],
          ),
          if (showLimit) ...[
            const Gap(10),
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('20ft'),
                Gap(45),
                Text('40ft'),
              ],
            )
          ],
        ],
      ),
    );
  }

  Widget _buildTableRow(BuildContext context, ContainerUtilization cu) {
    final textTheme = Theme.of(context).textTheme;

    final utils = cu.utilization;
    final summary = cu.summary;

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(7),
        1: FlexColumnWidth(3),
        2: FlexColumnWidth(4),
        3: FlexColumnWidth(4),
        4: FlexColumnWidth(4),
        5: FlexColumnWidth(4),
      },
      children: [
        ...mapIndexed(
          (utils?.mid ?? []),
          ((index, um) {
            final large = (utils?.large ?? [])[index];

            return TableRow(
              decoration: BoxDecoration(
                  border: Border(
                bottom: BorderSide(color: Palette.stroke),
              )),
              children: [
                _buildContentCell('${um.name}', textTheme,
                    bold: false, centered: false),
                _buildContentCell('${um.quantity ?? 0}', textTheme),
                _buildContentCell('${um.limit ?? 0}', textTheme,
                    limit: '${large.limit ?? 0}'),
                _buildContentCell(
                    '${summary?.mid?.containerUsage ?? 0}', textTheme,
                    limit: '${summary?.large?.containerUsage ?? 0}'),
                _buildContentCell('-', textTheme),
                _buildContentCell('-', textTheme),
              ],
            );
          }),
        ),
        // for (final UtilizationMetric um in utils?.mid ?? [])
        //   TableRow(
        //     decoration: BoxDecoration(
        //         border: Border(
        //       bottom: BorderSide(color: Palette.stroke),
        //     )),
        //     children: [
        //       _buildContentCell('${um.name}', textTheme, false, false),
        //       _buildContentCell('${um.quantity ?? 0}', textTheme),
        //       _buildContentCell('${um.limit ?? 0}', textTheme),
        //       _buildContentCell(
        //           '${summary?.mid?.containerUsage ?? 0}', textTheme),
        //       _buildContentCell('-', textTheme),
        //       _buildContentCell('-', textTheme),
        //     ],
        //   ),
        // for (final UtilizationMetric um in utils?.large ?? [])
        //   TableRow(
        //     decoration: BoxDecoration(
        //         border: Border(
        //       bottom: BorderSide(color: Palette.stroke),
        //     )),
        //     children: [
        //       _buildContentCell('${um.name}', textTheme, false, false),
        //       _buildContentCell('${um.quantity ?? 0}', textTheme),
        //       _buildContentCell('${um.limit ?? 0}', textTheme),
        //       _buildContentCell(
        //           '${summary?.large?.containerUsage ?? 0}', textTheme),
        //       _buildContentCell('-', textTheme),
        //       _buildContentCell('-', textTheme),
        //     ],
        //   ),
        TableRow(
          decoration: BoxDecoration(
              border: Border(
            bottom: BorderSide(color: Palette.stroke),
          )),
          children: [
            _buildContentCell('Total', textTheme, bold: true, centered: false),
            _buildContentCell(
                '${summary?.mid?.totalQuantity ?? summary?.large?.totalQuantity ?? 0}',
                textTheme,
                bold: true),
            _buildContentCell('-', textTheme, bold: true),
            _buildContentCell('-', textTheme, bold: true),
            _buildContentCell(
                '${summary?.mid?.containersRequired ?? 0}', textTheme,
                bold: true,
                limit: '${summary?.large?.containersRequired ?? 0}'),
            _buildContentCell(
                '${summary?.mid?.lastContainerUtilization ?? 0}%', textTheme,
                bold: true,
                limit: '${summary?.large?.lastContainerUtilization ?? 0}%'),
          ],
        ),
      ],
    );
  }

  // Widget _buildContentCell(dynamic content, TextTheme textTheme,
  //     {bool bold = false, bool centered = true, String? limit}) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
  //     child: limit == null ? content is String
  //         ? Text(
  //             content,
  //             style: textTheme.bodyMedium?.copyWith(
  //                 color: Palette.blackSecondary,
  //                 fontWeight: bold ? FontWeight.w700 : FontWeight.w400),
  //             textAlign: centered ? TextAlign.center : TextAlign.start,
  //             overflow: TextOverflow.ellipsis,
  //           )
  //         : content : ,
  //   );
  // }

  Widget _buildContentCell(dynamic content, TextTheme textTheme,
      {bool bold = false, bool centered = true, String? limit}) {
    final style = textTheme.bodyMedium?.copyWith(
        color: Palette.blackSecondary,
        fontWeight: bold ? FontWeight.w700 : FontWeight.w400);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (limit == null)
            content is String
                ? Align(
                    alignment:
                        centered ? Alignment.center : Alignment.centerLeft,
                    child: Text(
                      content,
                      style: style,
                      textAlign: centered ? TextAlign.center : TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )
                : content
          else ...[
            const Gap(10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                content is String
                    ? Align(
                        alignment:
                            centered ? Alignment.center : Alignment.centerLeft,
                        child: Text(
                          content,
                          style: style,
                          textAlign:
                              centered ? TextAlign.center : TextAlign.start,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    : content,
                const Gap(45),
                Text(
                  limit,
                  style: style,
                  textAlign: centered ? TextAlign.center : TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            )
          ],
        ],
      ),
    );
  }
}

class TruckWidget extends StatelessWidget {
  const TruckWidget(this.cUtil, this.isFirstIndex, {super.key});

  final ContainerUtilization cUtil;
  final bool isFirstIndex;

  @override
  Widget build(BuildContext context) {
    return Expandable(
      expanded: true,
      header: Text(cUtil.name.toString().toUpperCase()),
      content: Column(
        children: [
          _buildTableHeader(context),
          _buildTableRow(context, cUtil),
        ],
      ),
    );
  }

  Widget _buildTableHeader(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      // height: 60,
      padding: const EdgeInsets.symmetric(vertical: 5),
      // alignment: Alignment.bottomCenter,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
            top: BorderSide(color: Palette.stroke),
            bottom: BorderSide(color: Palette.stroke)),
      ),
      child: Table(
        defaultVerticalAlignment: TableCellVerticalAlignment.bottom,
        columnWidths: const {
          0: FlexColumnWidth(3),
          1: FlexColumnWidth(7),
          2: FlexColumnWidth(3),
          3: FlexColumnWidth(4),
          4: FlexColumnWidth(4),
          5: FlexColumnWidth(4),
          6: FlexColumnWidth(4),
        },
        children: [
          TableRow(
            children: [
              _buildHeaderCell('SKU', textTheme,
                  showLimit: false, centered: true),
              _buildHeaderCell('Description', textTheme,
                  showLimit: false, centered: false),
              _buildHeaderCell('Pallets', textTheme,
                  hintText: 'Your current cart quantity', showLimit: false),
              _buildHeaderCell('Loadability limits', textTheme,
                  hintText: 'Number of cases that fill a truck'),
              _buildHeaderCell('Truck usage', textTheme,
                  hintText: 'Amount of space used up in the truck'),
              _buildHeaderCell('Trucks required', textTheme,
                  hintText: 'Number of trucks needed'),
              _buildHeaderCell('Utilization of last truck', textTheme,
                  hintText: '% utilization of the last truck'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme,
      {String? hintText,
      bool centered = true,
      bool showLimit = false,
      Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment:
                centered ? MainAxisAlignment.center : MainAxisAlignment.start,
            children: [
              Text(
                text,
                style: color != null
                    ? textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w600, color: color)
                    : textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w600),
                textAlign: centered ? TextAlign.center : TextAlign.start,
              ),
              if (hintText != null)
                Tooltip(
                  message: hintText,
                  preferBelow: false,
                  child: SvgPicture.asset('$kSvgDir/order/info.svg'),
                ),
            ],
          ),
          if (showLimit) ...[
            const Gap(10),
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('20ft'),
                Gap(45),
                Text('40ft'),
              ],
            )
          ],
        ],
      ),
    );
  }

  Widget _buildTableRow(BuildContext context, ContainerUtilization cu) {
    final textTheme = Theme.of(context).textTheme;

    final utils = cu.utilization;
    final summary = cu.summary;

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(3),
        1: FlexColumnWidth(7),
        2: FlexColumnWidth(3),
        3: FlexColumnWidth(4),
        4: FlexColumnWidth(4),
        5: FlexColumnWidth(4),
        6: FlexColumnWidth(4),
      },
      children: [
        for (final UtilizationMetric um in utils?.truck ?? [])
          TableRow(
            decoration: BoxDecoration(
                border: Border(
              bottom: BorderSide(color: Palette.stroke),
            )),
            children: [
              _buildContentCell('${um.code}', textTheme, bold: false),
              _buildContentCell('${um.name}', textTheme,
                  bold: false, centered: false),
              _buildContentCell('${um.quantity ?? 0}', textTheme, bold: false),
              _buildContentCell('${um.limit ?? 0}', textTheme, bold: false),
              _buildContentCell('${summary?.truck?.truckUsage ?? 0}', textTheme,
                  bold: false),
              _buildContentCell('-', textTheme, bold: false),
              _buildContentCell('-', textTheme, bold: false),
            ],
          ),
        TableRow(
          decoration: BoxDecoration(
              border: Border(
            bottom: BorderSide(color: Palette.stroke),
          )),
          children: [
            _buildContentCell('-', textTheme),
            _buildContentCell('Total', textTheme, centered: false),
            _buildContentCell(
                '${summary?.truck?.totalQuantity ?? 0}', textTheme),
            _buildContentCell('-', textTheme),
            _buildContentCell('${summary?.truck?.truckUsage ?? 0}', textTheme),
            _buildContentCell(
                '${summary?.truck?.trucksRequired ?? 0}', textTheme),
            _buildContentCell(
                '${summary?.truck?.lastTruckUtilization ?? 0}%', textTheme,
                color: Colors.green),
          ],
        ),
      ],
    );
  }

  Widget _buildContentCell(dynamic content, TextTheme textTheme,
      {bool bold = true, bool centered = true, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: content is String
          ? Text(
              content,
              style: textTheme.bodyMedium?.copyWith(
                  color: color ?? Palette.blackSecondary,
                  fontWeight: bold ? FontWeight.w700 : FontWeight.w400),
              textAlign: centered ? TextAlign.center : TextAlign.start,
              overflow: TextOverflow.ellipsis,
            )
          : content,
    );
  }
}
