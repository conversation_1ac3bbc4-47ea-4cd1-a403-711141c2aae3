import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/widgets/grid_item_quantity_picker.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class VariantQuantityPicker extends ConsumerStatefulWidget {
  const VariantQuantityPicker(
    this.variant, {
    super.key,
    this.useEditingCart = true,
    this.showInitialAddBtn = true,
    this.title,
    this.contentPadding,
    this.cursorHeight,
    this.width,
    this.height,
    this.ignoreQuantityLimits = false,
  });

  final Variant variant;
  final bool useEditingCart;
  final bool showInitialAddBtn;
  final String? title;
  final EdgeInsetsGeometry? contentPadding;
  final double? cursorHeight;
  final double? width;
  final double? height;
  final bool ignoreQuantityLimits;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _VariantQuantityPickerState();
}

class _VariantQuantityPickerState extends ConsumerState<VariantQuantityPicker> {
  num quantity = 0;
  late bool showQuantityInput;

  bool get isExportCountry =>
      ref.read(countryTypeProvider) == CountryType.export;

  num get maxQuantity => widget.ignoreQuantityLimits
      ? defaultItemQuantity
      : isExportCountry
          ? defaultItemQuantity
          : widget.variant.extVariant?.available ?? 0;

  @override
  void initState() {
    super.initState();
    initializeQuantity();
  }

  void initializeQuantity() {
    final cartItem = ref
            .read(cartProvider.notifier)
            .item(widget.variant.variantSupplierId) ??
        ref.read(editingCartProvider).firstWhereOrNull((item) =>
            item.variant.variantSupplierId == widget.variant.variantSupplierId);

    quantity = cartItem?.count ?? 0;
    showQuantityInput = !widget.showInitialAddBtn || quantity > 0;
  }

  @override
  Widget build(BuildContext context) {
    final variant = widget.variant;
    final existingItem = ref
            .watch(cartItemProvider(widget.variant.variantSupplierId)) ??
        ref.watch(editingCartItemProvider(widget.variant.variantSupplierId));

    if (isExportCountry) {
      ref.listen(
        cartItemProvider(widget.variant.variantSupplierId),
        (prev, next) {
          if (prev?.count != next?.count) {
            setState(() {
              quantity = next?.count ?? 0;
              showQuantityInput = quantity > 0;
            });
          }
        },
      );

      ref.listen(
        editingCartItemProvider(widget.variant.variantSupplierId),
        (prev, next) {
          if (prev?.count != next?.count) {
            setState(() {
              quantity = next?.count ?? 0;
              showQuantityInput = quantity > 0;
            });
          }
        },
      );
    }

    return (widget.width != null || widget.height != null)
        ? ConstrainedBox(
            constraints: BoxConstraints.loose(
                Size(widget.width ?? 120, widget.height ?? 38)),
            child: buildChild(context, existingItem, variant),
          )
        : buildChild(context, existingItem, variant);
  }

  Widget buildChild(
      BuildContext context, CartItem? existingItem, Variant variant) {
    return showQuantityInput
        ? buildQuantityPicker(context, existingItem, variant)
        : buildAddButton(context);
  }

  Widget buildAddButton(BuildContext context) {
    return SizedBox(
      height: 26,
      width: widget.title != null ? null : 54,
      child: FilledButton(
        onPressed: () {
          setState(() {
            quantity = 1;
            showQuantityInput = true;
          });

          addToCart();
        },
        child: Text(
          widget.title ?? 'Add',
          style: Theme.of(context)
              .textTheme
              .bodySmall
              ?.copyWith(color: Colors.white),
        ),
      ),
    );
  }

  Widget buildQuantityPicker(
      BuildContext context, CartItem? existingItem, Variant variant) {
    final count = existingItem?.count ?? 0;
    final newQuantity = count != 0 ? count : quantity;
    return TapRegion(
      onTapOutside: (_) {
        if (quantity == 0 && widget.showInitialAddBtn) {
          setState(() {
            showQuantityInput = false;
          });
        }
      },
      child: GridItemQuantityPicker(
        newQuantity,
        maxQuantity,
        focusBorder: false,
        contentPadding: widget.contentPadding,
        cursorHeight: widget.cursorHeight,
        onQuantityChanged: (newQuantity) {
          updateQuantity(
              num.tryParse(newQuantity.toString()) ?? 0, existingItem, variant);
        },
      ),
    );
  }

  void updateQuantity(
      num newQuantity, CartItem? existingItem, Variant variant) {
    setState(() {
      quantity = newQuantity;
    });
    final cartItem = existingItem ?? CartItem(count: 0, variant: variant);
    updateCartItem(cartItem.copyWith(count: newQuantity));

    if (existingItem == null && !widget.useEditingCart) {
      showCartToast();
    }
  }

  void addToCart() {
    final variantSupplierId = widget.variant.variantSupplierId;
    if (widget.useEditingCart) {
      ref.read(editingCartProvider.notifier).update((state) {
        final count = state
                .firstWhereOrNull(
                  (item) => item.variant.variantSupplierId == variantSupplierId,
                )
                ?.count ??
            0;
        return [
          ...state,
          CartItem(
            count: count + 1,
            variant: widget.variant,
          ),
        ];
      });
    } else {
      ref.read(cartProvider.notifier).addItem(
            CartItem(
              count:
                  (ref.read(cartItemProvider(variantSupplierId))?.count ?? 0) +
                      1,
              variant: widget.variant,
            ),
          );
      showCartToast();
    }
  }

  void showCartToast() {
    Toast.success(
      "You have a pending order with ${ref.read(cartProvider).uniqueCartItems.length} items",
      context,
      title: 'Pending Order',
      action: CustomAction(
        'Proceed to Order',
        (ctx) {
          if (ctx.mounted) {
            ctx.pushNamed(kCreateOrderRoute);
          }
        },
      ),
    );
  }

  void updateCartItem(CartItem updatedItem) {
    if (widget.useEditingCart) {
      ref.read(editingCartProvider.notifier).update((state) {
        final updatedList = state
            .map((item) => item.variant.variantSupplierId ==
                    updatedItem.variant.variantSupplierId
                ? updatedItem
                : item)
            .toList();

        final exists = state.any((item) =>
            item.variant.variantSupplierId ==
            updatedItem.variant.variantSupplierId);
        if (!exists) {
          updatedList.add(updatedItem);
        }

        return updatedList;
      });
    } else {
      ref.read(cartProvider.notifier).addItem(updatedItem);
    }
  }
}

// use to ensure UI rebuilds for a specific cart item
final cartItemProvider =
    Provider.family<CartItem?, String>((ref, variantSupplierId) {
  final cartState = ref.watch(cartProvider);
  return cartState.uniqueCartItemsWithNonZeroCount.firstWhereOrNull(
    (item) => item.variant.variantSupplierId == variantSupplierId,
  );
});

// use to ensure UI rebuilds for a specific cart item
final editingCartItemProvider =
    Provider.family<CartItem?, String>((ref, variantSupplierId) {
  final editingCartItems = ref.watch(editingCartProvider);
  return editingCartItems.firstWhereOrNull(
    (item) => item.variant.variantSupplierId == variantSupplierId,
  );
});

// class VariantQuantityPicker extends ConsumerStatefulWidget {
//   const VariantQuantityPicker(
//     this.variant, {
//     super.key,
//     this.useEditingCart = true,
//     this.showInitialAddBtn = true,
//     this.title,
//     this.contentPadding,
//     this.cursorHeight,
//     this.width,
//     this.height,
//     this.ignoreQuantityLimits = false,
//   });

//   final Variant variant;

//   /// If `useEditingCart` is `true`, items are added to a temporary editing cart
//   /// instead of the main cart. This enables users to review items before moving them
//   /// to the main cart.
//   final bool useEditingCart;

//   /// Controls whether an initial add btn is displayed.
//   final bool showInitialAddBtn;

//   final String? title;
//   final EdgeInsetsGeometry? contentPadding;
//   final double? cursorHeight;
//   final double? width;
//   final double? height;

//   /// If `ignoreQuantityLimits` is `true`, the `maxQuantity` will be set to
//   /// [defaultItemQuantity] instead of the actual quantity available for the item.
//   final bool ignoreQuantityLimits;

//   @override
//   ConsumerState<ConsumerStatefulWidget> createState() =>
//       _VariantQuantityPickerState();
// }

// class _VariantQuantityPickerState extends ConsumerState<VariantQuantityPicker> {
//   num quantity = 0;
//   late bool showQuantityInput;

//   bool get isExportCountry =>
//       ref.read(countryTypeProvider) == CountryType.export;

//   num get maxQuantity => widget.ignoreQuantityLimits
//       ? defaultItemQuantity
//       : isExportCountry
//           ? defaultItemQuantity
//           : widget.variant.extVariant?.available ?? 0;

//   @override
//   void initState() {
//     super.initState();

//     final cartItem = ref.read(editingCartProvider).firstWhereOrNull((item) =>
//             item.variant.variantSupplierId ==
//             widget.variant.variantSupplierId) ??
//         ref.read(cartProvider.notifier).item(widget.variant.variantSupplierId);

//     quantity = cartItem?.count ?? 0;
//     showQuantityInput = !widget.showInitialAddBtn || quantity > 0;
//   }

//   @override
//   Widget build(BuildContext context) {
//     final variant = widget.variant;

//     final existingItem = ref
//             .watch(cartItemProvider(widget.variant.variantSupplierId)) ??
//         ref.watch(editingCartItemProvider(widget.variant.variantSupplierId));

//     final child = !showQuantityInput
//         ? SizedBox(
//             height: 26,
//             width: widget.title != null ? null : 54,
//             child: FilledButton(
//               onPressed: () {
//                 setState(() {
//                   quantity = 1;
//                   addToCart();

//                   if (!showQuantityInput) {
//                     showQuantityInput = true;
//                   }
//                 });
//               },
//               child: Text(
//                 widget.title ?? 'Add',
//                 style: Theme.of(context)
//                     .textTheme
//                     .bodySmall
//                     ?.copyWith(color: Colors.white),
//               ),
//             ),
//           )
//         : TapRegion(
//             onTapOutside: (_) {
//               if (quantity == 0) {
//                 setState(() {
//                   showQuantityInput = false;
//                 });
//               }
//             },
//             child: GridItemQuantityPicker(
//               // key: UniqueKey(),
//               quantity,
//               maxQuantity,
//               focusBorder: false,
//               // focusNode: focusNode,
//               contentPadding: widget.contentPadding,
//               cursorHeight: widget.cursorHeight,
//               onQuantityChanged: (newQuantity) {
//                 setState(() {
//                   quantity = newQuantity;
//                   final cartItem =
//                       existingItem ?? CartItem(count: 0, variant: variant);
//                   updateCartItem(cartItem.copyWith(count: quantity));
//                 });

//                 if (existingItem == null && !widget.useEditingCart) {
//                   showCartToast();
//                 }
//               },
//             ),
//           );

//     return (widget.width != null || widget.height != null)
//         ? ConstrainedBox(
//             constraints: BoxConstraints.loose(
//                 Size(widget.width ?? 120, widget.height ?? 38)),
//             child: child,
//           )
//         : child;
//   }

//   void addToCart() {
//     if (widget.useEditingCart) {
//       ref.read(editingCartProvider.notifier).update((state) => [
//             ...state,
//             CartItem(
//               count: (ref
//                           .read(editingCartItemProvider(
//                               widget.variant.variantSupplierId))
//                           ?.count ??
//                       0) +
//                   1,
//               variant: widget.variant,
//             ),
//           ]);
//     } else {
//       ref.read(cartProvider.notifier).addItem(
//             CartItem(
//               count: (ref
//                           .read(cartItemProvider(
//                               widget.variant.variantSupplierId))
//                           ?.count ??
//                       0) +
//                   1,
//               variant: widget.variant,
//             ),
//           );
//       showCartToast();
//     }
//   }

//   void showCartToast() {
//     Toast.success(
//       "${ref.read(cartProvider).uniqueCartItemsWithNonZeroCount.length} items in cart",
//       context,
//       title: 'Your Cart',
//       action: CustomAction(
//         'Create Order',
//         (ctx) {
//           if (ctx.mounted) {
//             ctx.pushNamed(kCreateOrderRoute);
//           }
//         },
//       ),
//     );
//   }

//   /// Removes the current item from either the editing or main cart.
//   void removeFromCart() {
//     if (widget.useEditingCart) {
//       ref.read(editingCartProvider.notifier).update((state) => state
//           .where((item) =>
//               item.variant.variantSupplierId !=
//               widget.variant.variantSupplierId)
//           .toList());
//     } else {
//       ref
//           .read(cartProvider.notifier)
//           .removeItem(widget.variant.variantSupplierId);
//     }
//   }

//   /// Updates an existing item in either the editing or main cart with a new quantity.
//   void updateCartItem(CartItem updatedItem) {
//     if (widget.useEditingCart) {
//       ref.read(editingCartProvider.notifier).update((state) {
//         final updatedList = state
//             .map((item) => item.variant.variantSupplierId ==
//                     updatedItem.variant.variantSupplierId
//                 ? updatedItem
//                 : item)
//             .toList();

//         // Check if the item was updated; if not, add it to the list.
//         final exists = state.any((item) =>
//             item.variant.variantSupplierId ==
//             updatedItem.variant.variantSupplierId);
//         if (!exists) {
//           updatedList.add(updatedItem);
//         }

//         return updatedList;
//       });
//     } else {
//       ref.read(cartProvider.notifier).addItem(updatedItem);
//     }
//   }
// }

// /// use to ensure UI rebuilds for a specific cart item
// final cartItemProvider =
//     Provider.family<CartItem?, String>((ref, variantSupplierId) {
//   final cartState = ref.watch(cartProvider);
//   return cartState.cartItems.firstWhereOrNull(
//     (item) => item.variant.variantSupplierId == variantSupplierId,
//   );
// });

// /// use to ensure UI rebuilds for a specific cart item
// final editingCartItemProvider =
//     Provider.family<CartItem?, String>((ref, variantSupplierId) {
//   final editingCartItems = ref.watch(editingCartProvider);
//   return editingCartItems.firstWhereOrNull(
//     (item) => item.variant.variantSupplierId == variantSupplierId,
//   );
// });
