import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:td_procurement/app/order/driver_notifier.dart';
import 'package:td_procurement/app/order/options_provider.dart';
import 'package:td_procurement/app/order/order_controller.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class OrderPagination extends ConsumerWidget {
  final int? total;
  const OrderPagination({super.key, this.total});

  @override
  Widget build(BuildContext context, ref) {
    final orderState = ref.watch(orderControllerProvider);
    final params = orderState.fetchTransactionsParam;
    final perPage = params.perPage;
    final start = ((params.currentPage - 1) * perPage) + 1;
    final end = params.currentPage * perPage;
    final currentPageData = orderState.transactions.maybeWhen(
      data: (data) => data,
      orElse: () => [],
    );
    final totalCount =
        params.totalCount > 0 ? params.totalCount : currentPageData.length;

    return Padding(
      padding: const EdgeInsets.only(left: 35, right: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$start - ${end > totalCount ? totalCount : end} of $totalCount',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Palette.blackSecondary),
          ),
          if (params.totalPages > 0)
            IntrinsicWidth(
              child: NumberPagination(
                onPageChanged: (int pageNumber) {
                  ref.read(orderControllerProvider.notifier).fetchTransactions(
                      params.copyWith(currentPage: pageNumber),
                      forced: true);
                },
                visiblePagesCount: perPage > 5 || perPage == 0 ? 5 : perPage,
                buttonElevation: 0.5,
                totalPages: params.totalPages,
                currentPage: params.currentPage,
                buttonRadius: 6,
                selectedButtonColor: Palette.primaryBlack,
                selectedNumberColor: Colors.white,
                unSelectedButtonColor: Colors.white,
                unSelectedNumberColor: Palette.blackSecondary,
                fontSize: 14,
                numberButtonSize: const Size(35, 35),
                controlButtonSize: const Size(40, 40),
                firstPageIcon: SvgPicture.asset(kDoubleChevronLeftSvg,
                    width: 25, height: 25),
                previousPageIcon: SvgPicture.asset(kChevronLeftSvg,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn),
                    width: 16,
                    height: 16),
                lastPageIcon: SvgPicture.asset(kDoubleChevronRightSvg,
                    width: 25, height: 25),
                nextPageIcon: SvgPicture.asset(kChevronRightSvg,
                    width: 16,
                    height: 16,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn)),
              ),
            ),
        ],
      ),
    );
  }
}

class SalesOrderPagination extends ConsumerWidget {
  final int? total;
  const SalesOrderPagination({super.key, this.total});

  @override
  Widget build(BuildContext context, ref) {
    final orderState = ref.watch(orderControllerProvider);
    final params = orderState.fetchSalesOrdersParams;
    final perPage = params.perPage;
    final start = ((params.currentPage - 1) * perPage) + 1;
    final end = params.currentPage * perPage;
    final currentPageData = orderState.salesOrders.maybeWhen(
      data: (data) => data,
      orElse: () => [],
    );
    final totalCount =
        params.totalCount > 0 ? params.totalCount : currentPageData.length;

    return Padding(
      padding: const EdgeInsets.only(left: 38, right: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$start - ${end > totalCount ? totalCount : end} of $totalCount',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Palette.blackSecondary),
          ),
          if (params.totalPages > 0)
            IntrinsicWidth(
              child: NumberPagination(
                onPageChanged: (int pageNumber) {
                  ref
                      .read(optionsProvider.notifier)
                      .clearList(); // clears out the selected orders
                  ref
                      .read(driverProvider.notifier)
                      .removeDriver(); // removes the selected driver
                  ref.read(orderControllerProvider.notifier).fetchSalesOrders(
                      params.copyWith(currentPage: pageNumber),
                      forced: true);
                },
                visiblePagesCount: perPage > 5 || perPage == 0 ? 5 : perPage,
                buttonElevation: 0.5,
                totalPages: params.totalPages,
                currentPage: params.currentPage,
                buttonRadius: 6,
                selectedButtonColor: Palette.primaryBlack,
                selectedNumberColor: Colors.white,
                unSelectedButtonColor: Colors.white,
                unSelectedNumberColor: Palette.blackSecondary,
                fontSize: 14,
                numberButtonSize: const Size(35, 35),
                controlButtonSize: const Size(40, 40),
                firstPageIcon: SvgPicture.asset(kDoubleChevronLeftSvg,
                    width: 25, height: 25),
                previousPageIcon: SvgPicture.asset(kChevronLeftSvg,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn),
                    width: 16,
                    height: 16),
                lastPageIcon: SvgPicture.asset(kDoubleChevronRightSvg,
                    width: 25, height: 25),
                nextPageIcon: SvgPicture.asset(kChevronRightSvg,
                    width: 16,
                    height: 16,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn)),
              ),
            ),
        ],
      ),
    );
  }
}
