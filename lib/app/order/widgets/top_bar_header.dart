import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class TopBarHeader extends ConsumerWidget {
  const TopBarHeader({super.key, this.title = 'Products', this.saveBtn = true});

  final String title;
  final bool saveBtn;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Palette.stroke),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              children: [
                Flexible(
                  child: Icon<PERSON>utton(
                    icon: Skeleton.replace(
                      child: SvgPicture.asset('$kSvgDir/order/close.svg'),
                    ),
                    onPressed: () {
                      context.pop();
                      ref.read(editingCartProvider).clear();
                    },
                  ),
                ),
                const Gap(10),
                Flexible(
                  child: Text(
                    title,
                    style:
                        textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
                  ),
                ),
              ],
            ),
          ),
          if (saveBtn)
            Flexible(
              child: ElevatedButton(
                onPressed: () {
                  final editedItems = ref.read(editingCartProvider);

                  ref.read(cartProvider.notifier).addItems(editedItems);
                  ref.read(editingCartProvider).clear();

                  context.pop();
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  foregroundColor: Colors.white,
                  backgroundColor: Palette.primaryBlack,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Save'),
              ),
            )
        ],
      ),
    );
  }
}
