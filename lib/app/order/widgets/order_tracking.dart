import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:timelines_plus/timelines_plus.dart';

class OrderTrackingWidget extends ConsumerStatefulWidget {
  const OrderTrackingWidget(
    this.orderDetails, {
    super.key,
    this.isLoading = false,
  });

  final OrderDetails? orderDetails;
  final bool isLoading;

  @override
  ConsumerState<OrderTrackingWidget> createState() =>
      _OrderTrackingWidgetState();
}

class _OrderTrackingWidgetState extends ConsumerState<OrderTrackingWidget> {
  List<ExportOrderTracking> _exportOrderTrackings = [];

  bool get isExportOrder =>
      widget.orderDetails?.transaction.isExportOrder ?? false;

  late OrderTracking tracking;

  @override
  void initState() {
    tracking = _getOrderTracking();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      height: 552,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Palette.stroke, width: 1),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 2,
            spreadRadius: -1,
          ),
        ],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order tracking',
            style:
                textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          const Gap(32),
          Expanded(
            child: FixedTimeline.tileBuilder(
              theme: TimelineThemeData(
                nodePosition: 0,
                indicatorPosition: 0,
                nodeItemOverlap: false,
                connectorTheme: ConnectorThemeData(
                  thickness: 1,
                  color: Palette.stroke,
                ),
                indicatorTheme: IndicatorThemeData(
                  position: 0.3,
                  size: 10.0,
                  color: Palette.stroke,
                ),
              ),
              builder: TimelineTileBuilder(
                itemCount: 4,
                contentsBuilder: (_, index) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTrackingContent(context, index, tracking),
                      if (index < 4)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Divider(
                            thickness: 1,
                            color: Palette.stroke,
                          ),
                        ),
                    ],
                  );
                },
                indicatorBuilder: (context, index) {
                  final isActive = [
                    tracking.isOrderPlaced,
                    tracking.isOrderConfirmed,
                    tracking.isOrderOutForDelivery,
                    tracking.isOrderDelivered,
                  ][index];
                  return Skeleton.shade(
                    child: SvgPicture.asset(
                      '$kSvgDir/shipment/circle.svg',
                      colorFilter: isActive
                          ? const ColorFilter.mode(
                              Colors.green, BlendMode.srcIn)
                          : const ColorFilter.mode(
                              Colors.orange, BlendMode.srcIn),
                    ),
                  );
                },
                endConnectorBuilder: (_, __) {
                  return SolidLineConnector(
                    color: Palette.stroke,
                  );
                },
              ),
            ),

            // FixedTimeline.tileBuilder(
            //   theme: TimelineThemeData(
            //     nodePosition: 0,
            //     indicatorPosition: 0,
            //     connectorTheme: ConnectorThemeData(
            //       thickness: 2,
            //       color: Palette.stroke,
            //     ),
            //     indicatorTheme: IndicatorThemeData(
            //       position: 0.3,
            //       size: 10.0,
            //       color: Palette.stroke,
            //     ),
            //   ),
            //   builder: TimelineTileBuilder.connected(
            //     connectionDirection: ConnectionDirection.after,
            //     itemCount: 4,
            //     itemExtent: 110,
            //     contentsBuilder: (_, index) {
            //       return _buildTrackingContent(context, index, tracking);
            //     },
            //     indicatorBuilder: (context, index) {
            //       final isActive = [
            //         tracking.isOrderPlaced,
            //         tracking.isOrderConfirmed,
            //         tracking.isOrderOutForDelivery,
            //         tracking.isOrderDelivered,
            //       ][index];
            //       return DotIndicator(
            //         color: isActive ? Palette.primaryBlack : Palette.stroke,
            //       );
            //     },
            //     connectorBuilder: (context, index, connectorType) {
            //       final isActive = [
            //         tracking.isOrderPlaced,
            //         tracking.isOrderConfirmed,
            //         tracking.isOrderOutForDelivery,
            //         tracking.isOrderDelivered,
            //       ][index];
            //       return SolidLineConnector(
            //         color: isActive ? Palette.primaryBlack : Palette.stroke,
            //       );
            //     },
            //   ),
            // ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingContent(
      BuildContext context, int index, OrderTracking tracking) {
    final textTheme = Theme.of(context).textTheme;
    final orders = widget.orderDetails?.orders ?? [];

    switch (index) {
      case 0:
        return buildTrackingStep(
          context,
          'Order Placed',
          tracking.orderPlacedDate?.toFullDateTime() ?? '',
          isActive: tracking.isOrderPlaced,
          isLast: !tracking.isOrderConfirmed,
        );
      case 1:
        return buildTrackingStep(
          context,
          'Order Confirmed',
          tracking.orderConfirmedDate?.toFullDateTime() ?? '',
          isActive: tracking.isOrderConfirmed,
          isLast: !tracking.isOrderOutForDelivery,
        );
      case 2:
        return buildTrackingStep(
          context,
          'Out for Delivery',
          tracking.orderOutForDeliveryDate?.toFullDateTime() ?? '',
          extraContent: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (orders.isNotEmpty &&
                  tracking.isOrderOutForDelivery &&
                  orders.first.dispatchUser != null) ...[
                const Gap(10),
                Text(
                  'Driver name',
                  style: textTheme.bodyLarge?.copyWith(
                    color: Palette.blackSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  orders.first.dispatchUser!.name!,
                  style: textTheme.bodyLarge,
                ),
              ],
            ],
          ),
          isActive: tracking.isOrderOutForDelivery,
          isLast: tracking.isOrderDelivered,
        );
      case 3:
        return buildTrackingStep(
          context,
          'Delivered',
          tracking.orderDeliveredDate?.toFullDateTime() ?? '',
          isActive: tracking.isOrderDelivered,
          isLast: true,
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget buildTrackingStep(
    BuildContext context,
    String status,
    String dateTime, {
    bool isActive = false,
    bool isLast = false,
    Widget? extraContent,
  }) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            status,
            style: textTheme.labelMedium?.copyWith(fontSize: 16),
          ),
          const Gap(4),
          Text(
            dateTime,
            style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
                fontWeight: FontWeight.w400,
                fontSize: 12),
          ),
          if (extraContent != null) ...[
            const Gap(4),
            extraContent,
          ],
        ],
      ),
    );
  }

  OrderTracking _getOrderTracking() {
    final orders = widget.orderDetails?.orders ?? [];

    if (orders.isEmpty) {
      return const OrderTracking(
        isOrderPlaced: true,
        isOrderConfirmed: false,
        isOrderOutForDelivery: false,
        isOrderDelivered: false,
      );
    }

    if (isExportOrder) {
      if (_exportOrderTrackings.isEmpty) {
        Future.microtask(() => getTrackingDetailsById());
        // unawaited(getTrackingDetailsById());
        return const OrderTracking(
          isOrderPlaced: false,
          isOrderConfirmed: false,
          isOrderOutForDelivery: false,
          isOrderDelivered: false,
        );
      }

      final orderPlaced = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "order placed",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );
      final orderScheduled = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "scheduled",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );
      final orderOutForDelivery = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "out for delivery",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );
      final orderDelivered = _exportOrderTrackings.firstWhere(
        (el) => el.status.toLowerCase() == "delivered",
        orElse: () => ExportOrderTracking(status: "unknown"),
      );

      return OrderTracking(
        isOrderPlaced: orderPlaced.status != "unknown",
        orderPlacedDate: orderPlaced.createdAt,
        isOrderConfirmed: orderScheduled.status != "unknown",
        orderConfirmedDate: orderScheduled.createdAt,
        isOrderOutForDelivery: orderOutForDelivery.status != "unknown",
        orderOutForDeliveryDate: orderOutForDelivery.createdAt,
        isOrderDelivered: orderDelivered.status != "unknown",
        orderDeliveredDate: orderDelivered.createdAt,
      );
    }

    // Fallback for non-UK orders
    return nonExportOrderTracking();
  }

  OrderTracking nonExportOrderTracking() {
    final orders = widget.orderDetails?.orders ?? [];
    if (orders.isEmpty) {
      return const OrderTracking(
        isOrderPlaced: false,
        isOrderConfirmed: false,
        isOrderOutForDelivery: false,
        isOrderDelivered: false,
      );
    }

    final order = orders.first;
    final autoApprovalEvent = (order.history ?? [])
        .where((event) => event.event == "AUTO_APPROVAL")
        .firstOrNull;

    final shippingStatus = (order.shippingStatus ?? '').toLowerCase();
    final isOrderDelivered = ["delivered", "shipped"].contains(shippingStatus);
    final isOrderOutForDelivery =
        ["dispatched"].contains(shippingStatus) || isOrderDelivered;
    final isOrderConfirmed = order.approvalStatus == 'approved' ||
        autoApprovalEvent?.createdAt != null ||
        isOrderOutForDelivery;
    final isOrderPlaced =
        ["pending", "cancelled"].contains(shippingStatus) || isOrderConfirmed;

    return OrderTracking(
      isOrderPlaced: isOrderPlaced,
      orderPlacedDate: isOrderPlaced ? order.createdAt : null,
      isOrderConfirmed: isOrderConfirmed,
      orderConfirmedDate: isOrderConfirmed
          ? (order.approvals.isNotEmpty
                  ? order.approvals.first.approvedAt
                  : null) ??
              autoApprovalEvent?.createdAt
          : null,
      isOrderOutForDelivery: isOrderOutForDelivery,
      orderOutForDeliveryDate:
          isOrderOutForDelivery ? order.dispatchedAt ?? order.shippedAt : null,
      isOrderDelivered: isOrderDelivered,
      orderDeliveredDate: isOrderDelivered ? order.deliveredAt : null,
    );
  }

  Future<void> getTrackingDetailsById() async {
    final shipment = widget.orderDetails?.shipment;

    if (shipment == null || shipment.id.isEmpty) {
      final tracking = OrderTracking(
        isOrderPlaced: !widget.isLoading,
        orderPlacedDate: widget.orderDetails?.transaction.createdAt,
        isOrderConfirmed: false,
        isOrderOutForDelivery: false,
        isOrderDelivered: false,
      );

      setState(() {
        this.tracking = tracking;
      });

      return;
    }

    final res = await ref.read(fetchShipmentUseCaseProvider).call(shipment.id);
    res.when(
      success: (data) {
        if (mounted) {
          final exportTracking =
              processExportOrderTracking(data.exportOrderTracking);
          setState(() {
            tracking = exportTracking;
            _exportOrderTrackings = data.exportOrderTracking;
          });
        }
      },
      failure: (error, _) {
        Toast.apiError(error, context);
      },
    );
  }

  OrderTracking processExportOrderTracking(
    List<ExportOrderTracking> exportOrderTrackings,
  ) {
    final orderPlaced = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "order placed",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );
    final orderScheduled = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "scheduled",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );
    final orderOutForDelivery = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "out for delivery",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );
    final orderDelivered = exportOrderTrackings.firstWhere(
      (el) => el.status.toLowerCase() == "delivered",
      orElse: () => ExportOrderTracking(status: "unknown"),
    );

    return OrderTracking(
      isOrderPlaced: orderPlaced.status != "unknown",
      orderPlacedDate: orderPlaced.createdAt,
      isOrderConfirmed: orderScheduled.status != "unknown",
      orderConfirmedDate: orderScheduled.createdAt,
      isOrderOutForDelivery: orderOutForDelivery.status != "unknown",
      orderOutForDeliveryDate: orderOutForDelivery.createdAt,
      isOrderDelivered: orderDelivered.status != "unknown",
      orderDeliveredDate: orderDelivered.createdAt,
    );
  }
}
