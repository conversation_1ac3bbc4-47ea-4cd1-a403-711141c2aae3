import 'package:flutter/material.dart' hide SearchBar;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/widgets/index.dart';

class ProductsWidget extends ConsumerStatefulWidget {
  const ProductsWidget(this.variants, this.category, {super.key});

  final List<Variant> variants;
  final String category;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ProductsWidgetState();
}

class _ProductsWidgetState extends ConsumerState<ProductsWidget> {
  final _controller = TextEditingController();

  int activeIndex = -1;
  String selectedCategory = 'All';
  String? searchTerm;

  @override
  void initState() {
    _controller.addListener(_onTextChanged);
    super.initState();
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    setState(() {
      searchTerm = text.isEmpty ? null : text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final variants = widget.variants;

    return Scaffold(
      body: Column(
        children: [
          ProductsTopBarWidget(
            _controller,
            title: widget.category,
            activeIndex: activeIndex,
            selectedCategory: selectedCategory,
            categories: getCategories(variants),
            onIndexChanged: (index) {
              setState(() {
                activeIndex = index;
              });
            },
            onCategoryChanged: (category) {
              setState(() {
                selectedCategory = category;
              });
            },
          ),
          Expanded(
            child: CustomScrollView(
              slivers: [
                const SliverToBoxAdapter(
                  child: Gap(20),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.category,
                          style: textTheme.headlineSmall,
                        ),
                        const Gap(10),
                      ],
                    ),
                  ),
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  sliver: SliverGrid(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3, // Number of columns
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio: 1.2,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        return VariantGridItem(
                            getProducts(
                                variants, selectedCategory, searchTerm)[index],
                            key: UniqueKey());
                      },
                      childCount:
                          getProducts(variants, selectedCategory, searchTerm)
                              .length, // Number of items in the first grid
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Variant> getProducts(
      List<Variant> variants, String category, String? searchTerm) {
    final pm = ProductsManager(variants);
    return pm.getItemsByCategory(category, searchTerm);
  }

  List<String> getCategories(List<Variant> variants) {
    final pm = ProductsManager(variants);
    return pm.getCategoryFilter();
  }
}

class ProductsTopBarWidget extends ConsumerWidget {
  const ProductsTopBarWidget(
    this.controller, {
    super.key,
    required this.onIndexChanged,
    required this.activeIndex,
    required this.selectedCategory,
    required this.onCategoryChanged,
    required this.categories,
    required this.title,
  });

  final TextEditingController controller;

  final int activeIndex;
  final Function(int) onIndexChanged;
  final String selectedCategory;
  final Function(String) onCategoryChanged;
  final List<String> categories;
  final String title;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: 200,
      child: Column(
        children: [
          TopBarHeader(title: title, saveBtn: true),
          Container(
            height: 70,
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 40),
            alignment: Alignment.center,
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: SearchBar(controller: controller),
                ),
                const Gap(10),
                Flexible(
                  flex: 1,
                  child: CategoryFilterDropdown(
                    categoryFilter: categories,
                    selectedCategory: selectedCategory,
                    onCategoryChanged: onCategoryChanged,
                    onIndexChanged: onIndexChanged,
                    loading: false,
                  ),
                ),
              ],
            ),
          ),
          CategorySelectionBar(
            categoryOptions: categories,
            activeIndex: activeIndex,
            onIndexChanged: onIndexChanged,
            onCategoryChanged: onCategoryChanged,
            loading: false,
          ),
        ],
      ),
    );
  }
}
