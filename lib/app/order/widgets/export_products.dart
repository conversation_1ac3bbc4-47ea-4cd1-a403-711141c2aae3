import 'package:flutter/material.dart' hide SearchBar;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_controller.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/screens/products_screen.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class ExportProductsWidget extends ConsumerStatefulWidget {
  const ExportProductsWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ExportProductsWidgetState();
}

class _ExportProductsWidgetState extends ConsumerState<ExportProductsWidget> {
  final _controller = TextEditingController();

  int activeIndex = -1;
  String selectedCategory = 'All';
  String? searchTerm;

  @override
  void initState() {
    _controller.addListener(_onTextChanged);
    super.initState();
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    setState(() {
      searchTerm = text.isEmpty ? null : text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final orderState = ref.watch(orderControllerProvider);
    final showingMoreProducts =
        ref.watch(cartProvider).showingExclusiveProducts;
    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    return Scaffold(
      body: Column(
        children: [
          TopBarWidget(
            _controller,
            activeIndex: activeIndex,
            selectedCategory: selectedCategory,
            onIndexChanged: (index) {
              setState(() {
                activeIndex = index;
              });
            },
            onCategoryChanged: (category) {
              setState(() {
                selectedCategory = category;
              });
            },
            variants: isExportCountry
                ? orderState.exportVariants
                : orderState.outletVariants,
            moreProductVariants: orderState.variantsInCollections,
            showingMoreProducts: showingMoreProducts,
          ),
          Expanded(
            child: orderState.exportVariants.when(
              data: (data) {
                if (data.isEmpty) {
                  return const CustomScrollView(
                    slivers: [
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: EmptyWidget(
                          icon: '$kSvgDir/order/cart.svg',
                          title: 'No products available',
                          subTitle:
                              'There are currently no available\nproducts in the inventory',
                          baseline: false,
                        ),
                      ),
                    ],
                  );
                }

                return CustomScrollView(
                  slivers: [
                    const SliverToBoxAdapter(
                      child: Gap(20),
                    ),
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Your Listings',
                              style: textTheme.headlineSmall,
                            ),
                            const Gap(10),
                          ],
                        ),
                      ),
                    ),
                    SliverPadding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      sliver: SliverGrid(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3, // Number of columns
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 10,
                          childAspectRatio: 1.2,
                        ),
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            return VariantGridItem(
                                getExportListings(
                                    data, selectedCategory, searchTerm)[index],
                                key: UniqueKey());
                          },
                          childCount: getExportListings(
                                  data, selectedCategory, searchTerm)
                              .length, // Number of items in the first grid
                        ),
                      ),
                    ),
                  ],
                );
              },
              loading: () => loadingWidget(context),
              error: (e, s) {
                return CustomScrollView(
                  slivers: [
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: Center(
                        child: FailureWidget(
                          // fullScreen: true,
                          heightFactor: 0.7,
                          e: e,
                          retry: () => ref
                              .read(orderControllerProvider.notifier)
                              .fetchExportVariants(),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Variant> getExportListings(
      List<Variant> variants, String category, String? searchTerm) {
    final pm = ProductsManager(variants);
    return pm.getItemsByCategory(category, searchTerm);
  }
}
