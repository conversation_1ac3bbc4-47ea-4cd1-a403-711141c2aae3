import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/app/shipments/widgets/tile.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class OrderDeliveryWidget extends ConsumerStatefulWidget {
  final Order order;
  final VoidCallback onSuccess;

  const OrderDeliveryWidget(
    this.order, {
    super.key,
    required this.onSuccess,
  });

  @override
  ConsumerState<OrderDeliveryWidget> createState() =>
      _OrderDeliveryWidgetState();
}

class _OrderDeliveryWidgetState extends ConsumerState<OrderDeliveryWidget> {
  final loader = ValueNotifier<bool>(false);

  Order get order => widget.order;
  List<OrderItem> get items => order.items ?? [];

  final String quantityShippedKey = 'quantityShipped';

  late List<Map<String, dynamic>> markedItems;

  @override
  void initState() {
    super.initState();
    _initializeMarkedItems();
  }

  void _initializeMarkedItems() {
    markedItems = items
        .map((x) => {
              ...x.toMap(),
              quantityShippedKey: x.quantity ?? 0,
            })
        .map((map) {
      map.removeWhere(
          (key, value) => value == null || (value is List && value.isEmpty));
      if (map.containsKey('id')) {
        map['_id'] = map.remove('id');
      }
      return map;
    }).toList();
  }

  void _updateMarkedItemQuantity(String id, num newQuantity) {
    final itemIndex = markedItems.indexWhere((item) => item['_id'] == id);
    if (itemIndex != -1) {
      markedItems[itemIndex][quantityShippedKey] = newQuantity;
    }
  }

  bool _hasShippedQuantity() {
    return markedItems.any((item) => item[quantityShippedKey] > 0);
  }

  Future<void> _markAsDelivered(BuildContext context) async {
    if (!_hasShippedQuantity()) {
      Toast.error('Input the delivered quantity to ship this order', context,
          title: 'Quantity Required');
      return;
    }

    loader.value = true;

    final res = await ref.read(
      markOrderAsDeliveredUseCaseProvider({'items': markedItems}),
    );

    res.when(
      success: (_) {
        loader.value = false;
        context.pop();
        widget.onSuccess.call();
      },
      failure: (error, _) {
        loader.value = false;
        Toast.apiError(
          error,
          context,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        _buildHeader(context, textTheme),
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverFillRemaining(
                hasScrollBody: false,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOrderDetails(context, textTheme),
                    Divider(color: Palette.stroke),
                    _buildItemReviewSection(context, textTheme),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, TextTheme textTheme) {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
      ),
      child: Row(
        children: [
          IconButton(
            icon: SvgPicture.asset('$kSvgDir/order/close.svg'),
            onPressed: () => Navigator.pop(context),
          ),
          const Gap(10),
          Flexible(
            child: Text(
              'Orders',
              style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
            ),
          ),
          const Gap(16),
          SvgPicture.asset('$kSvgDir/packs/chevron_right.svg',
              width: 14, height: 12),
          const Gap(10),
          Flexible(
            child: Text(
              order.orderNumber.toString(),
              style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(order.orderNumber.toString(), style: textTheme.headlineSmall),
          const Gap(10),
          Wrap(
            spacing: 20,
            runSpacing: 10,
            children: [
              TileWidget(
                title: 'AMOUNT'.toUpperCase(),
                subtitle: CurrencyWidget.tableString(
                    context,
                    order.currency?.iso ?? defaultCurrency.iso!,
                    order.total ?? 0),
              ),
              TileWidget(
                  title: 'DELIVERING TO'.toUpperCase(),
                  subtitle: order.address?.address1 ?? '_'),
              TileWidget(
                title: 'CREATED ON'.toUpperCase(),
                subtitle: order.createdAt?.toDayMonth() ?? '_',
              ),
              TileWidget(
                title: 'STATUS'.toUpperCase(),
                subtitle: order.shippingStatus,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemReviewSection(BuildContext context, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Review and confirm items and quantity',
            style: textTheme.headlineSmall,
          ),
          const Gap(20),
          ...mapIndexed(markedItems, (index, item) {
            return _buildItemTile(context, textTheme, item);
          }),
          const Gap(20),
          SizedBox(
            width: double.maxFinite,
            child: CustomFilledButton(
              text: 'Mark as delivered',
              loaderNotifier: loader,
              disabledNotifier: loader,
              onPressed: () => _markAsDelivered(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemTile(
      BuildContext context, TextTheme textTheme, Map<String, dynamic> item) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 14),
      width: 546,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            leading: CachedImage(item['variantId'], ImageSize.small),
            title: Text(item['name'], style: textTheme.bodyMedium),
          ),
          const Gap(20),
          Text(
            'Quantity',
            style: textTheme.bodySmall?.copyWith(color: Palette.blackSecondary),
          ),
          const Gap(5),
          GridItemQuantityPicker(
            item[quantityShippedKey],
            item['quantity'],
            height: 40,
            allowDecimal: true,
            onQuantityChanged: (quantity) {
              _updateMarkedItemQuantity(item['_id'], quantity);
              setState(() {});
            },
          ),
        ],
      ),
    );
  }
}

// class OrderDeliveryWidget extends ConsumerStatefulWidget {
//   final Order order;
//   final VoidCallback onSuccess;
//   const OrderDeliveryWidget(
//     this.order, {
//     super.key,
//     required this.onSuccess,
//   });

//   @override
//   ConsumerState<OrderDeliveryWidget> createState() =>
//       _OrderDeliveryWidgetState();
// }

// class _OrderDeliveryWidgetState extends ConsumerState<OrderDeliveryWidget> {
//   final loader = ValueNotifier<bool>(false);

//   Order get order => widget.order;
//   List<OrderItem> get items => order.items ?? [];

//   final quantityShipped = 'quantityShipped';

//   late List<Map<String, dynamic>> markedItems = items
//       .map((x) => {
//             ...x.toMap(),
//             quantityShipped: x.quantity ?? 0,
//           })
//       .map((map) {
//     // Remove null values
//     map.removeWhere(
//         (key, value) => (value == null || (value is List && value.isEmpty)));

//     // Convert "id" to "_id"
//     if (map.containsKey('id')) {
//       map['_id'] = map.remove('id');
//     }

//     return map;
//   }).toList();

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;

//     return Column(
//       children: [
//         Container(
//           height: 70,
//           padding: const EdgeInsets.symmetric(horizontal: 40),
//           decoration: BoxDecoration(
//             border: Border.all(color: Palette.stroke),
//           ),
//           child: Row(
//             children: [
//               Flexible(
//                 child: IconButton(
//                   icon: SvgPicture.asset('$kSvgDir/order/close.svg'),
//                   onPressed: () => context.pop(),
//                 ),
//               ),
//               const Gap(10),
//               Flexible(
//                 child: Text(
//                   'Orders',
//                   style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
//                 ),
//               ),
//               const Gap(16),
//               SvgPicture.asset('$kSvgDir/packs/chevron_right.svg',
//                   width: 14, height: 12),
//               const Gap(10),
//               Flexible(
//                 child: Text(
//                   order.orderNumber.toString(),
//                   style: textTheme.bodyLarge?.copyWith(color: Palette.k6B797C),
//                 ),
//               ),
//             ],
//           ),
//         ),
//         Expanded(
//             child: CustomScrollView(
//           slivers: [
//             SliverFillRemaining(
//               hasScrollBody: false,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.symmetric(
//                         vertical: 16, horizontal: 40),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           order.orderNumber.toString(),
//                           style: textTheme.headlineSmall,
//                         ),
//                         const Gap(10),
//                         Wrap(
//                           spacing: 20,
//                           runSpacing: 10,
//                           children: [
//                             TileWidget(
//                               title: 'AMOUNT'.toUpperCase(),
//                               subtitle: CurrencyWidget.tableString(
//                                   context,
//                                   order.currency?.iso ?? defaultCurrency.iso!,
//                                   order.total ?? 0),
//                             ),
//                             TileWidget(
//                                 title: 'DELIVERING TO'.toUpperCase(),
//                                 subtitle: order.address?.address1 ?? '_'),
//                             TileWidget(
//                               title: 'CREATED ON'.toUpperCase(),
//                               subtitle: order.createdAt?.toDayMonth() ?? '_',
//                             ),
//                             TileWidget(
//                               title: 'STATUS'.toUpperCase(),
//                               subtitle: order.shippingStatus,
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                   Divider(color: Palette.stroke),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(
//                         vertical: 16, horizontal: 40),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           'Review and confirm items and quantity',
//                           style: textTheme.headlineSmall,
//                         ),
//                         const Gap(20),
//                         ...mapIndexed(markedItems, (index, item) {
//                           return Container(
//                             decoration: BoxDecoration(
//                               border: Border.all(
//                                 color: Palette.stroke,
//                               ),
//                               borderRadius: BorderRadius.circular(8),
//                             ),
//                             padding: const EdgeInsets.symmetric(
//                                 vertical: 12, horizontal: 14),
//                             width: 546,
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 ListTile(
//                                   leading: CachedImage(
//                                       item['variantId'], ImageSize.small),
//                                   title: Text(
//                                     item['name'],
//                                     style: textTheme.bodyMedium,
//                                   ),
//                                 ),
//                                 const Gap(20),
//                                 Text(
//                                   'Quantity',
//                                   style: textTheme.bodySmall
//                                       ?.copyWith(color: Palette.blackSecondary),
//                                 ),
//                                 const Gap(5),
//                                 GridItemQuantityPicker(
//                                   item[quantityShipped],
//                                   item['quantity'],
//                                   height: 40,
//                                   onQuantityChanged: (quantity) {
//                                     final found = markedItems.firstWhere(
//                                         (x) => x['id'] == item['id']);
//                                     final index = markedItems.indexOf(found);
//                                     markedItems[index] = {
//                                       ...found,
//                                       quantityShipped: quantity,
//                                     };

//                                     setState(() {
//                                       // rebuild
//                                     });
//                                   },
//                                 ),
//                               ],
//                             ),
//                           );
//                         }),
//                         const Gap(20),
//                         SizedBox(
//                           width: double.maxFinite,
//                           child: CustomFilledButton(
//                             text: 'Mark as delivered',
//                             loaderNotifier: loader,
//                             disabledNotifier: loader,
//                             onPressed: () async {
//                               final itemsWithShippedQuantityGreaterThanZero =
//                                   markedItems.firstWhereOrNull(
//                                       (x) => x[quantityShipped] > 0);

//                               if (itemsWithShippedQuantityGreaterThanZero ==
//                                   null) {
//                                 return ToastBox.showError(
//                                     context, 'Cannot ship zero quantity');
//                               }

//                               loader.value = true;

//                               final res = await ref.read(
//                                   markOrderAsDeliveredUseCaseProvider(
//                                       {'items': markedItems}));

//                               res.when(
//                                 success: (_) {
//                                   loader.value = false;
//                                   context.pop();
//                                   widget.onSuccess.call();
//                                 },
//                                 failure: (error, _) {
//                                   loader.value = false;
//                                   ToastBox.apiError(context, error);
//                                 },
//                               );
//                             },
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             )
//           ],
//         ))
//       ],
//     );
//   }
// }
