import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/widgets/table_summary_row.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class PurchaseOrderWidget extends ConsumerStatefulWidget {
  const PurchaseOrderWidget(this.orderType, {super.key});

  final OrderType orderType;

  @override
  ConsumerState<PurchaseOrderWidget> createState() =>
      _PurchaseOrderWidgetState();
}

class _PurchaseOrderWidgetState extends ConsumerState<PurchaseOrderWidget> {
  final _scrollController = ScrollController();
  late String orderReference;

  late final isSalesOrder = widget.orderType == OrderType.sales;

  @override
  void initState() {
    orderReference = ref.read(cartProvider.notifier).populateReference();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    const double padding = 4;

    final cartState = ref.watch(cartProvider);
    final cartNotifier = ref.watch(cartProvider.notifier);

    final branch = ref.read(branchesProvider).first;

    final cartItems = cartState.uniqueCartItemsWithNonZeroCount;

    final address = (cartState.branch?.contactPhone ?? '').startsWith('+44')
        ? 'Tradedepot Inc.\n60 Capitol way,\nColindale,\nLondon NW90BR'
        : 'Tradedepot\n3/4 Adewunmi Industrial Estate\nKudirat Abiola Way,\nOregun Ikeja,\nLagos Nigeria';

    return Container(
      decoration: BoxDecoration(
        color: Palette.kFCFCFC,
      ),
      child: Container(
        // width: 185.w,
        // height: 754.h,
        padding: const EdgeInsets.symmetric(horizontal: 40),
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 40),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8)),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 10)],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            const Gap(40),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    widget.orderType.name,
                    style: textTheme.bodyLarge?.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Flexible(child: SvgPicture.asset(kLogoSvg))
              ],
            ),
            const Gap(40),
            Column(
              children: [
                ItemRow('Order number',
                    '$orderReference - ${isSalesOrder ? '-' : 'DRAFT'}'),
                ItemRow('Date', DateTime.now().toDate()),
                const Gap(30),
                isSalesOrder
                    ? Column(
                        children: [
                          const ItemRow('Company Name', 'Delivery Address',
                              extra: 'Sold By', useBoldFontWeight: true),
                          const Gap(3),
                          ItemRow(
                            branch.outletBusinessName,
                            branch.streetName ?? branch.outletBusinessName,
                            extra: branch.outletBusinessName,
                          ),
                        ],
                      )
                    : Column(
                        children: [
                          const ItemRow('Order by', 'Order to',
                              useBoldFontWeight: true),
                          const Gap(3),
                          ItemRow(cartState.branch?.outletBusinessName ?? '',
                              address),
                        ],
                      ),
              ],
            ),
            const Gap(30),
            // Due Date and Amount Section
            // Text(
            //   '£0.00 GBP due April 24, 2024',
            //   style: textTheme.bodyLarge?.copyWith(
            //     // fontFamily: fontFamily,
            //     fontWeight: FontWeight.w700,
            //   ),
            // ),
            // const Gap(30),
            // Table Section
            Table(
              // border: TableBorder(
              //     horizontalInside: BorderSide(
              //         color: Palette.primaryBlack)),
              // defaultVerticalAlignment:
              //     TableCellVerticalAlignment.bottom,
              columnWidths: const {
                0: FlexColumnWidth(20),
                1: FlexColumnWidth(3),
                2: FlexColumnWidth(8),
                3: FlexColumnWidth(6),
                4: FlexColumnWidth(3),
                5: FlexColumnWidth(6),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: Palette.primaryBlack))),
                  children: [
                    Padding(
                        padding: const EdgeInsets.all(padding),
                        child: Text(
                          'Description',
                          style: textTheme.bodySmall
                              ?.copyWith(fontWeight: FontWeight.w500),
                        )),
                    Padding(
                        padding: const EdgeInsets.all(padding),
                        child: Text('Qty',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500))),
                    Padding(
                        padding: const EdgeInsets.all(padding),
                        child: Text('Unit Price',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500))),
                    Padding(
                        padding: const EdgeInsets.all(padding),
                        child: Text('Discount',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500))),
                    Padding(
                        padding: const EdgeInsets.all(padding),
                        child: Text('Tax',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500))),
                    Padding(
                        padding: const EdgeInsets.all(padding),
                        child: Text('Amount',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500))),
                  ],
                ),
                // For table content
                for (final item in cartItems)
                  TableRow(
                    children: [
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                            item.variant.name ?? '',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          )),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                            '${item.count ?? 0}',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          )),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                            CurrencyWidget.value(
                                context,
                                item.variant.currency?.iso ??
                                    kDefaultGBCurrency,
                                item.variant.price ?? 0),
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          )),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                            CurrencyWidget.value(
                                context,
                                item.variant.currency?.iso ??
                                    kDefaultGBCurrency,
                                item.variant.discount ?? 0),
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          )),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                            CurrencyWidget.value(
                                context,
                                item.variant.currency?.iso ??
                                    kDefaultGBCurrency,
                                0),
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          )),
                      Padding(
                          padding: const EdgeInsets.all(padding),
                          child: Text(
                            CurrencyWidget.value(
                                context,
                                item.variant.currency?.iso ??
                                    kDefaultGBCurrency,
                                item.total),
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          )),
                    ],
                  ),
              ],
            ),
            const Gap(20),
            if (cartNotifier.discount > 0)
              TableSummaryRow(
                  'Discount',
                  CurrencyWidget.value(
                      context,
                      cartItems.first.variant.currency?.iso ??
                          kDefaultGBCurrency,
                      cartNotifier.discount)),
            // const TableSummaryRow('VAT (20%)', '£0.00'),
            TableSummaryRow(
                'Subtotal',
                CurrencyWidget.value(context, cartNotifier.currencyCode,
                    cartNotifier.totalPrice)),
            TableSummaryRow(
                'Taxes',
                CurrencyWidget.value(
                    context, cartNotifier.currencyCode, cartNotifier.taxes)),
            TableSummaryRow(
                'Total',
                CurrencyWidget.value(context, cartNotifier.currencyCode,
                    cartNotifier.totalPrice)),
            const Gap(4),
            TableSummaryRow(
                'Amount Due',
                CurrencyWidget.tableString(
                    context, cartNotifier.currencyCode, cartNotifier.amountDue),
                useBoldFontWeight: true),
            // const Spacer(),
            const Gap(50),
            if (!isSalesOrder) ...[
              Divider(color: Palette.kE7E7E7),
              const Gap(10),
              Text(
                '$orderReference - DRAFT - ${CurrencyWidget.tableString(context, cartNotifier.currencyCode, cartNotifier.amountDue)}',
                style: textTheme.bodySmall
                    ?.copyWith(fontWeight: FontWeight.w500)
                    .copyWith(color: Palette.k6B797C),
              ),
              const Gap(40),
            ],
          ],
        ),
      ),
    );
  }
}

class ItemRow extends StatelessWidget {
  final String title;

  final String content;
  final String? extra;
  final bool? useBoldFontWeight;
  const ItemRow(
    this.title,
    this.content, {
    super.key,
    this.useBoldFontWeight = false,
    this.extra,
  });

  @override
  Widget build(BuildContext context) {
    const fontFamily = 'Pretendard Variable';

    final style = Theme.of(context).textTheme.bodySmall!.copyWith(
          fontFamily: fontFamily,
          color: useBoldFontWeight!
              ? Palette.primaryBlack
              : Palette.blackSecondary,
        );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: Text(
            title,
            style: useBoldFontWeight!
                ? style.copyWith(fontWeight: FontWeight.w600)
                : style,
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            content,
            style: useBoldFontWeight!
                ? style.copyWith(fontWeight: FontWeight.w600)
                : style,
          ),
        ),
        Expanded(
          flex: 3,
          child: extra != null
              ? Text(
                  extra!,
                  style: useBoldFontWeight!
                      ? style.copyWith(fontWeight: FontWeight.w600)
                      : style,
                )
              : Container(),
        ),
      ],
    );
  }
}
