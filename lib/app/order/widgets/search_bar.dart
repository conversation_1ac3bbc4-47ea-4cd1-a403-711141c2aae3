import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class SearchBar extends StatelessWidget {
  final TextEditingController controller;
  const SearchBar({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return SizedBox(
      height: 42,
      width: double.infinity,
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          prefixIcon: Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: SizedBox(
              width: 16,
              height: 16,
              child: SvgPicture.asset(
                '$kSvgDir/order/search.svg',
                fit: BoxFit.scaleDown,
              ),
            ),
          ),
          hintText: 'Search',
          hintStyle: textTheme.bodyMedium?.copyWith(
            color: Palette.placeholder,
            fontWeight: FontWeight.w400,
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Palette.stroke),
            borderRadius: BorderRadius.circular(6),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Palette.primary),
            borderRadius: BorderRadius.circular(6),
          ),
          contentPadding: const EdgeInsets.only(bottom: 40),
        ),
        textAlignVertical: TextAlignVertical.center,
        cursorHeight: 14,
      ),
    );
  }
}
