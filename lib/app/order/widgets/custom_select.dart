import 'package:flutter/material.dart';

class CustomSelect extends StatelessWidget {
  final String placeholder;
  final bool hasInputIcon;
  final Function action;
  final List<Map<String, String>> items;
  final bool open;
  final Function setOpen;
  final String emptyText;

  const CustomSelect({
    super.key,
    this.placeholder = "Select",
    this.hasInputIcon = false,
    required this.action,
    required this.items,
    required this.open,
    required this.setOpen,
    this.emptyText = "No Items Found",
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => setOpen(),
          child: Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(placeholder),
                const Icon(Icons.keyboard_arrow_down),
              ],
            ),
          ),
        ),
        if (open)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              children: items.isEmpty
                  ? [Text(emptyText)]
                  : items.map((el) {
                      return ListTile(
                        title: Text(el['title'] ?? ''),
                        subtitle: Text(el['subtext'] ?? ''),
                        onTap: () => action(el),
                      );
                    }).toList(),
            ),
          ),
      ],
    );
  }
}
