import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/cart_item.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_controller.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/order_state.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/src/components/buttons/custom_elevated_button.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class CartItemWidget extends ConsumerStatefulWidget {
  const CartItemWidget(this.item,
      {super.key,
      this.isEditing = false,
      this.index = -1,
      this.onIndexChanged,
      this.onSave,
      this.focusedField,
      this.onFocusChange,
      this.ignoreQuantityLimits = false,
      required this.orderType});

  final CartItem item;
  final bool isEditing;
  final int index;
  final ValueChanged<int>? onIndexChanged;
  final ValueChanged<num>? onSave;
  final FocusedField? focusedField;
  final ValueChanged<FocusedField>? onFocusChange;
  final bool ignoreQuantityLimits;
  final OrderType orderType;

  @override
  ConsumerState<CartItemWidget> createState() => _CartItemWidgetState();
}

class _CartItemWidgetState extends ConsumerState<CartItemWidget> {
  // late final palletQuantity = widget.item.variant.subUnit?.palletQuantity ?? 1;

  // bool get canUpdatePallet =>
  //     widget.item.variant.subUnit?.palletQuantity != null;

  // bool get isExportCountry =>
  //     ref.read(countryTypeProvider) == CountryType.export;

  // bool get isSalesOrder => widget.orderType == OrderType.sales;

  // late num quantity;
  // // only available and in use on export variants
  // late num usedPallets;

  // Variant get variant => widget.item.variant;
  // String? get variantId => variant.variantId;
  // num get maxValue => widget.ignoreQuantityLimits
  //     ? defaultItemQuantity
  //     : isExportCountry
  //         ? defaultItemQuantity
  //         : variant.extVariant?.available ?? 0;
  // String get currencyCode => variant.currency?.iso ?? kDefaultGBCurrency;

  late final num palletQuantity =
      widget.item.variant.subUnit?.palletQuantity ?? 1;
  late num quantity;
  // only available and in use on export variants
  late num usedPallets;

  bool get canUpdatePallet =>
      widget.item.variant.subUnit?.palletQuantity != null;
  bool get isExportCountry =>
      ref.read(countryTypeProvider) == CountryType.export;
  bool get isSalesOrder => widget.orderType == OrderType.sales;

  Variant get variant => widget.item.variant;
  String? get variantId => variant.variantId;
  num get maxValue => widget.ignoreQuantityLimits
      ? defaultItemQuantity
      : isExportCountry
          ? defaultItemQuantity
          : variant.extVariant?.available ?? 0;
  String get currencyCode => variant.currency?.iso ?? kDefaultGBCurrency;

  @override
  void initState() {
    super.initState();
    quantity = widget.item.count ?? 0;
    usedPallets = calculateUsedPallets();

    final available = variant.extVariant?.available;
    if (available == null || available <= 0) {
      final currentState = ref.read(orderControllerProvider);
      updateCart(ref, currentState, widget.item, isExportCountry);
    }
  }

  @override
  void didUpdateWidget(covariant CartItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    /// prevent resetting of quantity for export countries
    if (!isExportCountry) {
      setState(() {
        quantity = widget.item.count ?? 0;
        if (isExportCountry) {
          usedPallets = calculateUsedPallets();
        }
      });
    }
  }

  void updateQuantity(num newQuantity) {
    setState(() {
      quantity = newQuantity;
    });
    widget.onSave?.call(newQuantity);
    if (isExportCountry && canUpdatePallet) {
      final updatedPallets = calculateUsedPallets();
      if (updatedPallets != usedPallets) {
        setState(() {
          usedPallets = updatedPallets;
        });
      }
    }
    widget.onFocusChange?.call(FocusedField.quantity);
  }

  // void updateUiOnly(num newQuantity) {
  //   setState(() {
  //     quantity = newQuantity;
  //   });

  //   if (isExportCountry && canUpdatePallet) {
  //     final updatedPallets = calculateUsedPallets();
  //     setState(() {
  //       usedPallets = updatedPallets;
  //     });
  //   }
  // }

  void updatePallet(num newQuantity) {
    final itemQuantity = newQuantity * palletQuantity;
    setState(() {
      usedPallets = newQuantity;
      quantity = itemQuantity;
    });

    if (itemQuantity > 0) {
      widget.onSave?.call(itemQuantity);
    }
    widget.onFocusChange?.call(FocusedField.pallet);
  }

  num calculateUsedPallets() {
    // return (quantity + palletQuantity - 1) ~/ palletQuantity;
    double pallets = quantity / palletQuantity;
    return double.parse(pallets.toStringAsFixed(2));
  }

  @override
  Widget build(BuildContext context) {
    // Listen for ongoing state changes
    ref.listen<OrderState>(orderControllerProvider, (previousState, newState) {
      updateCart(ref, newState, widget.item, isExportCountry);
    });

    return AnimatedSwitcherWidget(
      child:
          (!isExportCountry || isSalesOrder) ? nonExportCart() : exportCart(),
    );
  }

  Widget exportCart() {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      SizedBox(
                        width: 44,
                        height: 44,
                        child: CachedImage(variantId, ImageSize.small),
                      ),
                      const Gap(10),
                      Expanded(
                        child: Text(
                          (variant.name ?? '').toUpperCase(),
                          style: textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                deleteIconButton(),
              ],
            ),
            const Gap(12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 3,
                  child: decoratedQuantityField(
                    'Quantity',
                    quantity,
                    maxValue,
                    widget.isEditing &&
                        widget.focusedField == FocusedField.quantity,
                    FocusedField.quantity,
                    (value) {
                      // if (value > 0) {
                      updateQuantity(num.tryParse(value.toString()) ?? 0);
                      // } else {
                      // updateUiOnly(value);
                      // }
                    },
                  ),
                ),
                Expanded(
                    flex: 3,
                    child: decoratedQuantityField(
                      'Pallet',
                      usedPallets,
                      maxValue,
                      widget.isEditing &&
                          widget.focusedField == FocusedField.pallet,
                      FocusedField.pallet,
                      (value) {
                        //  if (value > 0) {
                        updatePallet(num.tryParse(value.toString()) ?? 0);
                        //}
                      },
                    )),
                if (!canUpdatePallet) const Gap(4),
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 6),
                        child: Text(
                          'Amount',
                          style: textTheme.bodySmall?.copyWith(
                            color: Palette.blackSecondary,
                          ),
                        ),
                      ),
                      const Gap(6),
                      decoratedPrice(40, true),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget nonExportCart() {
    return widget.isEditing ? editingCartWidget() : cartWidget();
  }

  Widget editingCartWidget() {
    return Container(
      // height: 140,
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 5,
                  child: Container(
                    height: 42,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 10),
                    decoration: BoxDecoration(
                      color: Palette.stroke.withValues(alpha: 0.7),
                      border: Border.all(color: Palette.stroke),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      variant.name ?? '',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 42,
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    decoration: BoxDecoration(
                      // color: Palette.stroke,
                      border: Border.all(color: Palette.stroke),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: CartQuantityInputField(
                      initialCount: quantity,
                      maxValue: maxValue,
                      onQuantityChanged: (value) {
                        if (value == null) return;
                        setState(() {
                          quantity = num.tryParse(value.toString()) ?? 0;
                        });
                      },
                      cursorHeight: 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: decoratedPrice(42),
                )
              ],
            ),
            const Gap(5),
            Text(
              'Max: ${CurrencyWidget.formattedAmount(context, maxValue)} items',
              style: textTheme.bodySmall,
            ),
            const Gap(10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomElevatedButton(
                  text: 'Cancel',
                  onPressed: () {
                    setState(() {
                      quantity = widget.item.count ?? 0;
                      if (widget.onIndexChanged != null) {
                        widget.onIndexChanged!(-1);
                      }
                    });
                  },
                ),
                const Gap(10),
                SizedBox(
                  height: 30,
                  width: 60,
                  child: CustomFilledButton(
                    text: 'Save',
                    onPressed: () {
                      if (widget.onSave != null) {
                        widget.onSave!(quantity);
                      }
                    },
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget cartWidget() {
    return Container(
      padding: const EdgeInsets.only(bottom: 4),
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Palette.stroke))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 8,
            child: Row(
              children: [
                Flexible(
                  child: Text(
                    variant.name ?? '',
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  ' x${widget.item.count}',
                  style: textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Palette.blackSecondary,
                  ),
                )
              ],
            ),
          ),
          const Spacer(flex: 1),
          Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.centerRight,
              child: CurrencyWidget(widget.item.total, currencyCode),
            ),
          ),
          const Gap(6),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              deleteIconButton(),
              InkWell(
                onTap: () {
                  if (widget.onIndexChanged != null) {
                    widget.onIndexChanged!(widget.index);
                  }
                },
                borderRadius: BorderRadius.circular(30),
                child: CircleAvatar(
                  backgroundColor: Colors.transparent,
                  child: SvgPicture.asset('$kSvgDir/order/edit.svg'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget deleteIconButton() {
    return InkWell(
      onTap: () {
        ref.read(cartProvider.notifier).removeItem(variant.variantSupplierId);
      },
      borderRadius: BorderRadius.circular(30),
      child: CircleAvatar(
        backgroundColor: Colors.transparent,
        child: SizedBox(
          width: 25,
          height: 25,
          child: SvgPicture.asset(
            '$kSvgDir/order/delete.svg',
            fit: BoxFit.scaleDown,
          ),
        ),
      ),
    );
  }

  Widget decoratedPrice(double height, [bool showCurrency = false]) {
    return Container(
      height: height,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      decoration: BoxDecoration(
        color: Palette.stroke.withValues(alpha: 0.7),
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        showCurrency
            ? VariantPriceWidget.value(context, ref, variant, quantity)
            : VariantPriceWidget.formattedPrice(
                context, ref, variant, quantity),
        style: textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w700,
          // height: 1.6,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget decoratedQuantityField(
      String title,
      num quantity,
      num maxValue,
      bool autoFocus,
      FocusedField focusedField,
      ValueChanged<dynamic> onQuantityChanged) {
    final isPallet = title == 'Pallet';
    final isReadonlyPallet = isPallet && !canUpdatePallet;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 6),
          child: Text(
            title,
            style: textTheme.bodySmall?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ),
        const Gap(6),
        BlurredContent(
          blur: false,
          // blur: focusedField == FocusedField.pallet && !canUpdatePallet,
          child: Container(
            height: 40,
            margin: const EdgeInsets.symmetric(horizontal: 5),
            decoration: BoxDecoration(
              color: Palette.stroke,
              border: Border.all(color: Palette.stroke),
              borderRadius: BorderRadius.circular(8),
            ),
            child: GridItemQuantityPicker(
              // key: UniqueKey(),
              quantity,
              maxValue,
              autoFocus: autoFocus,
              onQuantityChanged: onQuantityChanged,
              readOnly: focusedField == FocusedField.pallet && !canUpdatePallet,
              contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 11),
              cursorHeight: 18,
              focusBorder: false,
              emptyIfZero: isReadonlyPallet,
              restrictToAllowedDecimals: !isPallet,
              // canReduceToZero: false,
            ),
          ),
        ),
      ],
    );
  }

  TextTheme get textTheme => Theme.of(context).textTheme;
}


// class CartItemWidget extends ConsumerStatefulWidget {
//   const CartItemWidget(this.item,
//       {super.key,
//       this.isEditing = false,
//       this.index = -1,
//       this.onIndexChanged,
//       this.onSave,
//       this.focusedField,
//       this.onFocusChange,
//       this.ignoreQuantityLimits = false,
//       required this.orderType});

//   final CartItem item;
//   final bool isEditing;
//   final int index;
//   final ValueChanged<int>? onIndexChanged;
//   final ValueChanged<num>? onSave;
//   final FocusedField? focusedField;
//   final ValueChanged<FocusedField>? onFocusChange;
//   final bool ignoreQuantityLimits;
//   final OrderType orderType;

//   @override
//   ConsumerState<CartItemWidget> createState() => _CartItemWidgetState();
// }

// class _CartItemWidgetState extends ConsumerState<CartItemWidget> {
//   late final num palletQuantity =
//       widget.item.variant.subUnit?.palletQuantity ?? 1;
//   late num quantity;
//   late num usedPallets;

//   bool get canUpdatePallet =>
//       widget.item.variant.subUnit?.palletQuantity != null;
//   bool get isExportCountry =>
//       ref.read(countryTypeProvider) == CountryType.export;
//   bool get isSalesOrder => widget.orderType == OrderType.sales;

//   Variant get variant => widget.item.variant;
//   String? get variantId => variant.variantId;
//   num get maxValue => widget.ignoreQuantityLimits
//       ? defaultItemQuantity
//       : isExportCountry
//           ? defaultItemQuantity
//           : variant.extVariant?.available ?? 0;
//   String get currencyCode => variant.currency?.iso ?? kDefaultGBCurrency;

//   @override
//   void initState() {
//     super.initState();
//     quantity = widget.item.count ?? 0;
//     usedPallets = calculateUsedPallets();

//     final available = variant.extVariant?.available;
//     if (available == null || available <= 0) {
//       final currentState = ref.read(orderControllerProvider);
//       updateCart(ref, currentState, widget.item, isExportCountry);
//     }
//   }

//   @override
//   void didUpdateWidget(covariant CartItemWidget oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (widget.item != oldWidget.item) {
//       setState(() {
//         quantity = widget.item.count ?? 0;
//         if (isExportCountry) {
//           usedPallets = calculateUsedPallets();
//         }
//       });
//     }
//   }

//   void updateQuantity(num newQuantity) {
//     setState(() {
//       quantity = newQuantity;
//     });
//     widget.onSave?.call(newQuantity);
//     if (isExportCountry && canUpdatePallet) {
//       final updatedPallets = calculateUsedPallets();
//       if (updatedPallets != usedPallets) {
//         setState(() {
//           usedPallets = updatedPallets;
//         });
//       }
//     }
//     widget.onFocusChange?.call(FocusedField.quantity);
//   }

//   void updatePallet(num newQuantity) {
//     final itemQuantity = newQuantity * palletQuantity;
//     setState(() {
//       usedPallets = newQuantity;
//       quantity = itemQuantity;
//     });
//     if (itemQuantity > 0) {
//       widget.onSave?.call(itemQuantity);
//     }
//     widget.onFocusChange?.call(FocusedField.pallet);
//   }

//   num calculateUsedPallets() {
//     double pallets = quantity / palletQuantity;
//     return double.parse(pallets.toStringAsFixed(2));
//   }

//   @override
//   Widget build(BuildContext context) {
//     ref.listen<OrderState>(orderControllerProvider, (previousState, newState) {
//       updateCart(ref, newState, widget.item, isExportCountry);
//     });

//     return AnimatedSwitcherWidget(
//       child:
//           (!isExportCountry || isSalesOrder) ? nonExportCart() : exportCart(),
//     );
//   }

//   Widget exportCart() {
//     return Container(
//       margin: const EdgeInsets.only(bottom: 10),
//       decoration: BoxDecoration(
//         border: Border.all(color: Palette.stroke),
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(12.0),
//         child: Column(
//           children: [
//             _buildHeader(),
//             const Gap(12),
//             _buildExportCartFields(),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget nonExportCart() {
//     return widget.isEditing ? editingCartWidget() : cartWidget();
//   }

//   Widget editingCartWidget() {
//     return Container(
//       margin: const EdgeInsets.only(bottom: 5),
//       decoration: BoxDecoration(
//         border: Border.all(color: Palette.stroke),
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(12.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             _buildEditingHeader(),
//             const Gap(5),
//             Text(
//               'Max: ${CurrencyWidget.formattedAmount(context, maxValue)} items',
//               style: textTheme.bodySmall,
//             ),
//             const Gap(10),
//             _buildEditingActions(),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget cartWidget() {
//     return Container(
//       padding: const EdgeInsets.only(bottom: 4),
//       margin: const EdgeInsets.only(bottom: 4),
//       decoration: BoxDecoration(
//         border: Border(bottom: BorderSide(color: Palette.stroke)),
//       ),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Expanded(
//             flex: 8,
//             child: Row(
//               children: [
//                 Flexible(
//                   child: Text(
//                     variant.name ?? '',
//                     style: textTheme.bodyMedium?.copyWith(
//                       fontWeight: FontWeight.w500,
//                     ),
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 ),
//                 Text(
//                   ' x${widget.item.count}',
//                   style: textTheme.bodyLarge?.copyWith(
//                     fontWeight: FontWeight.w500,
//                     color: Palette.blackSecondary,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           const Spacer(flex: 1),
//           Expanded(
//             flex: 4,
//             child: Align(
//               alignment: Alignment.centerRight,
//               child: CurrencyWidget(widget.item.total, currencyCode),
//             ),
//           ),
//           const Gap(6),
//           _buildCartActions(),
//         ],
//       ),
//     );
//   }

//   Widget _buildHeader() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Row(
//           children: [
//             SizedBox(
//               width: 44,
//               height: 44,
//               child: CachedImage(variantId, ImageSize.small),
//             ),
//             const Gap(10),
//             Text(
//               (variant.name ?? '').toUpperCase(),
//               style: textTheme.bodyMedium?.copyWith(
//                 fontWeight: FontWeight.w500,
//               ),
//               overflow: TextOverflow.ellipsis,
//             ),
//           ],
//         ),
//         _buildDeleteIconButton(),
//       ],
//     );
//   }

//   Widget _buildExportCartFields() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Expanded(
//           flex: 3,
//           child: _buildQuantityField(
//             'Quantity',
//             quantity,
//             maxValue,
//             widget.isEditing && widget.focusedField == FocusedField.quantity,
//             FocusedField.quantity,
//             (value) => updateQuantity(num.tryParse(value.toString()) ?? 0),
//           ),
//         ),
//         Expanded(
//           flex: 3,
//           child: _buildQuantityField(
//             'Pallet',
//             usedPallets,
//             maxValue,
//             widget.isEditing && widget.focusedField == FocusedField.pallet,
//             FocusedField.pallet,
//             (value) => updatePallet(num.tryParse(value.toString()) ?? 0),
//           ),
//         ),
//         if (!canUpdatePallet) const Gap(4),
//         Expanded(
//           flex: 2,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.stretch,
//             children: [
//               Padding(
//                 padding: const EdgeInsets.only(left: 6),
//                 child: Text(
//                   'Amount',
//                   style: textTheme.bodySmall?.copyWith(
//                     color: Palette.blackSecondary,
//                   ),
//                 ),
//               ),
//               const Gap(6),
//               _buildDecoratedPrice(40, true),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildEditingHeader() {
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Flexible(
//           flex: 5,
//           child: Container(
//             height: 42,
//             padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
//             decoration: BoxDecoration(
//               color: Palette.stroke.withValues(alpha: 0.7),
//               border: Border.all(color: Palette.stroke),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: Text(
//               variant.name ?? '',
//               overflow: TextOverflow.ellipsis,
//             ),
//           ),
//         ),
//         Flexible(
//           flex: 2,
//           child: Container(
//             height: 42,
//             margin: const EdgeInsets.symmetric(horizontal: 5),
//             decoration: BoxDecoration(
//               border: Border.all(color: Palette.stroke),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: CartQuantityInputField(
//               initialCount: quantity,
//               maxValue: maxValue,
//               onQuantityChanged: (value) {
//                 setState(() {
//                   quantity = value;
//                 });
//               },
//               cursorHeight: 14,
//             ),
//           ),
//         ),
//         Flexible(
//           flex: 2,
//           child: _buildDecoratedPrice(42),
//         ),
//       ],
//     );
//   }

//   Widget _buildEditingActions() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.end,
//       children: [
//         CustomElevatedButton(
//           text: 'Cancel',
//           onPressed: () {
//             setState(() {
//               quantity = widget.item.count ?? 0;
//               widget.onIndexChanged?.call(-1);
//             });
//           },
//         ),
//         const Gap(10),
//         SizedBox(
//           height: 30,
//           width: 60,
//           child: CustomFilledButton(
//             text: 'Save',
//             onPressed: () {
//               widget.onSave?.call(quantity);
//             },
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildCartActions() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.end,
//       children: [
//         _buildDeleteIconButton(),
//         InkWell(
//           onTap: () {
//             widget.onIndexChanged?.call(widget.index);
//           },
//           borderRadius: BorderRadius.circular(30),
//           child: CircleAvatar(
//             backgroundColor: Colors.transparent,
//             child: SvgPicture.asset('$kSvgDir/order/edit.svg'),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildDeleteIconButton() {
//     return InkWell(
//       onTap: () {
//         ref.read(cartProvider.notifier).removeItem(variant.variantSupplierId);
//       },
//       borderRadius: BorderRadius.circular(30),
//       child: CircleAvatar(
//         backgroundColor: Colors.transparent,
//         child: SizedBox(
//           width: 25,
//           height: 25,
//           child: SvgPicture.asset(
//             '$kSvgDir/order/delete.svg',
//             fit: BoxFit.scaleDown,
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildDecoratedPrice(double height, [bool showCurrency = false]) {
//     return Container(
//       height: height,
//       padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
//       decoration: BoxDecoration(
//         color: Palette.stroke.withValues(alpha: 0.7),
//         border: Border.all(color: Palette.stroke),
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Text(
//         showCurrency
//             ? VariantPriceWidget.value(context, ref, variant, quantity)
//             : VariantPriceWidget.formattedPrice(
//                 context, ref, variant, quantity),
//         style: textTheme.bodyMedium?.copyWith(
//           fontWeight: FontWeight.w700,
//         ),
//         overflow: TextOverflow.ellipsis,
//       ),
//     );
//   }

//   Widget _buildQuantityField(
//       String title,
//       num quantity,
//       num maxValue,
//       bool autoFocus,
//       FocusedField focusedField,
//       ValueChanged<dynamic> onQuantityChanged) {
//     final isPallet = title == 'Pallet';
//     final isReadonlyPallet = isPallet && !canUpdatePallet;
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: const EdgeInsets.only(left: 6),
//           child: Text(
//             title,
//             style: textTheme.bodySmall?.copyWith(
//               color: Palette.blackSecondary,
//             ),
//           ),
//         ),
//         const Gap(6),
//         BlurredContent(
//           blur: false,
//           child: Container(
//             height: 40,
//             margin: const EdgeInsets.symmetric(horizontal: 5),
//             decoration: BoxDecoration(
//               color: Palette.stroke,
//               border: Border.all(color: Palette.stroke),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: GridItemQuantityPicker(
//               quantity,
//               maxValue,
//               autoFocus: autoFocus,
//               onQuantityChanged: onQuantityChanged,
//               readOnly: focusedField == FocusedField.pallet && !canUpdatePallet,
//               contentPadding: const EdgeInsets.fromLTRB(0, 0, 0, 11),
//               cursorHeight: 18,
//               focusBorder: false,
//               emptyIfZero: isReadonlyPallet,
//               restrictToAllowedDecimals: !isPallet,
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   TextTheme get textTheme => Theme.of(context).textTheme;
// }