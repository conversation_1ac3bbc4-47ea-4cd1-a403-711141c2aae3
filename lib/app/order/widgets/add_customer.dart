import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/auth/data/models/outlet_type.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/widgets/sliver_delegate.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/google_places/address_autocomplete_widgets.dart';
import 'package:td_procurement/core/services/google_places/model/suggestion.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/app_values/app_values.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class AddCustomerWidget extends ConsumerStatefulWidget {
  const AddCustomerWidget({super.key, this.isAdvanceInvoice = false});

  final bool isAdvanceInvoice;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AddCustomerWidgetState();
}

class _AddCustomerWidgetState extends ConsumerState<AddCustomerWidget> {
  final _formKey = GlobalKey<FormState>();
  final _validateMode = ValueNotifier(AutovalidateMode.disabled);
  final params = CustomerParams();
  late final _businessTypeController =
      TextEditingController(text: params.businessType?.name);
  late final googleKey = ref.read(appConfigProvider).googleApiKey;
  late final _addressController =
      TextEditingController(text: params.address?.address);
  late final countryCode = ref.read(countryCodeProvider);
  final _isLoading = ValueNotifier<bool>(false);
  late final _isDisabled = ValueNotifier<bool>(!params.isComplete);

  bool get isAdvanceInvoice => widget.isAdvanceInvoice;

  String get breadcrumbText => isAdvanceInvoice
      ? 'Advance invoice > Add customer'
      : 'Create order > Add customer';

  @override
  void initState() {
    Future.microtask(() {
      ref.read(fetchOutletTypesProvider(countryCode));
    });
    super.initState();
  }

  @override
  void dispose() {
    _validateMode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverHeaderDelegate(
              minHeight: 60,
              maxHeight: 60,
              child: Container(
                color: Colors.white,
                child: Row(
                  children: [
                    const Gap(18),
                    IconButton(
                      icon: SvgPicture.asset('$kSvgDir/order/close.svg'),
                      onPressed: () => Navigator.pop(context),
                    ),
                    const Gap(10),
                    Text(
                      'Add Customer',
                      style: textTheme.bodyLarge?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                children: [
                  const Gap(24),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Palette.kE7E7E7,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildBreadcrumb(),
                          const Gap(24),
                          _buildFormCard(context),
                        ],
                      ),
                    ),
                  ),
                  const Gap(200),
                ],
              ),
            ),
          )
        ],
        // padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      ),
    );
  }

  Widget _buildBreadcrumb() {
    return Text(
      breadcrumbText,
      style: textTheme.bodyMedium?.copyWith(
        color: HexColor('#6B797C'),
      ),
    );
  }

  Widget _buildFormCard(BuildContext context) {
    return ValueListenableBuilder<AutovalidateMode>(
      valueListenable: _validateMode,
      builder: (context, mode, _) {
        return Form(
          key: _formKey,
          autovalidateMode: mode,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('Customer details'),
              const Gap(16),
              _buildCustomerDetailsFields(),
              const Gap(24),
              _buildSectionTitle('Outlet information'),
              const Gap(16),
              _buildOutletInformationFields(context),
              const Gap(24),
              _buildAddCustomerButton(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: textTheme.headlineSmall,
    );
  }

  Widget _buildCustomerDetailsFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildTextFormField(
                hintText: 'First Name',
                initialValue: params.firstName,
                onChanged: (value) => params.setFirstName = value.trim(),
                validator: (input) => Validators.verifyInput(
                  input,
                  field: 'first name',
                  length: 3,
                ),
              ),
            ),
            const Gap(10),
            Expanded(
              child: _buildTextFormField(
                hintText: 'Last Name',
                initialValue: params.lastName,
                onChanged: (value) => params.setLastName = value.trim(),
                validator: (input) => Validators.verifyInput(
                  input,
                  field: 'Last name',
                  length: 3,
                ),
              ),
            ),
          ],
        ),
        const Gap(16),
        _buildTextFormField(
          hintText: 'Email Address',
          initialValue: params.email,
          onChanged: (value) => params.setEmail = value.trim(),
          validator: Validators.verifyEmail,
        ),
        const Gap(16),
        _buildTextFormField(
          hintText: 'Phone number',
          initialValue: params.phoneNumber,
          onChanged: (value) => params.setNumber = value.trim(),
          validator: (input) => Validators.verifyInput(
            input,
            field: 'Phone number',
            length: 1,
          ),
          keyboardType: TextInputType.phone,
          inputFormatters: [Validators.validNumberInput()],
        ),
      ],
    );
  }

  Widget _buildTextFormField({
    required String hintText,
    required String? initialValue,
    required Function(String) onChanged,
    required String? Function(String?) validator,
    TextInputType keyboardType = TextInputType.name,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return TextFormField(
      decoration: InputDecoration(
        hintText: hintText,
        constraints: BoxConstraints(
          minHeight: defaultHeight,
          maxHeight: defaultHeight,
        ),
      ),
      initialValue: initialValue,
      onChanged: (value) {
        onChanged(value);
        _isDisabled.value = !params.isComplete;
      },
      validator: validator,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
    );
  }

  Widget _buildOutletInformationFields(BuildContext context) {
    return Column(
      children: [
        _buildTextFormField(
          hintText: 'Outlet name',
          initialValue: params.outletName,
          onChanged: (value) => params.setOutletName = value.trim(),
          validator: (input) => Validators.verifyInput(
            input,
            field: 'Outlet name',
            length: 3,
          ),
        ),
        const Gap(16),
        _buildBusinessTypeDropdown(context),
        const Gap(16),
        _buildAddressAutocompleteField(),
      ],
    );
  }

  Widget _buildBusinessTypeDropdown(BuildContext context) {
    return Consumer(
      builder: (context, ref, _) {
        ref.listen(
          fetchOutletTypesProvider(params.address?.countryShort),
          (prev, next) {
            if (next.hasError) {
              _businessTypeController.text = '';
              Toast.apiError(
                next.error as AppException,
                context,
              );
            }
          },
        );
        final state = ref.watch(fetchOutletTypesProvider(countryCode));
        final isEnabled = countryCode != null;
        return SizedBox(
          width: double.infinity,
          child: DropdownMenu<RetailOutletType>(
            trailingIcon: state.maybeWhen(
              orElse: () => SvgPicture.asset(
                kChevronDownSvg,
                fit: BoxFit.cover,
              ),
              error: (_, __) => InkWell(
                onTap: () => ref.invalidate(fetchOutletTypesProvider),
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(2),
                  child: Text(
                    'Retry',
                    style:
                        textTheme.bodyMedium?.copyWith(color: Palette.primary),
                  ),
                ),
              ),
            ),
            controller: _businessTypeController,
            enabled: isEnabled,
            errorText: state.maybeWhen(
              orElse: () => _validateMode.value == AutovalidateMode.always &&
                      _businessTypeController.text.isEmpty
                  ? 'Business type is required'
                  : null,
              error: (_, __) => 'Something Went Wrong',
            ),
            hintText: state.maybeWhen(
              orElse: () => 'Business type',
              loading: () => 'Loading...',
            ),
            menuHeight: 300,
            expandedInsets: EdgeInsets.zero,
            textStyle: textTheme.bodyMedium,
            enableSearch: false,
            requestFocusOnTap: false,
            selectedTrailingIcon: state.maybeWhen(
              orElse: () => SvgPicture.asset(
                kChevronDownSvg,
                fit: BoxFit.cover,
              ),
              error: (_, __) => InkWell(
                onTap: () => ref.invalidate(fetchOutletTypesProvider),
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.all(2),
                  child: Text(
                    'Retry',
                    style:
                        textTheme.bodyMedium?.copyWith(color: Palette.primary),
                  ),
                ),
              ),
            ),
            inputDecorationTheme: InputDecorationTheme(
              fillColor: Palette.kE7E7E7,
              filled: !isEnabled,
              constraints: BoxConstraints(
                minHeight: defaultHeight,
                maxHeight: defaultHeight,
              ),
              // constraints: BoxConstraints.tight(
              //   Size.fromHeight(
              //     state.maybeWhen(
              //       orElse: () =>
              //           _validateMode.value == AutovalidateMode.always &&
              //                   _businessTypeController.text.isEmpty
              //               ? 74
              //               : 44,
              //       error: (_, __) => 74,
              //     ),
              //   ),
              // ),
            ),
            onSelected: (value) {
              params.setBusinessType = value;
              if (_validateMode.value == AutovalidateMode.always) {
                ref.invalidate(fetchOutletTypesProvider);
              }

              _isDisabled.value = !params.isComplete;
            },
            dropdownMenuEntries: state.maybeWhen(
              orElse: () => [],
              data: (types) => types
                  .map<DropdownMenuEntry<RetailOutletType>>(
                    (option) => DropdownMenuEntry<RetailOutletType>(
                      value: option,
                      label: option.name ?? 'Business type',
                      style: MenuItemButton.styleFrom(
                        foregroundColor: Palette.primaryBlack,
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddressAutocompleteField() {
    return AddressAutocompleteTextFormField(
      mapsApiKey: googleKey,
      debounceTime: 200,
      requiredField: true,
      controller: _addressController,
      reportValidationFailAndRequestFocus: (_) => true,
      onInitialSuggestionClick: (suggestion) {},
      onSuggestionClickGetTextToUseForControl: (place) => place.address,
      onSuggestionClick: (place) {
        params.setAddress = place;
        _isDisabled.value = !params.isComplete;
      },
      hoverColor: Palette.kE7E7E7,
      selectionColor: Palette.primary,
      buildItem: (Suggestion suggestion, int index) {
        return Container(
          margin: const EdgeInsets.all(2),
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerLeft,
          color: Colors.white,
          child: Text(
            suggestion.description,
            style: textTheme.bodyMedium,
          ),
        );
      },
      clearButton: Icon(
        Icons.close,
        size: 15,
        color: Palette.primaryBlack,
      ),
      language: 'en-Us',
      scrollPadding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      keyboardType: TextInputType.streetAddress,
      textCapitalization: TextCapitalization.words,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        hintText: 'Address',
        constraints: BoxConstraints(
          minHeight: defaultHeight,
          // maxHeight: defaultHeight,
        ),
      ),
    );
  }

  Widget _buildAddCustomerButton() {
    return SizedBox(
      width: double.maxFinite,
      child: CustomFilledButton(
        text: 'Add Customer',
        disabledNotifier: _isDisabled,
        loaderNotifier: _isLoading,
        onPressed: handleSubmit,
      ),
    );
  }

  Future<void> handleSubmit() async {
    final FormState form = _formKey.currentState!;
    if (params.address != null &&
        params.address?.address != _addressController.text) {
      Toast.error("Kindly select a valid address", context);
    } else if (form.validate()) {
      form.save();
      validatePhone();
    } else {
      _validateMode.value = AutovalidateMode.always;
      Toast.error("Kindly attend to the error(s)", context);
    }
  }

  Future<void> validatePhone() async {
    final normalizedNumber = await Validators.validatePhoneNumber(
        params.phoneNumber!, params.address?.countryShort ?? '');
    if (params.address?.countryShort != null && normalizedNumber != null) {
      params.setNumber = normalizedNumber;
      handleCreateCustomer();
    } else {
      if (mounted) {
        Toast.error(
            "Server could not validate phone number for selected address",
            context);
      }
    }
  }

  Future<void> handleCreateCustomer() async {
    _isLoading.value = true;
    final res = await ref.read(addNewCustomerUseCaseProvider(params));
    res.when(
      success: (outlet) {
        _isLoading.value = false;
        Navigator.of(context).pop(outlet);
        Toast.show('New Customer Added Successfully', context);
      },
      failure: (error, _) {
        _isLoading.value = false;
        Toast.apiError(error, context);
      },
    );
  }

  double get defaultHeight => 55;
  TextTheme get textTheme => Theme.of(context).textTheme;
}

class CustomerParams {
  CustomerParams();
  String? firstName;
  String? lastName;
  String? email;
  String? phoneNumber;
  String? outletName;
  RetailOutletType? businessType;
  Place? address;

  set setFirstName(value) => firstName = value;
  set setLastName(value) => lastName = value;
  set setEmail(value) => email = value;
  set setNumber(value) => phoneNumber = value;
  set setOutletName(value) => outletName = value;
  set setBusinessType(value) => businessType = value;
  set setAddress(Place value) => address = value;

  bool get isComplete {
    return firstName != null &&
        lastName != null &&
        email != null &&
        phoneNumber != null &&
        outletName != null &&
        businessType != null &&
        address != null;
  }

  Map<String, dynamic> toMap() {
    return {
      'name': outletName,
      'description': outletName,
      'country': address?.countryShort,
      'lga': address?.county,
      'user': {
        'firstName': firstName,
        'lastName': lastName,
        'phoneNumber': phoneNumber,
      },
      'address': {
        'company': outletName,
        'fullName': '$firstName $lastName',
        'address': address?.address,
        'city': address?.city,
        'lga': address?.county,
        'state': address?.state,
        'country': address?.countryShort,
        'postal': address?.zipCode,
        if (address?.lat != null && address?.lng != null)
          'coordinates': {
            'latitude': address?.lat,
            'longitude': address?.lng,
          },
      },
      'email': email,
      'phoneNumber': phoneNumber,
      'phone': phoneNumber,
      'outletTypeId': businessType?.id,
      'extChannel': 'SHOP.B2B',
      'domain': address?.countryShort == Country.britain.slug
          ? 'tradedepot'
          : 'shoptopup',
    };
  }
}

final fetchOutletTypesProvider =
    FutureProviderFamily<List<RetailOutletType>, String?>(
        (ref, arg) async => arg == null
            ? []
            : (await ref.read(
                fetchOutletsUseCaseProvider(arg),
              ))
                .extract());
