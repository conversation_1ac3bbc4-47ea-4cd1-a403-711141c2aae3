import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';

class ShowMoreButtonWidget extends ConsumerWidget {
  const ShowMoreButtonWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final showingOtherProducts = ref.watch(cartProvider).showingOtherProducts;
    return !showingOtherProducts
        ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Gap(8),
              TextButton(
                onPressed: () => ref
                    .read(cartProvider.notifier)
                    .setShowingOtherProducts(true),
                style: ButtonStyle(
                  shape: WidgetStateProperty.all<OutlinedBorder>(
                      const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(30.0),
                    ),
                  )),
                ),
                child: const Text('See More'),
              ),
              const Gap(40),
            ],
          )
        : const SizedBox.shrink();
  }
}
