import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/order_use_cases.dart';
import 'package:td_procurement/app/order/widgets/add_customer.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

/// Tracks focused field on export cart items
final kFocusedFieldProvider = StateProvider<FocusedField?>((_) => null);
final foundCustomersProvider = StateProvider<List<RetailOutlet>>((_) => []);
final selectedCustomerProvider = StateProvider<RetailOutlet?>((_) => null);
final supplierInventoryProvider =
    StateProvider<AsyncValue<List<Variant>>>((_) => const AsyncValue.data([]));

class OutletSearchAutoCompleteWidget extends ConsumerStatefulWidget {
  const OutletSearchAutoCompleteWidget(
      {super.key, this.isAdvanceInvoice = false});

  final bool isAdvanceInvoice;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      OutletSearchAutoCompleteWidgetState();
}

class OutletSearchAutoCompleteWidgetState
    extends ConsumerState<OutletSearchAutoCompleteWidget>
    with SingleTickerProviderStateMixin {
  final _outletController = TextEditingController();
  final _searchNotifier = ValueNotifier<bool>(false);
  final _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  OverlayEntry? _addCustomerOverlayEntry;
  Timer? _debounce;
  late AnimationController _controller;
  late Animation<double> _opacity;
  bool _showOutletAutocomplete = false;

  bool get isExportCountry =>
      ref.read(countryTypeProvider) == CountryType.export;

  bool get isAdvanceInvoice => widget.isAdvanceInvoice;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        duration: const Duration(milliseconds: 300), vsync: this);
    _opacity = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _outletController.dispose();
    _overlayEntry?.remove();
    _addCustomerOverlayEntry?.remove();
    _controller.dispose();
    super.dispose();
  }

  Future<void> fetchSupplierInventory(String? outletId) async {
    if (outletId == null) return;
    ref.read(supplierInventoryProvider.notifier).state =
        const AsyncValue.loading();
    final res = await ref.read(fetchSupplierInventoryUseCaseProvider(outletId));
    res.when(
      success: (data) => ref.read(supplierInventoryProvider.notifier).state =
          AsyncValue.data(data),
      failure: (error, _) => ref
          .read(supplierInventoryProvider.notifier)
          .state = const AsyncValue.data([]),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    ref.listen(selectedCustomerProvider, (_, selectedSalesCustomer) {
      // fetch supplier inventory for sales order
      if (!isAdvanceInvoice) {
        if (selectedSalesCustomer != null) {
          fetchSupplierInventory(selectedSalesCustomer.id);
        } else {
          ref.read(supplierInventoryProvider.notifier).state =
              const AsyncValue.data([]);
        }
      }
    });

    final selectedCustomer = ref.watch(selectedCustomerProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildHeader(textTheme),
        const Gap(8),
        if (selectedCustomer != null)
          _buildSelectedCustomerTile(selectedCustomer, textTheme)
        else
          _buildSearchField(textTheme),
        const Gap(10),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Text('Customer',
              style:
                  textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700)),
        ),
        TextButton(
          onPressed: handleAddCustomer,
          child: Text('New Customer',
              style: textTheme.bodySmall?.copyWith(color: Palette.primary)),
        ),
      ],
    );
  }

  Widget _buildSelectedCustomerTile(
      RetailOutlet selectedCustomer, TextTheme textTheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Flexible(
          flex: 3,
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            title: Text(selectedCustomer.outletBusinessName ?? '-',
                style:
                    textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600)),
            subtitle: Text(
              selectedCustomer.streetName ??
                  selectedCustomer.formattedAddress ??
                  '-',
              style: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500, color: Palette.blackSecondary),
            ),
          ),
        ),
        Flexible(
          flex: 1,
          child: IconButton(
            onPressed: () {
              _outletController.clear();
              ref.read(selectedCustomerProvider.notifier).state = null;
              ref.read(cartProvider.notifier).clearCart();
            },
            icon: SvgPicture.asset('$kSvgDir/order/edit.svg'),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchField(TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: CompositedTransformTarget(
        link: _layerLink,
        child: SizedBox(
          height: 36,
          child: TextField(
            controller: _outletController,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderSide: BorderSide(color: Palette.stroke),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Palette.primary),
                borderRadius: BorderRadius.circular(6),
              ),
              hintText: 'Search by phone number',
              hintStyle:
                  textTheme.bodySmall?.copyWith(color: Palette.placeholder),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
                      .copyWith(bottom: 40),
              suffixIcon: ValueListenableBuilder<bool>(
                valueListenable: _searchNotifier,
                builder: (context, isLoading, _) {
                  return isLoading
                      ? Row(
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                  valueColor:
                                      AlwaysStoppedAnimation(Palette.primary)),
                            ),
                            const Gap(8),
                          ],
                        )
                      : const SizedBox.shrink();
                },
              ),
              suffixIconConstraints: BoxConstraints.tight(const Size(30, 20)),
            ),
            textAlign: TextAlign.start,
            textAlignVertical: TextAlignVertical.center,
            cursorHeight: 14,
            onTap: () {
              if (_outletController.text.isNotEmpty &&
                  _showOutletAutocomplete) {
                _updateOverlay();
              }
            },
            onChanged: (text) {
              if (text.isNotEmpty) {
                if (_debounce?.isActive ?? false) _debounce?.cancel();
                _debounce = Timer(const Duration(milliseconds: 500), () {
                  handleSearch(text);
                });
              }
            },
          ),
        ),
      ),
    );
  }

  void handleAddCustomer() async {
    final outlet = await showCustomGeneralDialog<RetailOutlet?>(
      context,
      child: AddCustomerWidget(isAdvanceInvoice: isAdvanceInvoice),
      percentage: 0.4,
      minRightSectionWidth: 520,
    );

    if (outlet != null) {
      _outletController.clear();
      // set branch for sales order
      if (!isAdvanceInvoice) {
        ref.read(cartProvider.notifier).setBranch(RetailBranch(
              id: outlet.id ?? '',
              outletBusinessName: outlet.outletBusinessName ?? '',
              streetName: outlet.formattedAddress ?? outlet.streetName ?? '',
              contactPhone: outlet.contactPhone ?? '',
            ));
      }
      ref.read(selectedCustomerProvider.notifier).state = outlet;
    }
  }

  Future<void> handleSearch(String searchTerm) async {
    _removeOverlay();
    _searchNotifier.value = true;
    await _searchItems(searchTerm);
    _searchNotifier.value = false;

    setState(() {
      _showOutletAutocomplete = searchTerm.isNotEmpty;
    });

    _updateOverlay();
  }

  Future<List<RetailOutlet>> _searchItems(String searchTerm) async {
    List<RetailOutlet> results = [];
    ApiResponse<List<RetailOutlet>> res;

    // if (isAdvanceInvoice) {
    //   res = await ref.read(searchMerchantsUseCaseProvider(searchTerm));
    // } else {
    res = await ref.read(searchCustomersUseCaseProvider(searchTerm));
    // }

    res.when(
      success: (data) {
        results = data;
        ref.read(foundCustomersProvider.notifier).state = data;
      },
      failure: (error, _) {
        // do nothing
      },
    );
    return results;
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final searchedCustomers = ref.watch(foundCustomersProvider);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          if (_showOutletAutocomplete &&
              (_controller.status == AnimationStatus.forward ||
                  _controller.status == AnimationStatus.completed))
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeOverlay,
              ),
            ),
          Positioned(
            width: size.width,
            left: offset.dx,
            top: offset.dy + size.height,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: const Offset(0, 38),
              child: FadeTransition(
                opacity: _opacity,
                child: Visibility(
                  visible: _controller.status == AnimationStatus.completed ||
                      _controller.status == AnimationStatus.forward,
                  child: Material(
                    elevation: 0,
                    color: Colors.white,
                    child: Builder(builder: (context) {
                      final textTheme = Theme.of(context).textTheme;
                      return ConstrainedBox(
                        constraints:
                            const BoxConstraints(maxHeight: 200, minHeight: 50),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Palette.kE7E7E7),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.white,
                              boxShadow: const [
                                BoxShadow(
                                    color: Colors.black12,
                                    blurRadius: 4,
                                    offset: Offset(0, 2))
                              ],
                            ),
                            child: searchedCustomers.isNotEmpty
                                ? ListView.builder(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    physics: const BouncingScrollPhysics(),
                                    itemCount: searchedCustomers.length,
                                    itemBuilder: (context, index) {
                                      final searchedCustomer =
                                          searchedCustomers[index];
                                      return HoverableContainer(
                                        index: index,
                                        child: ListTile(
                                          title: Text(
                                              searchedCustomer
                                                      .outletBusinessName ??
                                                  '-',
                                              style: textTheme.bodyLarge
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.w600)),
                                          subtitle: Text(
                                            searchedCustomer.streetName ??
                                                searchedCustomer
                                                    .formattedAddress ??
                                                '-',
                                            style: textTheme.bodyMedium
                                                ?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                    color:
                                                        Palette.blackSecondary),
                                          ),
                                          onTap: () {
                                            _removeOverlay();
                                            ref
                                                .read(cartProvider.notifier)
                                                .setBranch(RetailBranch(
                                                  id: searchedCustomer.id ?? '',
                                                  outletBusinessName:
                                                      searchedCustomer
                                                              .outletBusinessName ??
                                                          '',
                                                  streetName: searchedCustomer
                                                          .formattedAddress ??
                                                      searchedCustomer
                                                          .streetName ??
                                                      '',
                                                  contactPhone: searchedCustomer
                                                          .contactPhone ??
                                                      '',
                                                ));
                                            ref
                                                .read(selectedCustomerProvider
                                                    .notifier)
                                                .state = searchedCustomer;
                                          },
                                        ),
                                      );
                                    },
                                  )
                                : const SizedBox.shrink(),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  OverlayEntry _createAddCustomerOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeAddCustomerOverlay,
            ),
          ),
          Positioned(
            width: size.width,
            left: offset.dx,
            top: offset.dy + size.height,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: const Offset(0, 38),
              child: Material(
                elevation: 0,
                color: Colors.white,
                child: Builder(builder: (context) {
                  final textTheme = Theme.of(context).textTheme;
                  return ListTile(
                    leading: const Icon(Icons.add),
                    title: Text(
                      'Add new delivery customer',
                      style: textTheme.bodyMedium,
                    ),
                    onTap: () {
                      _removeAddCustomerOverlay();
                      handleAddCustomer();
                    },
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _controller.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
        // Reset the animation to the initial state
        _controller.value = 0.0;
      });
    }
  }

  void _removeAddCustomerOverlay() {
    _addCustomerOverlayEntry?.remove();
    _addCustomerOverlayEntry = null;
    _controller.value = 0.0;
  }

  void _updateOverlay() {
    // Always remove the old overlay if it exists
    _removeOverlay();
    _removeAddCustomerOverlay();

    if (ref.read(foundCustomersProvider).isEmpty) {
      // Show add customer overlay if no search results
      _addCustomerOverlayEntry = _createAddCustomerOverlayEntry();
      Overlay.of(context).insert(_addCustomerOverlayEntry!);
      _controller.forward();
    } else {
      // Create a new overlay entry
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      // Start the fade-in animation
      _controller.forward();
    }
  }
}
