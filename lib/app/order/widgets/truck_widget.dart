import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class TruckWidget extends StatelessWidget {
  const TruckWidget({
    super.key,
    required this.name,
    // required this.division,
    required this.trucksRequired,
    required this.uLastTruck,
    required this.isLastIndex,
  });

  final String name;
  // final String division;
  final num trucksRequired;
  final num uLastTruck;
  final bool isLastIndex;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Expandable(
      header: Text(
        name.toUpperCase(),
        style: textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      content: Column(
        children: [
          _buildContentHeaders(textTheme),
          _buildContentBody(textTheme),
        ],
      ),
    );
  }

  Widget _buildContentHeaders(TextTheme textTheme) {
    final style = textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500);
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Expanded(
        //   flex: 1,
        //   child: Container(
        //     height: 35,
        //     decoration: BoxDecoration(
        //         border: Border(bottom: BorderSide(color: Palette.stroke))),
        //     child: Row(
        //       mainAxisAlignment: MainAxisAlignment.center,
        //       children: [
        //         Text(
        //           "Division",
        //           style: style,
        //           textAlign: TextAlign.center,
        //         ),
        //         Tooltip(
        //           message: '',
        //           preferBelow: false,
        //           child: SvgPicture.asset('$kSvgDir/order/info.svg'),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),
        Expanded(
          flex: 1,
          child: Container(
            height: 35,
            decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Palette.stroke))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Trucks required",
                  style: style,
                  textAlign: TextAlign.center,
                ),
                Tooltip(
                  message: 'Number of trucks needed',
                  preferBelow: false,
                  child: SvgPicture.asset('$kSvgDir/order/info.svg'),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          flex: 1,
          child: Container(
            height: 35,
            decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Palette.stroke))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Utilization of last truck",
                  style: style,
                  textAlign: TextAlign.center,
                ),
                Tooltip(
                  message: '% utilization of the last truck',
                  preferBelow: false,
                  child: SvgPicture.asset('$kSvgDir/order/info.svg'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentBody(TextTheme textTheme) {
    final style = textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w400);
    return Column(
      children: [
        Container(
          height: 40,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Palette.stroke),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // Expanded(
              //   child: Container(
              //     height: 40,
              //     decoration: BoxDecoration(
              //       border: Border(
              //         right: BorderSide(color: Palette.stroke),
              //       ),
              //     ),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceAround,
              //       children: [
              //         Text(division, style: style),
              //         // Text("40ft", style: style),
              //       ],
              //     ),
              //   ),
              // ),
              Expanded(
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(color: Palette.stroke),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text('$trucksRequired', style: style),
                      // Text("40ft", style: style),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Text('$uLastTruck%',
                        style: style?.copyWith(color: Colors.green)),
                    // Text("40ft", style: style),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Container(
        //   height: 40,
        //   decoration: isLastIndex
        //       ? BoxDecoration(
        //           border: Border(
        //             bottom: BorderSide(color: Palette.stroke),
        //           ),
        //         )
        //       : null,
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.spaceAround,
        //     children: [
        //       Expanded(
        //         // flex: 1,
        //         child: Container(
        //           height: 40,
        //           decoration: BoxDecoration(
        //             border: Border(
        //               right: BorderSide(color: Palette.stroke),
        //             ),
        //           ),
        //           child: Row(
        //             mainAxisAlignment: MainAxisAlignment.spaceAround,
        //             children: [
        //               Text('$trucksRequired', style: style),
        //               // Text('$cFortyQty', style: style),
        //             ],
        //           ),
        //         ),
        //       ),
        //       // Expanded(
        //       //   flex: 1,
        //       //   child: Row(
        //       //     mainAxisAlignment: MainAxisAlignment.spaceAround,
        //       //     children: [
        //       //       Text('$uTwentyQty%',
        //       //           style: style?.copyWith(color: Palette.kE61010)),
        //       //       Text('$uFortyQty%',
        //       //           style: style?.copyWith(color: Palette.k458558)),
        //       //     ],
        //       //   ),
        //       // ),
        //     ],
        //   ),
        // ),
      ],
    );
  }
}
