import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class SalesOrderSummaryTableWidget extends ConsumerStatefulWidget {
  const SalesOrderSummaryTableWidget(this.order, {super.key});

  final Order order;

  @override
  ConsumerState<SalesOrderSummaryTableWidget> createState() =>
      SalesOrderSummaryTableWidgetState();
}

class SalesOrderSummaryTableWidgetState
    extends ConsumerState<SalesOrderSummaryTableWidget> {
  late final order = widget.order;
  List<RetailBranch> branches = [];

  RetailBranch get branch =>
      branches.firstWhere((el) => el.id == widget.order.retailOutletId,
          orElse: () => branches.first);

  @override
  void initState() {
    super.initState();
    branches = ref.read(branchesProvider);
    // setPrintOrder();
  }

  // Future<void> setPrintOrder() async {
  //   if (transactionInView == null) return;
  //   _loading.value = true;
  //   final data = await processPrintOrder(
  //       transactionInView!, ref, widget.orderDetails?.orders ?? []);
  //   _printOrder.value = data;

  //   _loading.value = false;
  // }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final currencyCode = order.currency?.iso ?? kDefaultCurrency;
    final cal = processSalesPrintOrder(ref, order);
    final po = cal?.poOrderCalculation;
    final isDelivered = order.shippingStatus == 'delivered';
    final val = isDelivered ? 1 : 0;

    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.only(top: 30, bottom: 40),
      child: Column(
        children: [
          Container(
            constraints: const BoxConstraints(
              minHeight: 552,
              maxWidth: double.maxFinite,
              // maxWidth: 776,
            ),
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Palette.stroke, width: 1),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.04),
                  offset: const Offset(0, 2),
                  blurRadius: 2,
                  spreadRadius: -1,
                ),
              ],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Gap(5),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order ${order.orderNumber}',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          'Date   ${order.createdAt?.toDate() ?? ''}',
                          style: textTheme.bodyMedium?.copyWith(
                            color: Palette.blackSecondary,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const Gap(10),
                        SalesOrderStatusBadge(order.shippingStatus),
                      ],
                    ),
                    const Gap(20),
                    // Row(
                    //   crossAxisAlignment: CrossAxisAlignment.start,
                    //   children: [
                    //     Expanded(
                    //       flex: 1,
                    //       child: Text(
                    //         'From',
                    //         style: textTheme.bodyMedium?.copyWith(
                    //           color: Palette.blackSecondary,
                    //           fontWeight: FontWeight.w400,
                    //         ),
                    //       ),
                    //     ),
                    //     Expanded(
                    //       flex: 6,
                    //       child: Text(
                    //         'Tradedepot Inc, 60 Capitol way, Colindale, London NW90BR',
                    //         style: textTheme.bodyMedium,
                    //       ),
                    //     ),
                    //   ],
                    // ),
                    // const Gap(6),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Text(
                            'Deliver to',
                            style: textTheme.bodyMedium?.copyWith(
                              color: Palette.blackSecondary,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 6,
                          child: Text(
                              '${order.customerName},\n${order.address?.address1 ?? '-'}',
                              style: textTheme.bodyMedium),
                        ),
                      ],
                    ),
                  ],
                ),
                const Gap(20),
                Table(
                  columnWidths: {
                    0: const FlexColumnWidth(4),
                    if (!isDelivered) 1: const FlexColumnWidth(1),
                    if (isDelivered) 1: const FlexColumnWidth(1),
                    if (isDelivered) 2: const FlexColumnWidth(1),
                    (2 + val): const FlexColumnWidth(1),
                    (3 + val): const FlexColumnWidth(1),
                    (4 + val): const FlexColumnWidth(1),
                    (5 + val): const FlexColumnWidth(1),
                    (6 + val): const FlexColumnWidth(1),
                  },
                  // border: TableBorder(
                  //     horizontalInside: BorderSide(color: Colors.grey.shade300)),
                  children: [
                    TableRow(
                      children: [
                        buildTableHeader('Description', context),
                        if (!isDelivered) buildTableHeader('Qty', context),
                        if (isDelivered)
                          buildTableHeader('Ordered Qty', context),
                        if (isDelivered)
                          buildTableHeader('Delivered Qty', context),
                        buildTableHeader('Unit Price', context),
                        buildTableHeader('Discount', context),
                        buildTableHeader('Tax', context),
                        buildTableHeader('Amount', context),
                      ],
                    ),
                    TableRow(children: [
                      const SizedBox(height: 8),
                      const SizedBox(),
                      const SizedBox(),
                      const SizedBox(),
                      const SizedBox(),
                      if (isDelivered) const SizedBox(),
                      const SizedBox(),
                    ]),
                    // Table Items
                    for (final OrderItem item in (order.items ?? []))
                      buildTableRow(
                        isDelivered,
                        item.name ?? '',
                        item.quantity.toString(),
                        item.shippedQuantity.toString(),
                        CurrencyWidget.value(
                            context, currencyCode, item.price ?? 0),
                        CurrencyWidget.value(
                            context, currencyCode, item.discount ?? 0),
                        CurrencyWidget.value(
                            context, currencyCode, item.tax ?? 0),
                        CurrencyWidget.value(context, currencyCode,
                            ((item.quantity ?? 0) * (item.price ?? 0))),
                        // CurrencyWidget.value(
                        //   context,
                        //   currencyCode,
                        //   isDelivered
                        //       ? ((item.price ?? 0) *
                        //           (item.shippedQuantity ?? 0))
                        //       : item.total,
                        // ),
                        context,
                      ),
                  ],
                ),
                const Gap(18),
                Padding(
                  padding: const EdgeInsets.only(left: 120, right: 45),
                  child: Column(
                    children: [
                      TableSummaryRow(
                          'Subtotal',
                          CurrencyWidget.value(
                              context, currencyCode, po?.promoItemTotal ?? 0),
                          showBorder: false),
                      const Gap(8),
                      if (isExportCountry) ...[
                        TableSummaryRow(
                            'Shipping Fee',
                            CurrencyWidget.value(
                                context, currencyCode, po?.shippingFees ?? 0),
                            showBorder: false),
                        const Gap(8),
                      ],
                      // TableSummaryRow(
                      //     'Qty Shipped',
                      //     CurrencyWidget.value(
                      //         context, currencyCode, po?.quantityShipped ?? 0),
                      //     showBorder: false),
                      // const Gap(8),
                      // if ((cal?.discount ?? 0) > 0) ...[
                      TableSummaryRow(
                          'Discount',
                          CurrencyWidget.value(
                              context, currencyCode, po?.discount ?? 0),
                          showBorder: false),
                      const Gap(8),
                      // ],
                      if ((po?.taxes ?? 0) > 0) ...[
                        TableSummaryRow(
                            'Taxes',
                            CurrencyWidget.value(
                                context, currencyCode, po?.taxes ?? 0),
                            showBorder: false),
                        const Gap(8),
                      ],
                      if (po?.shippingAdjustment != null &&
                          po!.shippingAdjustment! != 0) ...[
                        TableSummaryRow(
                            'Shipping Adjustment',
                            CurrencyWidget.value(
                              context,
                              currencyCode,
                              po.shippingAdjustment!,
                              allowNegative: true,
                            ),
                            showBorder: false),
                        const Gap(8),
                      ],
                      TableSummaryRow(
                        'Total',
                        CurrencyWidget.value(
                            context, currencyCode, po?.promoTotal ?? 0),
                        showBorder: false,
                        useBoldFontWeight:
                            order.paymentStatus?.toLowerCase() == 'paid' ||
                                    order.shippingStatus?.toLowerCase() ==
                                        'delivered'
                                ? true
                                : false,
                      ),
                      const Gap(8),
                      if (order.paymentStatus?.toLowerCase() != 'paid' &&
                          order.shippingStatus?.toLowerCase() != 'delivered')
                        TableSummaryRow(
                            'Amount Due',
                            CurrencyWidget.value(
                                context, currencyCode, po?.amountDue ?? 0),
                            showBorder: false,
                            useBoldFontWeight: true),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildTableHeader(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
              fontWeight: FontWeight.w400,
            ),
      ),
    );
  }

  TableRow buildTableRow(
    bool isDelivered,
    String description,
    String qty,
    String shippedQty,
    String unitPrice,
    String discount,
    String tax,
    String amount,
    BuildContext context,
  ) {
    final textTheme = Theme.of(context).textTheme;
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            description,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        if (!isDelivered)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              qty,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
          ),
        if (isDelivered)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              qty,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
          ),
        if (isDelivered)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              shippedQty,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
          ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            unitPrice,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            discount,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            tax,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            amount,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
