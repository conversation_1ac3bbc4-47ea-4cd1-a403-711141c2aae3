import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/app/order/order_controller.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/multi_value_listenable_builder.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class OrderSummaryTableWidget extends ConsumerStatefulWidget {
  const OrderSummaryTableWidget(
      {super.key, this.orderDetails, this.transaction});

  final OrderDetails? orderDetails;
  final Transaction? transaction;

  @override
  ConsumerState<OrderSummaryTableWidget> createState() =>
      OrderSummaryTableWidgetState();
}

class OrderSummaryTableWidgetState
    extends ConsumerState<OrderSummaryTableWidget> {
  List<RetailBranch> branches = [];
  Transaction? transactionInView;
  late bool isDraftOrder;

  final _loading = ValueNotifier<bool>(false);
  final _printOrder = ValueNotifier<PrintOrderData?>(null);

  RetailBranch get branch =>
      branches.firstWhere((el) => el.id == transactionInView?.retailOutletId,
          orElse: () => branches.first);

  @override
  void initState() {
    super.initState();
    transactionInView = widget.transaction ??
        ref.read(orderControllerProvider).transactionInView;
    isDraftOrder = transactionInView?.isDraft ?? false;
    branches = ref.read(branchesProvider);

    setPrintOrder();
  }

  Future<void> setPrintOrder() async {
    if (transactionInView == null) return;
    _loading.value = true;
    final data = await processPrintOrder(
        transactionInView!, ref, widget.orderDetails?.orders ?? []);
    _printOrder.value = data;

    _loading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;

    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.only(top: 30, bottom: 40),
      child: MultiValueListenableBuilder<bool, PrintOrderData?, Null>(
        valueListenable1: _loading,
        valueListenable2: _printOrder,
        builder: (context, loading, printOrder, _, __) {
          final cal = printOrder?.poOrderCalculation;

          return Column(
            children: [
              Container(
                constraints: const BoxConstraints(
                  minHeight: 552,
                  maxWidth: double.maxFinite,
                  // maxWidth: 776,
                ),
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Palette.stroke, width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF000000).withValues(alpha: 0.04),
                      offset: const Offset(0, 2),
                      blurRadius: 2,
                      spreadRadius: -1,
                    ),
                  ],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Gap(5),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order ${printOrder?.poOrderReference ?? ''}',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              'Date    ${(printOrder?.poOrders != null && printOrder!.poOrders.isNotEmpty) ? printOrder.poOrders.first.createdAt?.toDate() : ''}',
                              style: textTheme.bodyMedium?.copyWith(
                                color: Palette.blackSecondary,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const Gap(10),
                            OrderStatusBadge(printOrder?.status),
                          ],
                        ),
                        const Gap(20),
                        // Row(
                        //   crossAxisAlignment: CrossAxisAlignment.start,
                        //   children: [
                        //     Expanded(
                        //       flex: 1,
                        //       child: Text(
                        //         'From',
                        //         style: textTheme.bodyMedium?.copyWith(
                        //           color: Palette.blackSecondary,
                        //           fontWeight: FontWeight.w400,
                        //         ),
                        //       ),
                        //     ),
                        //     Expanded(
                        //       flex: 6,
                        //       child: Text(
                        //         'Tradedepot Inc, 60 Capitol way, Colindale, London NW90BR',
                        //         style: textTheme.bodyMedium,
                        //       ),
                        //     ),
                        //   ],
                        // ),
                        // const Gap(6),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 1,
                              child: Text(
                                'Deliver to',
                                style: textTheme.bodyMedium?.copyWith(
                                  color: Palette.blackSecondary,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 6,
                              child: Text(
                                '${branch.outletBusinessName},\n${branch.streetName ?? ''}',
                                style: textTheme.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                        if (isExportCountry)
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                flex: 1,
                                child: Text(
                                  'Approval\nStatus',
                                  style: textTheme.bodyMedium?.copyWith(
                                    color: Palette.blackSecondary,
                                  ),
                                ),
                              ),
                              Expanded(
                                  flex: 6,
                                  child: Row(
                                    children: [
                                      Builder(
                                        builder: (context) {
                                          final color = printOrder
                                                      ?.approvalStatus
                                                      ?.toLowerCase() ==
                                                  'approved'
                                              ? HexColor('#21AC53')
                                              : HexColor('#117BFF');
                                          return Container(
                                            alignment: Alignment.center,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: color.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Text(
                                              capitalize(
                                                  printOrder?.approvalStatus),
                                              style: textTheme.bodySmall
                                                  ?.copyWith(
                                                      color: color,
                                                      fontWeight:
                                                          FontWeight.w500),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  )
                                  // child: Text(
                                  //   printOrder?.approvalStatus != null
                                  //       ? capitalize(printOrder?.approvalStatus)
                                  //       : '-',
                                  //   style: textTheme.bodyMedium
                                  //       ?.copyWith(fontWeight: FontWeight.w500),
                                  // ),
                                  ),
                            ],
                          ),
                      ],
                    ),
                    const Gap(20),
                    Table(
                      columnWidths: const {
                        0: FlexColumnWidth(4),
                        1: FlexColumnWidth(1),
                        2: FlexColumnWidth(1),
                        3: FlexColumnWidth(1),
                        4: FlexColumnWidth(1),
                        5: FlexColumnWidth(1),
                      },
                      // border: TableBorder(
                      //     horizontalInside: BorderSide(color: Colors.grey.shade300)),
                      children: [
                        TableRow(
                          children: [
                            buildTableHeader('Description', context),
                            buildTableHeader('Qty', context),
                            buildTableHeader('Unit Price', context),
                            buildTableHeader('Discount', context),
                            buildTableHeader('Tax', context),
                            buildTableHeader('Amount', context),
                          ],
                        ),
                        const TableRow(children: [
                          SizedBox(height: 8),
                          SizedBox(),
                          SizedBox(),
                          SizedBox(),
                          SizedBox(),
                          SizedBox(),
                        ]),
                        // Table Items
                        for (final PrintOrderItem order
                            in (printOrder?.poOrders ?? []))
                          buildTableRow(
                              order.name,
                              order.quantity.toString(),
                              CurrencyWidget.value(context,
                                  order.currency.iso ?? '', order.price),
                              CurrencyWidget.value(context,
                                  order.currency.iso ?? '', order.discount),
                              CurrencyWidget.value(
                                  context, order.currency.iso ?? '', order.tax),
                              CurrencyWidget.value(context,
                                  order.currency.iso ?? '', order.total),
                              context),
                      ],
                    ),
                    const Gap(18),
                    Padding(
                      padding: const EdgeInsets.only(left: 120, right: 45),
                      child: Column(
                        children: [
                          TableSummaryRow(
                              'Subtotal',
                              CurrencyWidget.value(
                                  context,
                                  printOrder?.currency.iso ?? '',
                                  cal?.promoItemTotal ?? 0),
                              showBorder: false),
                          const Gap(8),
                          if (isExportCountry) ...[
                            TableSummaryRow(
                                'Shipping Fee',
                                CurrencyWidget.value(
                                    context,
                                    printOrder?.currency.iso ?? '',
                                    cal?.shippingFees ?? 0),
                                showBorder: false),
                            const Gap(8),
                          ],
                          // if ((cal?.discount ?? 0) > 0) ...[
                          TableSummaryRow(
                              'Discount',
                              CurrencyWidget.value(
                                  context,
                                  printOrder?.currency.iso ?? '',
                                  cal?.discount ?? 0),
                              showBorder: false),
                          const Gap(8),
                          // ],
                          if ((cal?.taxes ?? 0) > 0) ...[
                            TableSummaryRow(
                                'Taxes',
                                CurrencyWidget.value(
                                    context,
                                    printOrder?.currency.iso ?? '',
                                    cal?.taxes ?? 0),
                                showBorder: false),
                            const Gap(8),
                          ],
                          TableSummaryRow(
                            'Total',
                            CurrencyWidget.value(
                                context,
                                printOrder?.currency.iso ?? '',
                                cal?.promoTotal ?? 0),
                            showBorder: false,
                            useBoldFontWeight: printOrder?.paymentStatus
                                            ?.toLowerCase() ==
                                        'paid' ||
                                    printOrder?.shippingStatus?.toLowerCase() ==
                                        'delivered'
                                ? true
                                : false,
                          ),
                          const Gap(8),
                          if (printOrder?.paymentStatus?.toLowerCase() !=
                                  'paid' &&
                              printOrder?.shippingStatus?.toLowerCase() !=
                                  'delivered')
                            TableSummaryRow(
                                isDraftOrder
                                    ? 'Estimated Amount'
                                    : 'Amount Due',
                                CurrencyWidget.value(
                                    context,
                                    printOrder?.currency.iso ?? '',
                                    cal?.amountDue ?? 0),
                                showBorder: false,
                                useBoldFontWeight: true),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget buildTableHeader(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
      ),
    );
  }

  TableRow buildTableRow(
    String description,
    String qty,
    String unitPrice,
    String discount,
    String tax,
    String amount,
    BuildContext context,
  ) {
    final textTheme = Theme.of(context).textTheme;
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            description,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            qty,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            unitPrice,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            discount,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            tax,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            amount,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
