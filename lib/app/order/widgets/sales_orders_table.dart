import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_procurement/app/order/options_provider.dart';
import 'package:td_procurement/app/order/order_controller.dart';
import 'package:td_procurement/app/order/order_utils.dart';
import 'package:td_procurement/app/order/screens/sales_order_summary.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class SalesOrdersTableWidget extends ConsumerStatefulWidget {
  final List<Order> orders;
  final List<Order>? selectedOrders;

  const SalesOrdersTableWidget(this.orders, this.selectedOrders, {super.key});

  @override
  ConsumerState<SalesOrdersTableWidget> createState() =>
      _SalesOrdersTableWidgetState();
}

class _SalesOrdersTableWidgetState
    extends ConsumerState<SalesOrdersTableWidget> {
  final loading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final orderNotifier = ref.read(orderControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildOrdersList(orderNotifier, textTheme),
        ),
      ],
    );
  }

  Widget _buildOrdersList(OrderController orderNotifier, TextTheme textTheme) {
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => _buildOrderItem(
              order: widget.orders[index],
              index: index,
              orderNotifier: orderNotifier,
              textTheme: textTheme,
            ),
            childCount: widget.orders.length,
          ),
        ),
      ],
    );
  }

  Widget _buildOrderItem({
    required Order order,
    required int index,
    required OrderController orderNotifier,
    required TextTheme textTheme,
  }) {
    return HoverableContainer(
      index: index,
      builder: (isHovered) => InkWell(
        onTap: () => _handleOrderTap(order, orderNotifier),
        child: _buildTableRow(order, isHovered, textTheme),
      ),
    );
  }

  Future<void> _handleOrderTap(
      Order order, OrderController orderNotifier) async {
    await orderNotifier.setSalesOrderInView(order);
    if (mounted) {
      context.goNamed(
        kSalesOrderSummaryRoute,
        pathParameters: {'id': order.id ?? ''},
      );
    }
  }

  Widget _buildTableRow(Order order, bool isHovered, TextTheme textTheme) {
    final canAssignDriver = _canAssignDriverToOrder(order);
    final selectedOrderNotifier = ref.read(optionsProvider.notifier);

    return Table(
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: _getColumnWidths(),
      children: [
        TableRow(
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Palette.stroke)),
          ),
          children: [
            _buildCheckboxCell(order, canAssignDriver, selectedOrderNotifier),
            _buildAmountCell(order, textTheme),
            _buildSummaryCell(order, textTheme),
            _buildCustomerNameCell(order, textTheme),
            _buildCreatedDateCell(order, textTheme),
            _buildAssignedDriverCell(order, canAssignDriver, textTheme),
            _buildPrintButton(order, isHovered),
            _buildMoreOptionsButton(order),
          ],
        ),
      ],
    );
  }

  bool _canAssignDriverToOrder(Order order) {
    return ['pending', 'skipped'].contains(order.shippingStatus?.toLowerCase());
  }

  Map<int, TableColumnWidth> _getColumnWidths() {
    return const {
      0: FlexColumnWidth(0.35),
      1: FlexColumnWidth(1.4),
      2: FlexColumnWidth(1.6),
      3: FlexColumnWidth(1.4),
      4: FlexColumnWidth(1.4),
      5: FlexColumnWidth(1.4),
      6: FlexColumnWidth(2),
      7: FlexColumnWidth(0.5),
    };
  }

  Widget _buildCheckboxCell(
    Order order,
    bool canAssignDriver,
    OptionsProvider selectedOrderNotifier,
  ) {
    if (!canAssignDriver) return Container();

    return Skeletonizer(
      enabled: ref.watch(orderControllerProvider).salesOrders.isLoading,
      child: Skeleton.shade(
        child: Checkbox(
          value: widget.selectedOrders!.contains(order),
          onChanged: (value) =>
              _handleCheckboxChange(value, order, selectedOrderNotifier),
        ),
      ),
    );
  }

  void _handleCheckboxChange(
      bool? value, Order order, OptionsProvider notifier) {
    if (value == true) {
      notifier.addItem(order);
    } else {
      notifier.removeItem(order);
    }
  }

  Widget _buildAmountCell(Order order, TextTheme textTheme) {
    return buildContentCell(
      CurrencyWidget.tableWidget(
        context,
        order.currency,
        order.total ?? 0,
        mainAxisAlignment: MainAxisAlignment.start,
        extra: const Gap(4),
      ),
      textTheme,
    );
  }

  Widget _buildSummaryCell(Order order, TextTheme textTheme) {
    final summary = order.summary ??
        order.items?.map((el) => el.name).join(", ") ??
        ''.toString();
    return buildContentCell(summary, textTheme);
  }

  Widget _buildCustomerNameCell(Order order, TextTheme textTheme) {
    return buildContentCell(order.customerName ?? '-', textTheme);
  }

  Widget _buildCreatedDateCell(Order order, TextTheme textTheme) {
    return buildContentCell(
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Text(
                order.createdAt?.toDayMonth() ?? '',
                style: textTheme.bodyMedium?.copyWith(
                  color: Palette.blackSecondary,
                ),
              ),
              const SizedBox(width: 5),
              SalesOrderStatusBadge(order.shippingStatus),
            ],
          ),
        ],
      ),
      textTheme,
    );
  }

  Widget _buildAssignedDriverCell(
    Order order,
    bool canAssignDriver,
    TextTheme textTheme,
  ) {
    return buildContentCell(
      Text(
        canAssignDriver ? 'Unassigned' : order.dispatchUser?.name ?? '-',
        style: textTheme.bodyMedium?.copyWith(
          color: canAssignDriver ? Palette.k6B797C : Palette.primaryBlack,
        ),
      ),
      textTheme,
    );
  }

  Widget _buildPrintButton(Order order, bool isHovered) {
    if (!isHovered) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 100,
            height: 35,
            child: CustomFilledButton(
              text: 'Print Order',
              loaderNotifier: loading,
              onPressed: () => _handlePrintOrder(order),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handlePrintOrder(Order order) async {
    loading.value = true;

    final printOrder = processSalesPrintOrder(ref, order);
    if (printOrder == null) {
      if (mounted) {
        loading.value = false;
        Toast.error('Error printing order', context, title: 'Print Error');
      }
      return;
    }

    final branch = RetailBranch(
      id: order.retailOutletId ?? order.retailerId ?? '',
      outletBusinessName: order.customerName ?? '',
      contactPhone: order.contactPhone,
    );

    final isExportCountry = ref.read(countryTypeProvider) == CountryType.export;
    final printer =
        OrderPrinter(printOrder, branch, isExportCountry, 'Sales Order');

    final pdfData = await printer.generatePdf();
    loading.value = false;
    await printer.printPdf(pdfData);
  }

  Widget _buildMoreOptionsButton(Order order) {
    return Align(
      alignment: Alignment.centerRight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          PopupMenuButton<String>(
            useRootNavigator: true,
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onSelected: (value) => _handleMenuSelection(value, order),
            itemBuilder: (context) => _buildMenuItems(order),
            color: Colors.white,
            tooltip: '',
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            padding: EdgeInsets.zero,
            menuPadding: EdgeInsets.zero,
            enableFeedback: false,
          ),
          const Gap(10),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value, Order order) {
    if (value == 'deliver') {
      _showDeliveryDialog(order);
    } else if (value == 'print') {
      _handlePrintOrder(order);
    } else if (value == 'copy') {
      Clipboard.setData(ClipboardData(
          text: order.orderNumber?.toString() ?? order.paymentRef.toString()));
      Toast.show("Order number copied to clipboard", context,
          duration: 2, title: '');
    }
  }

  List<PopupMenuItem<String>> _buildMenuItems(Order order) {
    final items = <PopupMenuItem<String>>[
      const PopupMenuItem(
        value: 'copy',
        child: ListTile(
          leading: Icon(Icons.copy_outlined),
          title: Text(
            'Copy Order Number',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
      const PopupMenuItem(
        value: 'print',
        child: ListTile(
          leading: Icon(Icons.print_outlined),
          title: Text(
            'Print Order',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
    ];

    if (isPendingOrSkipped(order.shippingStatus) &&
        order.paymentStatus?.toLowerCase() != 'paid') {
      items.add(
        const PopupMenuItem(
          value: 'deliver',
          child: ListTile(
            leading: Icon(Icons.check_circle_outline),
            title: Text(
              'Mark as Delivered',
              style: TextStyle(color: Colors.black),
            ),
          ),
        ),
      );
    }

    return items;
  }

  void _showDeliveryDialog(Order order) {
    showCustomGeneralDialog(
      context,
      percentage: 0.4,
      dismissible: false,
      child: OrderDeliveryWidget(
        order,
        onSuccess: () {
          final params =
              ref.read(orderControllerProvider).fetchSalesOrdersParams;
          ref.read(orderControllerProvider.notifier).fetchSalesOrders(
                params,
                forced: true,
              );
          Toast.success('Order has been shipped', context);
        },
      ),
    );
  }
}
