import 'package:flutter/material.dart';
import 'package:td_procurement/app/order/widgets/cart_quantity_field.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class GridItemQuantityPicker extends StatefulWidget {
  const GridItemQuantityPicker(
    this.quantity,
    this.maxValue, {
    super.key,
    required this.onQuantityChanged,
    this.focusBorder = true,
    this.cursorHeight,
    this.contentPadding,
    this.autoFocus = false,
    this.readOnly = false,
    this.height = 28,
    this.emptyIfZero = false,
    this.canReduceToZero = true,
    this.allowDecimal = true,
    this.restrictToAllowedDecimals = true,
    this.focusNode,
  });

  final num quantity;
  final num maxValue;
  final ValueChanged<dynamic> onQuantityChanged;
  final bool? focusBorder;
  final double? cursorHeight;
  final EdgeInsetsGeometry? contentPadding;
  final bool autoFocus;
  final bool readOnly;
  final double? height;
  final bool allowDecimal;
  final bool restrictToAllowedDecimals;
  final FocusNode? focusNode;

  /// Determines whether to display the quantity as empty when it is zero.
  ///
  /// If set to `true`, the quantity will be hidden or left blank instead
  /// of showing "0".
  final bool emptyIfZero;

  /// Indicates whether it's okay to reduce the item's quantity all the
  /// way down to zero.
  ///
  /// This is handy for cases where completely running out of an item
  /// (e.g., removing it from the cart) is allowed.
  final bool canReduceToZero;

  @override
  State<GridItemQuantityPicker> createState() => _GridItemQuantityPickerState();
}

class _GridItemQuantityPickerState extends State<GridItemQuantityPicker> {
  late num quantity;
  late final num validMinimum;

  @override
  void initState() {
    super.initState();
    quantity = widget.quantity;
    validMinimum = widget.canReduceToZero ? 0 : 1;
  }

  @override
  void didUpdateWidget(covariant GridItemQuantityPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.quantity != widget.quantity) {
      setState(() {
        quantity = widget.quantity;
      });
    }
  }

  void _decrementQuantity() {
    setState(() {
      quantity = quantity.ceil();
      if (quantity > validMinimum) {
        quantity--;
        widget.onQuantityChanged(quantity);
      }
    });
  }

  void _incrementQuantity() {
    final newQuantity = quantity.floor();
    if (newQuantity == widget.maxValue) {
      return Toast.error(
        'Only ${widget.maxValue} available in stock',
        context,
        title: 'Available quantity exceeded',
        duration: 3,
      );
    }
    setState(() {
      quantity = quantity.floor();
      quantity++;
      widget.onQuantityChanged(quantity);
    });
  }

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      ignoring: widget.readOnly,
      child: Container(
        height: widget.height!,
        decoration: BoxDecoration(
          color: Palette.stroke,
          border: Border.all(color: Palette.kE7E7E7),
          boxShadow: const [
            BoxShadow(
              blurRadius: 2,
              spreadRadius: -1,
              offset: Offset(0, 2),
              color: Palette.k0000000A,
            ),
          ],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildActionButton(Icons.remove, _decrementQuantity),
            _buildQuantityInputField(),
            _buildActionButton(Icons.add, _incrementQuantity),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(IconData icon, VoidCallback onTap) {
    return Expanded(
      flex: 2,
      child: InkWell(
        onTap: onTap,
        child: Container(
          height: double.maxFinite,
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: Palette.stroke,
            borderRadius: BorderRadius.circular(8).copyWith(
              topRight: icon == Icons.remove ? Radius.zero : null,
              bottomRight: icon == Icons.remove ? Radius.zero : null,
              topLeft: icon == Icons.add ? Radius.zero : null,
              bottomLeft: icon == Icons.add ? Radius.zero : null,
            ),
          ),
          child: Icon(icon),
        ),
      ),
    );
  }

  Widget _buildQuantityInputField() {
    return Expanded(
      flex: 4,
      child: widget.emptyIfZero
          ? const SizedBox.shrink()
          : CartQuantityInputField(
              focusBorder: widget.focusBorder!,
              focusNode: widget.focusNode,
              initialCount: quantity,
              maxValue: widget.maxValue,
              autoFocus: widget.autoFocus,
              readOnly: widget.readOnly,
              allowDecimal: widget.allowDecimal,
              restrictToAllowedDecimals: widget.restrictToAllowedDecimals,
              onQuantityChanged: (value) {
                if ((num.tryParse(value.toString()) ?? 0) >= validMinimum) {
                  quantity = num.tryParse(value.toString()) ?? 0;
                  widget.onQuantityChanged(value);
                }
              },
              cursorHeight: widget.cursorHeight ?? 14,
            ),
    );
  }
}

// class GridItemQuantityPicker extends StatefulWidget {
//   const GridItemQuantityPicker(this.quantity, this.maxValue,
//       {super.key,
//       required this.onQuantityChanged,
//       this.focusBorder = true,
//       this.cursorHeight,
//       this.contentPadding,
//       this.autoFocus = false,
//       this.readOnly = false,
//       this.height = 28,
//       this.emptyIfZero = false,
//       this.canReduceToZero = true,
//       this.allowDecimal = true,
//       this.restrictToAllowedDecimals = true,
//       this.focusNode});

//   final num quantity;
//   final num maxValue;
//   final ValueChanged<dynamic> onQuantityChanged;
//   final bool? focusBorder;
//   final double? cursorHeight;
//   final EdgeInsetsGeometry? contentPadding;
//   final bool autoFocus;
//   final bool readOnly;
//   final double? height;
//   final bool allowDecimal;
//   final bool restrictToAllowedDecimals;
//   final FocusNode? focusNode;

//   /// Determines whether to display the quantity as empty when it is zero.
//   ///
//   /// If set to `true`, the quantity will be hidden or left blank instead
//   /// of showing "0".
//   final bool emptyIfZero;

//   /// Indicates whether it's okay to reduce the item's quantity all the
//   /// way down to zero.
//   ///
//   /// This is handy for cases where completely running out of an item
//   /// (e.g., removing it from the cart) is allowed.
//   final bool canReduceToZero;

//   @override
//   State<GridItemQuantityPicker> createState() => _GridItemQuantityPickerState();
// }

// class _GridItemQuantityPickerState extends State<GridItemQuantityPicker> {
//   late num quantity = widget.quantity;
//   late final num validMinimum = widget.canReduceToZero ? 0 : 1;

//   @override
//   void didUpdateWidget(covariant GridItemQuantityPicker oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.quantity != widget.quantity) {
//       setState(() {
//         quantity = widget.quantity;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return IgnorePointer(
//       ignoring: widget.readOnly,
//       child: Container(
//         height: widget.height!,
//         decoration: BoxDecoration(
//           color: Palette.stroke,
//           border: Border.all(color: Palette.kE7E7E7),
//           boxShadow: const [
//             BoxShadow(
//                 blurRadius: 2,
//                 spreadRadius: -1,
//                 offset: Offset(0, 2),
//                 color: Palette.k0000000A)
//           ],
//           borderRadius: BorderRadius.circular(8),
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Expanded(
//               flex: 2,
//               child: InkWell(
//                 onTap: () {
//                   setState(() {
//                     // Ensure quantity is an integer before subtracting
//                     quantity = quantity.ceil();

//                     if (quantity > validMinimum) {
//                       quantity--;
//                       widget.onQuantityChanged(quantity);
//                     }
//                   });
//                 },
//                 child: Container(
//                   height: double.maxFinite,
//                   width: double.maxFinite,
//                   decoration: BoxDecoration(
//                     color: Palette.stroke,
//                     borderRadius: BorderRadius.circular(8).copyWith(
//                       topRight: Radius.zero,
//                       bottomRight: Radius.zero,
//                     ),
//                   ),
//                   child: const Icon(Icons.remove),
//                 ),
//               ),
//             ),
//             Expanded(
//               flex: 4,
//               child: widget.emptyIfZero
//                   ? const SizedBox.shrink()
//                   : CartQuantityInputField(
//                       focusBorder: widget.focusBorder!,
//                       focusNode: widget.focusNode,
//                       initialCount: quantity,
//                       maxValue: widget.maxValue,
//                       autoFocus: widget.autoFocus,
//                       readOnly: widget.readOnly,
//                       allowDecimal: widget.allowDecimal,
//                       restrictToAllowedDecimals:
//                           widget.restrictToAllowedDecimals,
//                       onQuantityChanged: (value) {
//                         // prevents setting quantity to zero in cart
//                         if ((num.tryParse(value.toString()) ?? 0) >=
//                             validMinimum) {
//                           quantity = num.tryParse(value.toString()) ?? 0;
//                           widget.onQuantityChanged(value);
//                         }
//                       },
//                       cursorHeight: widget.cursorHeight ?? 14,
//                     ),
//             ),
//             Expanded(
//               flex: 2,
//               child: InkWell(
//                 onTap: () {
//                   final newQuantity = quantity.floor();

//                   if (newQuantity == widget.maxValue) {
//                     return Toast.error(
//                       'Only ${widget.maxValue} available in stock',
//                       context,
//                       title: 'Available quantity exceeded',
//                       duration: 3,
//                     );
//                   }

//                   setState(() {
//                     // Ensure quantity is an integer before adding
//                     quantity = quantity.floor();
//                     quantity++;
//                     widget.onQuantityChanged(quantity);
//                   });
//                 },
//                 child: Container(
//                   height: double.maxFinite,
//                   width: double.maxFinite,
//                   decoration: BoxDecoration(
//                     color: Palette.stroke,
//                     borderRadius: BorderRadius.circular(8).copyWith(
//                       topLeft: Radius.zero,
//                       bottomLeft: Radius.zero,
//                     ),
//                   ),
//                   child: const Icon(Icons.add),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
