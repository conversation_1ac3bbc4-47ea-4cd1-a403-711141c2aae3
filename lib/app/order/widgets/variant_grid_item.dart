import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class VariantGridItem extends ConsumerWidget {
  const VariantGridItem(
    this.variant, {
    super.key,
    this.useEditingCart = true,
    this.quantityPickerWidth,
    this.allowAddInitial,
    this.onHomeScreen = false,
    this.ignoreQuantityLimits = false,
  });

  final Variant variant;
  final bool useEditingCart;
  final double? quantityPickerWidth;
  final bool? allowAddInitial;
  final bool onHomeScreen;
  final bool ignoreQuantityLimits;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: CachedImage(variant.variantId, ImageSize.large),
          ),
          const Gap(10),
          Expanded(
            flex: 3,
            child: _VariantDetails(
              variant: variant,
              textTheme: textTheme,
              quantityPickerWidth: quantityPickerWidth,
              actionButton: actionButton(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget actionButton(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        ref.watch(editingCartProvider);
        return VariantQuantityPicker(
          // key: UniqueKey(),
          variant,
          useEditingCart: useEditingCart,
          ignoreQuantityLimits: ignoreQuantityLimits,
        );
      },
    );
  }
}

class _VariantDetails extends StatelessWidget {
  const _VariantDetails({
    required this.variant,
    required this.textTheme,
    required this.quantityPickerWidth,
    required this.actionButton,
  });

  final Variant variant;
  final TextTheme textTheme;
  final double? quantityPickerWidth;
  final Widget actionButton;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        VariantPriceWidget(
          variant,
          textStyle: const TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 14.0,
          ),
        ),
        const Gap(4),
        Flexible(
          child: SizedBox(
            height: ((textTheme.bodyMedium?.fontSize ?? 14) * 1.5) * 3,
            child: Text(
              variant.name ?? '',
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
                height: 1.5,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        const Gap(4),
        if (quantityPickerWidth != null)
          ConstrainedBox(
            constraints: BoxConstraints.loose(Size(quantityPickerWidth!, 38)),
            child: actionButton,
          )
        else
          actionButton,
      ],
    );
  }
}

// class VariantGridItem extends ConsumerWidget {
//   const VariantGridItem(
//     this.variant, {
//     super.key,
//     this.useEditingCart = true,
//     this.quantityPickerWidth,
//     this.allowAddInitial,
//     this.onHomeScreen = false,
//     this.ignoreQuantityLimits = false,
//   });

//   final Variant variant;
//   final bool useEditingCart;
//   final double? quantityPickerWidth;
//   final bool? allowAddInitial;
//   final bool onHomeScreen;
//   final bool ignoreQuantityLimits;

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final textTheme = Theme.of(context).textTheme;

//     return Container(
//       padding: const EdgeInsets.all(0),
//       // color: Colors.red,
//       child: Row(
//         // crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Expanded(
//             flex: 2,
//             child: CachedImage(variant.variantId, ImageSize.large),
//           ),
//           const Gap(10),
//           Expanded(
//             flex: 3,
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 VariantPriceWidget(variant,
//                     textStyle: const TextStyle(
//                         fontWeight: FontWeight.w700, fontSize: 14.0)),
//                 // CurrencyWidget(
//                 //   variant.vatPrice ?? variant.price ?? 0,
//                 //   variant.currency?.iso ?? defaultCurrency.iso!,
//                 //   amountStyle: const TextStyle(
//                 //       fontWeight: FontWeight.w700, fontSize: 14.0),
//                 // ),
//                 const Gap(4),
//                 Flexible(
//                   child: SizedBox(
//                     height: ((textTheme.bodyMedium?.fontSize ?? 14) * 1.5) *
//                         3, // Calculate height for 3 lines
//                     child: Text(
//                       variant.name ?? '',
//                       style: textTheme.bodyMedium?.copyWith(
//                         color: Palette.blackSecondary,
//                         height: 1.5,
//                       ),
//                       maxLines: 3,
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                   ),
//                 ),
//                 const Gap(4),
//                 quantityPickerWidth != null
//                     ? ConstrainedBox(
//                         constraints: BoxConstraints.loose(
//                             Size(quantityPickerWidth!, 38)),
//                         child: actionButton(ref, context))
//                     : actionButton(ref, context)
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget actionButton(WidgetRef ref, BuildContext context) {
//     return VariantQuantityPicker(
//       variant,
//       useEditingCart: useEditingCart,
//       ignoreQuantityLimits: ignoreQuantityLimits,
//     );

//     // return ref.read(countryTypeProvider) != CountryType.export
//     //     ? VariantQuantityPicker(
//     //         variant,
//     //         useEditingCart: useEditingCart,
//     //         ignoreQuantityLimits: ignoreQuantityLimits,
//     //       )
//     //     : SizedBox(
//     //         height: 32,
//     //         width: 64,
//     //         child: CustomFilledButton(
//     //           onPressed: () {
//     //             showCustomGeneralDialog(
//     //               context,
//     //               child: VariantDetailsWidget(variant),
//     //               percentage: 0.44,
//     //               minRightSectionWidth: 630,
//     //             );
//     //           },
//     //           text: 'View',
//     //         ),
//     //       );
//   }
// }
