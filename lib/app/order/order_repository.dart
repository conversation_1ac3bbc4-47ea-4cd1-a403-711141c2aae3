import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/collection.dart';
import 'package:td_commons_flutter/models/order_confirmation.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/widgets/add_customer.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

import 'order_data_source.dart';
import 'order_params.dart';

final orderRepoProvider = Provider.autoDispose<OrderRepo>((ref) {
  final dataSource = ref.read(orderDataSourceProvider);
  return OrderRepoImplementation(dataSource, ref);
});

abstract class OrderRepo {
  Future<ApiResponse<FetchTransactionsResponse>> fetchOrders(
      FetchTransactionsParam params);
  Future<ApiResponse<OrderDetails>> fetchOrderDetails(String orderId);
  Future<ApiResponse<ShipmentResult>> fetchShipment(String shipmentId);
  Future<ApiResponse<void>> deleteOrder(String transactionId, String outletId);
  Future<ApiResponse<List<Variant>>> fetchExportVariants();
  Future<ApiResponse<List<Collection>>> fetchCollections(
      String hexCode, bool exclusive);
  Future<ApiResponse<List<Variant>>> fetchOutletVariants();
  Future<ApiResponse<List<Variant>>> searchOutletVariants(
      String hexCode, String searchTerm);
  Future<ApiResponse<OrderPreviewDetail>> prepareNonExportOrder(
      PrepareOrderParams params);
  Future<ApiResponse<OrderPreviewDetail>> prepareExportOrder(
      PrepareOrderParams params);
  Future<ApiResponse<OrderConfirmation>> fetchShippingRate(
      ShippingRateParams params);
  Future<ApiResponse<void>> createDraftOrder(String outletId);
  Future<ApiResponse<void>> updateDraftOrder(UpdateDraftParams params);
  Future<ApiResponse<String>> createNonExportCart(CreateOrderParams params);
  Future<ApiResponse<CreateOrderResponse>> createNonExportOrder(
      CreateOrderParams params);
  Future<ApiResponse<RetailOutlet>> getRetailOutlet(String outletId);
  Future<ApiResponse<void>> chargeOrder(ChargeOrderParams params);
  Future<ApiResponse<dynamic>> createExportOrder(CreateOrderParams params);
  Future<ApiResponse<dynamic>> createSalesOrder(CreateOrderParams params);
  Future<ApiResponse<FetchSalesOrdersResponse>> fetchSalesOrders(
      FetchSalesOrdersParams params);
  Future<ApiResponse<Order>> getSalesOrder(String orderId);
  Future<ApiResponse<List<RetailOutlet>>> searchCustomers(String phoneNumber);
  Future<ApiResponse<List<Variant>>> fetchSupplierInventory(String outletId);
  Future<ApiResponse<dynamic>> acceptOrder(String reference);
  Future<ApiResponse<dynamic>> markOrderAsDelivered(Map<String, dynamic> items);
  Future<ApiResponse<RetailOutlet>> addNewCustomer(CustomerParams params);
  Future<ApiResponse<FetchSalesLocationDriversResponse>>
      fetchSalesLocationDrivers(FetchSalesLocationDriverParams params);
  Future<ApiResponse<dynamic>> assignDriverToOrders(AssignOrdersParams params);
}

class OrderRepoImplementation implements OrderRepo {
  final OrderDataSource _orderDataSource;
  final Ref _ref;

  OrderRepoImplementation(this._orderDataSource, this._ref);

  @override
  Future<ApiResponse<FetchTransactionsResponse>> fetchOrders(
      FetchTransactionsParam params) async {
    return dioInterceptor(() => _orderDataSource.fetchOrders(params), _ref);
  }

  @override
  Future<ApiResponse<OrderDetails>> fetchOrderDetails(String orderId) async {
    return dioInterceptor(
        () => _orderDataSource.fetchOrderDetails(orderId), _ref);
  }

  @override
  Future<ApiResponse<ShipmentResult>> fetchShipment(String shipmentId) async {
    return dioInterceptor(
        () => _orderDataSource.fetchShipment(shipmentId), _ref);
  }

  @override
  Future<ApiResponse<void>> deleteOrder(
      String transactionId, String outletId) async {
    return dioInterceptor(
        () => _orderDataSource.deleteOrder(transactionId, outletId), _ref);
  }

  @override
  Future<ApiResponse<List<Variant>>> fetchExportVariants() async {
    return dioInterceptor(() => _orderDataSource.fetchExportVariants(), _ref);
  }

  @override
  Future<ApiResponse<List<Collection>>> fetchCollections(
      String hexCode, bool exclusive) async {
    return dioInterceptor(
        () => _orderDataSource.fetchCollections(hexCode, exclusive), _ref);
  }

  @override
  Future<ApiResponse<List<Variant>>> fetchOutletVariants() async {
    return dioInterceptor(() => _orderDataSource.fetchOutletVariants(), _ref);
  }

  @override
  Future<ApiResponse<List<Variant>>> searchOutletVariants(
      String hexCode, String searchTerm) async {
    return dioInterceptor(
        () => _orderDataSource.searchOutletVariants(hexCode, searchTerm), _ref);
  }

  @override
  Future<ApiResponse<OrderPreviewDetail>> prepareNonExportOrder(
      PrepareOrderParams params) async {
    return dioInterceptor(
        () => _orderDataSource.prepareNonExportOrder(params), _ref);
  }

  @override
  Future<ApiResponse<OrderPreviewDetail>> prepareExportOrder(
      PrepareOrderParams params) async {
    return dioInterceptor(
        () => _orderDataSource.prepareExportOrder(params), _ref);
  }

  @override
  Future<ApiResponse<OrderConfirmation>> fetchShippingRate(
      ShippingRateParams params) async {
    return dioInterceptor(
        () => _orderDataSource.fetchShippingRate(params), _ref);
  }

  @override
  Future<ApiResponse<void>> createDraftOrder(String outletId) async {
    return dioInterceptor(
        () => _orderDataSource.createDraftOrder(outletId), _ref);
  }

  @override
  Future<ApiResponse<void>> updateDraftOrder(UpdateDraftParams params) async {
    return dioInterceptor(
        () => _orderDataSource.updateDraftOrder(params), _ref);
  }

  @override
  Future<ApiResponse<String>> createNonExportCart(
      CreateOrderParams params) async {
    return dioInterceptor(
        () => _orderDataSource.createNonExportCart(params), _ref);
  }

  @override
  Future<ApiResponse<CreateOrderResponse>> createNonExportOrder(
      CreateOrderParams params) async {
    return dioInterceptor(
        () => _orderDataSource.createNonExportOrder(params), _ref);
  }

  @override
  Future<ApiResponse<RetailOutlet>> getRetailOutlet(String outletId) async {
    return dioInterceptor(
        () => _orderDataSource.getRetailOutlet(outletId), _ref);
  }

  @override
  Future<ApiResponse<void>> chargeOrder(ChargeOrderParams params) async {
    return dioInterceptor(() => _orderDataSource.chargeOrder(params), _ref);
  }

  @override
  Future<ApiResponse> createExportOrder(CreateOrderParams params) async {
    return dioInterceptor(
        () => _orderDataSource.createExportOrder(params), _ref);
  }

  @override
  Future<ApiResponse<dynamic>> createSalesOrder(CreateOrderParams params) {
    return dioInterceptor(
        () => _orderDataSource.createSalesOrder(params), _ref);
  }

  @override
  Future<ApiResponse<FetchSalesOrdersResponse>> fetchSalesOrders(
      FetchSalesOrdersParams params) async {
    return dioInterceptor(
        () => _orderDataSource.fetchSalesOrders(params), _ref);
  }

  @override
  Future<ApiResponse<Order>> getSalesOrder(String orderId) async {
    return dioInterceptor(() => _orderDataSource.getSalesOrder(orderId), _ref);
  }

  @override
  Future<ApiResponse<List<RetailOutlet>>> searchCustomers(
      String phoneNumber) async {
    return dioInterceptor(
        () => _orderDataSource.searchCustomers(phoneNumber), _ref);
  }

  @override
  Future<ApiResponse<List<Variant>>> fetchSupplierInventory(
      String outletId) async {
    return dioInterceptor(
        () => _orderDataSource.fetchSupplierInventory(outletId), _ref);
  }

  @override
  Future<ApiResponse<dynamic>> acceptOrder(String reference) async {
    return dioInterceptor(() => _orderDataSource.acceptOrder(reference), _ref);
  }

  @override
  Future<ApiResponse<dynamic>> markOrderAsDelivered(
      Map<String, dynamic> items) async {
    return dioInterceptor(
        () => _orderDataSource.markOrderAsDelivered(items), _ref);
  }

  @override
  Future<ApiResponse<RetailOutlet>> addNewCustomer(
      CustomerParams params) async {
    return dioInterceptor(() => _orderDataSource.addNewCustomer(params), _ref);
  }

  @override
  Future<ApiResponse<FetchSalesLocationDriversResponse>>
      fetchSalesLocationDrivers(FetchSalesLocationDriverParams params) async {
    return dioInterceptor(
        () => _orderDataSource.fetchSalesLocationDrivers(params), _ref);
  }

  @override
  Future<ApiResponse<dynamic>> assignDriverToOrders(
      AssignOrdersParams params) async {
    return dioInterceptor(
        () => _orderDataSource.assignDriverToOrders(params), _ref);
  }
}
