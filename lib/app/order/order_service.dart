import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/collection.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

import 'order_params.dart';
import 'order_use_cases.dart';

final orderServiceProvider = Provider.autoDispose<OrderService>((ref) {
  final fetchOrdersUseCase = ref.read(fetchOrdersUseCaseProvider);
  final fetchOrderDetailsUseCase = ref.read(fetchOrderDetailsUseCaseProvider);
  final fetchExportVariantsUseCase =
      ref.read(fetchExportVariantsUseCaseProvider);
  final fetchCollectionsUseCase = ref.read(fetchCollectionsUseCaseProvider);
  final fetchOutletVariantsUseCase =
      ref.read(fetchOutletVariantsUseCaseProvider);
  final searchOutletVariantsUseCase =
      ref.read(searchOutletVariantsUseCaseProvider);
  final fetchSalesOrdersUseCase = ref.read(fetchSalesOrdersUseCaseProvider);
  final fetchSalesLocationDriversUseCase =
      ref.read(fetchSalesLocationDriversUseCaseProvider);
  // final deleteOrderUseCase = ref.read(deleteOrderUseCaseProvider);

  return OrderServiceImplementation(
      fetchOrdersUseCase,
      fetchOrderDetailsUseCase,
      fetchExportVariantsUseCase,
      fetchCollectionsUseCase,
      fetchOutletVariantsUseCase,
      searchOutletVariantsUseCase,
      fetchSalesOrdersUseCase,
      fetchSalesLocationDriversUseCase
      // deleteOrderUseCase,
      );
});

abstract class OrderService {
  Future<ApiResponse<FetchTransactionsResponse>> fetchOrders(
      FetchTransactionsParam params);
  Future<ApiResponse<OrderDetails>> fetchOrderDetails(String orderId);
  Future<ApiResponse<List<Variant>>> fetchExportVariants();
  Future<ApiResponse<List<Collection>>> fetchNonExportCollections(
      String hexCode, bool exclusive);
  Future<ApiResponse<List<Variant>>> fetchNonExportOutletVariants();
  Future<ApiResponse<List<Variant>>> searchNonExportOutletVariants(
      String hexCode, String searchTerm);
  Future<ApiResponse<FetchSalesOrdersResponse>> fetchSalesOrders(
      FetchSalesOrdersParams params);
  Future<ApiResponse<FetchSalesLocationDriversResponse>>
      fetchSalesLocationDrivers(FetchSalesLocationDriverParams params);
  // Future<ApiResponse<void>> deleteOrder(String transactionId, String outletId);
}

class OrderServiceImplementation implements OrderService {
  OrderServiceImplementation(
    this._fetchOrdersUseCase,
    this._fetchOrderDetailsUseCase,
    this._fetchExportVariantsUseCase,
    this._fetchCollectionsUseCase,
    this._fetchOutletVariantsUseCase,
    this._searchOutletVariantsUseCase,
    this._fetchSalesOrdersUseCase,
    this._fetchSalesLocationDriversUseCase,
    // this._deleteOrderUseCase,
  );

  final FetchOrdersUseCase _fetchOrdersUseCase;
  final FetchOrderDetailsUseCase _fetchOrderDetailsUseCase;
  final FetchExportVariantsUseCase _fetchExportVariantsUseCase;
  final FetchCollectionsUseCase _fetchCollectionsUseCase;
  final FetchOutletVariantsUseCase _fetchOutletVariantsUseCase;
  final SearchOutletVariantsUseCase _searchOutletVariantsUseCase;
  final FetchSalesOrdersUseCase _fetchSalesOrdersUseCase;
  final FetchSalesLocationDriversUseCase _fetchSalesLocationDriversUseCase;
  // final DeleteOrderUseCase _deleteOrderUseCase;

  @override
  Future<ApiResponse<FetchTransactionsResponse>> fetchOrders(
      FetchTransactionsParam params) async {
    return _fetchOrdersUseCase.call(params);
  }

  @override
  Future<ApiResponse<OrderDetails>> fetchOrderDetails(String orderId) async {
    return _fetchOrderDetailsUseCase.call(orderId);
  }

  @override
  Future<ApiResponse<List<Variant>>> fetchExportVariants() async {
    return _fetchExportVariantsUseCase.call();
  }

  @override
  Future<ApiResponse<List<Collection>>> fetchNonExportCollections(
      String hexCode, bool exclusive) async {
    return _fetchCollectionsUseCase.call(hexCode, exclusive);
  }

  @override
  Future<ApiResponse<List<Variant>>> fetchNonExportOutletVariants() async {
    return _fetchOutletVariantsUseCase.call();
  }

  @override
  Future<ApiResponse<List<Variant>>> searchNonExportOutletVariants(
      String hexCode, String searchTerm) async {
    return _searchOutletVariantsUseCase.call(hexCode, searchTerm);
  }

  @override
  Future<ApiResponse<FetchSalesOrdersResponse>> fetchSalesOrders(
      FetchSalesOrdersParams params) async {
    return _fetchSalesOrdersUseCase.call(params);
  }

  @override
  Future<ApiResponse<FetchSalesLocationDriversResponse>>
      fetchSalesLocationDrivers(FetchSalesLocationDriverParams params) async {
    return _fetchSalesLocationDriversUseCase.call(params);
  }

  // @override
  // Future<ApiResponse<void>> deleteOrder(
  //     String transactionId, String outletId) async {
  //   return _deleteOrderUseCase.call(transactionId, outletId);
  // }
}
