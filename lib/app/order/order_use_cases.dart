import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/collection.dart';
import 'package:td_commons_flutter/models/order_confirmation.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/order/widgets/add_customer.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

import 'order_params.dart';
import 'order_repository.dart';

final fetchOrderDetailsUseCaseProvider =
    Provider.autoDispose<FetchOrderDetailsUseCase>(
  (ref) {
    return (String orderId) {
      return UseCase<OrderDetails>().call(
        () => ref.read(orderRepoProvider).fetchOrderDetails(orderId),
      );
    };
  },
);

final fetchOrdersUseCaseProvider = Provider.autoDispose<FetchOrdersUseCase>(
  (ref) {
    return (FetchTransactionsParam params) {
      return UseCase<FetchTransactionsResponse>().call(
        () => ref.read(orderRepoProvider).fetchOrders(params),
      );
    };
  },
);

final fetchShipmentUseCaseProvider = Provider.autoDispose<FetchShipmentUseCase>(
  (ref) {
    return (String shipmentId) {
      return UseCase<ShipmentResult>().call(
        () => ref.read(orderRepoProvider).fetchShipment(shipmentId),
      );
    };
  },
);

final deleteOrderUseCaseProvider = Provider.autoDispose<DeleteOrderUseCase>(
  (ref) {
    return (String transactionId, String outletId) {
      return UseCase<void>().call(
        () => ref.read(orderRepoProvider).deleteOrder(transactionId, outletId),
      );
    };
  },
);

final fetchExportVariantsUseCaseProvider =
    Provider.autoDispose<FetchExportVariantsUseCase>(
  (ref) {
    return () {
      return UseCase<List<Variant>>().call(
        () => ref.read(orderRepoProvider).fetchExportVariants(),
      );
    };
  },
);

final fetchCollectionsUseCaseProvider =
    Provider.autoDispose<FetchCollectionsUseCase>(
  (ref) {
    return (String hexCode, bool exclusive) {
      return UseCase<List<Collection>>().call(
        () => ref.read(orderRepoProvider).fetchCollections(hexCode, exclusive),
      );
    };
  },
);

final fetchOutletVariantsUseCaseProvider =
    Provider.autoDispose<FetchOutletVariantsUseCase>(
  (ref) {
    return () {
      return UseCase<List<Variant>>().call(
        () => ref.read(orderRepoProvider).fetchOutletVariants(),
      );
    };
  },
);

final searchOutletVariantsUseCaseProvider =
    Provider.autoDispose<SearchOutletVariantsUseCase>(
  (ref) {
    return (String hexCode, String searchTerm) {
      return UseCase<List<Variant>>().call(
        () => ref
            .read(orderRepoProvider)
            .searchOutletVariants(hexCode, searchTerm),
      );
    };
  },
);

final prepareNonExportOrderUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<OrderPreviewDetail>>, PrepareOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<OrderPreviewDetail>();
    return useCase.call(() => orderRepository.prepareNonExportOrder(params));
  },
);

final prepareExportOrderUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<OrderPreviewDetail>>, PrepareOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<OrderPreviewDetail>();
    return useCase.call(() => orderRepository.prepareExportOrder(params));
  },
);

final fetchShippingRateUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<OrderConfirmation>>, ShippingRateParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<OrderConfirmation>();
    return useCase.call(() => orderRepository.fetchShippingRate(params));
  },
);

final createDraftOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<void>>, String>(
  (ref, cartOrderId) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<void>();
    return useCase.call(() => orderRepository.createDraftOrder(cartOrderId));
  },
);

final updateDraftOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<void>>, UpdateDraftParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<void>();
    return useCase.call(() => orderRepository.updateDraftOrder(params));
  },
);

/// Returns the `cartOrderId`
final createNonExportCartUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<String>>, CreateOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<String>();
    return useCase.call(() => orderRepository.createNonExportCart(params));
  },
);

/// Returns the order `reference`
final createNonExportOrderUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<CreateOrderResponse>>, CreateOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<CreateOrderResponse>();
    return useCase.call(() => orderRepository.createNonExportOrder(params));
  },
);

final createExportOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<dynamic>>, CreateOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<dynamic>();
    return useCase.call(() => orderRepository.createExportOrder(params));
  },
);

final createSalesOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<dynamic>>, CreateOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<dynamic>();
    return useCase.call(() => orderRepository.createSalesOrder(params));
  },
);

final getOutletUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<RetailOutlet>>, String>(
  (ref, outletId) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<RetailOutlet>();
    return useCase.call(() => orderRepository.getRetailOutlet(outletId));
  },
);

final chargeOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<void>>, ChargeOrderParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<void>();
    return useCase.call(() => orderRepository.chargeOrder(params));
  },
);

final fetchSalesOrdersUseCaseProvider =
    Provider.autoDispose<FetchSalesOrdersUseCase>(
  (ref) {
    return (FetchSalesOrdersParams params) {
      return UseCase<FetchSalesOrdersResponse>().call(
        () => ref.read(orderRepoProvider).fetchSalesOrders(params),
      );
    };
  },
);

final fetchSalesLocationDriversUseCaseProvider =
    Provider.autoDispose<FetchSalesLocationDriversUseCase>(
  (ref) {
    return (FetchSalesLocationDriverParams params) {
      return UseCase<FetchSalesLocationDriversResponse>().call(
        () => ref.read(orderRepoProvider).fetchSalesLocationDrivers(params),
      );
    };
  },
);

final assignDriverToOrdersUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<dynamic>>, AssignOrdersParams>(
        (ref, params) {
  final orderRepository = ref.read(orderRepoProvider);
  final useCase = UseCase<dynamic>();
  return useCase.call(() => orderRepository.assignDriverToOrders(params));
});

final getSalesOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<Order>>, String>(
  (ref, orderId) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<Order>();
    return useCase.call(() => orderRepository.getSalesOrder(orderId));
  },
);

final searchCustomersUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<List<RetailOutlet>>>, String>(
  (ref, phoneNumber) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<List<RetailOutlet>>();
    return useCase.call(() => orderRepository.searchCustomers(phoneNumber));
  },
);

final fetchSupplierInventoryUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<List<Variant>>>, String>(
  (ref, outletId) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<List<Variant>>();
    return useCase.call(() => orderRepository.fetchSupplierInventory(outletId));
  },
);

final acceptOrderUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<dynamic>>, String>(
  (ref, reference) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<dynamic>();
    return useCase.call(() => orderRepository.acceptOrder(reference));
  },
);

final markOrderAsDeliveredUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<dynamic>>, Map<String, dynamic>>(
  (ref, items) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<dynamic>();
    return useCase.call(() => orderRepository.markOrderAsDelivered(items));
  },
);

final addNewCustomerUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<RetailOutlet>>, CustomerParams>(
  (ref, params) {
    final orderRepository = ref.read(orderRepoProvider);
    final useCase = UseCase<RetailOutlet>();
    return useCase.call(() => orderRepository.addNewCustomer(params));
  },
);

typedef FetchOrdersUseCase = Future<ApiResponse<FetchTransactionsResponse>>
    Function(FetchTransactionsParam params);
typedef FetchOrderDetailsUseCase = Future<ApiResponse<OrderDetails>> Function(
    String orderId);
typedef FetchShipmentUseCase = Future<ApiResponse<ShipmentResult>> Function(
    String shipmentId);
typedef DeleteOrderUseCase = Future<ApiResponse<void>> Function(
    String transactionId, String outletId);

typedef FetchExportVariantsUseCase = Future<ApiResponse<List<Variant>>>
    Function();
typedef FetchCollectionsUseCase = Future<ApiResponse<List<Collection>>>
    Function(String hexCode, bool exclusive);
typedef FetchOutletVariantsUseCase = Future<ApiResponse<List<Variant>>>
    Function();
typedef SearchOutletVariantsUseCase = Future<ApiResponse<List<Variant>>>
    Function(String hexCode, String searchTerm);

typedef FetchSalesOrdersUseCase = Future<ApiResponse<FetchSalesOrdersResponse>>
    Function(FetchSalesOrdersParams params);

typedef FetchSalesLocationDriversUseCase
    = Future<ApiResponse<FetchSalesLocationDriversResponse>> Function(
        FetchSalesLocationDriverParams params);
