import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';

/*Legal compliance (for all countries) - Step 0
Banking Information (for just NG) - Step 1
Documents Upload (for just NG) - Step 2
Verify Identity (KYC) (for all countries except NG) - Step 3 */

class BusinessVerificationState extends Notifier<int> {
  //initial value

  @override
  build() {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    bool step1Complete = outlet?.company?.kyb?.businessType != null &&
        outlet?.company?.kyb?.rcNumber != null;
    bool step2Complete = outlet?.company?.kyb?.bvn != null &&
        outlet?.walletAccount?.accountNumber != null &&
        outlet?.walletAccount?.bankName != null;
    bool? step4Complete = outlet?.idVerified;

    int nextStep = 0;
    if (!step1Complete) {
      nextStep = 0;
    } else if (step1Complete && step2Complete) {
      if (outlet?.country!.toLowerCase() != "ng") {
        nextStep = 3;
      } else {
        nextStep = 2;
      }
    } else if (step1Complete) {
      if (outlet?.country!.toLowerCase() != "ng") {
        nextStep = 3;
      } else {
        nextStep = 1;
      }
    } else if (step4Complete == true) {}
    return nextStep;
  }

  updateNavigationState(int expectedState) {
    state = expectedState;
  }

  navigatePageForward(bool isNg) {
    if (isNg) {
      if (state != 2) {
        state++;
      }
    } else {
      if (state == 0) {
        state = 3;
      }
    }
  }

  navigatePageBackward(bool isNg) {
    if (state == 0) {
      return;
    }
    if (isNg) {
      if (state <= 2) {
        state--;
      }
      if (state == 3) {
        state = 0;
      }
    }
  }
}

final businessVerificationProvider =
    NotifierProvider<BusinessVerificationState, int>(() {
  return BusinessVerificationState();
});
