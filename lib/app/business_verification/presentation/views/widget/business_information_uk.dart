import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class BusinessInformationUK extends StatefulWidget {
  const BusinessInformationUK({super.key});

  @override
  State<BusinessInformationUK> createState() => _BusinessInformationUKState();
}

class _BusinessInformationUKState extends State<BusinessInformationUK> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Information',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'Type to search for your business name',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            const TextField(
              decoration: InputDecoration(hintText: 'Search business name'),
              keyboardType: TextInputType.emailAddress,
              //style: ,
            ),
            const Gap(10),
            Text.rich(
              TextSpan(
                text: "Can't find name? ",
                style: theme.textTheme.bodyMedium
                    ?.copyWith(color: Palette.blackSecondary),
                children: [
                  WidgetSpan(
                    child: InkWell(
                      child: Text(
                        "Enter details manually",
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Palette.primary,
                        ),
                      ),
                    ),
                    baseline: TextBaseline.alphabetic,
                    alignment: ui.PlaceholderAlignment.baseline,
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
