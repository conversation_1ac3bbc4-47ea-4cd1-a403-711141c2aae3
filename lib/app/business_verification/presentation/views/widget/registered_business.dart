import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class RegisteredBusiness extends StatefulWidget {
  const RegisteredBusiness({super.key});

  @override
  State<RegisteredBusiness> createState() => _RegisteredBusinessState();
}

class _RegisteredBusinessState extends State<RegisteredBusiness> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Registered business address',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'The official business address registered with a government body. It can be different from the address at which you have a business',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            const Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(hintText: 'Address line 1'),
                    keyboardType: TextInputType.emailAddress,
                    //style: ,
                  ),
                ),
              ],
            ),
            const Gap(10),
            Row(
              children: [
                Expanded(
                  child: PopupMenuButton<int>(
                    itemBuilder: (context) {
                      var list = <PopupMenuEntry<int>>[];

                      return list;
                    },
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                      side: const BorderSide(),
                    ),
                    elevation: 0.7,
                    padding: const EdgeInsets.all(0),
                    onSelected: (val) {},
                    tooltip: null,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                            width: .2),
                      ),
                      height: 50,
                      padding: const EdgeInsets.only(left: 10, right: 10),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text('LGA'),
                          ),
                          Icon(Icons.arrow_drop_down),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const Gap(10),
            const Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(hintText: 'State'),
                    keyboardType: TextInputType.emailAddress,
                    //style: ,
                  ),
                ),
                Gap(10),
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(hintText: 'P.O Box'),
                    keyboardType: TextInputType.emailAddress,
                    //style: ,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
