import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class VerifyBusinessUK extends StatefulWidget {
  const VerifyBusinessUK({super.key});

  @override
  State<VerifyBusinessUK> createState() => _VerifyBusinessUKState();
}

class _VerifyBusinessUKState extends State<VerifyBusinessUK> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verify your Business',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(10),
            Text(
              'You need to complete the following steps to verify your business',
              style: theme.textTheme.bodyMedium
                  ?.copyWith(fontWeight: FontWeight.w400),
            ),
            const Gap(20),
            customCard(
                title: 'Business details',
                description:
                    'You are the only owner with more than 25% of the business'),
            const Gap(20),
            customCard(
                title: 'Banking information',
                description:
                    'There are other directors or shareholders with greater than 25% ownership'),
            const Gap(20),
            customCard(
                title: 'Director information',
                description:
                    'There are other directors or shareholders with greater than 25% ownership'),
          ],
        ),
      ),
    );
  }

  Widget customCard({required String title, required String description}) {
    final theme = Theme.of(context);
    return Card(
      color: theme.secondaryHeaderColor,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            SvgPicture.asset(
              kBankSvg,
              width: 20,
            ),
            const Gap(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const Gap(5),
                  Text(
                    description,
                    softWrap: true,
                    style: theme.textTheme.bodyMedium
                        ?.copyWith(color: Palette.blackSecondary),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: const Color(0XFFFF8D06).withOpacity(.12)),
                        child: Text(
                          'Pending',
                          style: theme.textTheme.bodySmall
                              ?.copyWith(color: const Color(0XFFFF8D06)),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> statusText() {
    switch ('') {
      case '':
        return {'statusText': 'Pending', 'color': Palette.statusPending};
      case ' ':
        return {'stausText': 'Verifying', 'color': Palette.statusVerifying};
      case '  ':
        return {'statusText': 'Verified', 'color': Palette.statusSuccess};
      default:
        return {};
    }
  }
}
