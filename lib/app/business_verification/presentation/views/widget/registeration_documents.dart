import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class RegisterationDocuments extends StatefulWidget {
  const RegisterationDocuments({super.key});

  @override
  State<RegisterationDocuments> createState() => _RegisterationDocumentsState();
}

class _RegisterationDocumentsState extends State<RegisterationDocuments> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add registration documents',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'Upload the Certificate of Incorporation and Memorandum of Association (Mermat) documents for this company',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            const Gap(20),
            customCardUnuploaded(),
            const Gap(20),
            customCardUploaded()
          ],
        ),
      ),
    );
  }

  Widget customCardUnuploaded() {
    final theme = Theme.of(context);
    return Row(
      children: [
        Card(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.secondaryHeaderColor,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(
                vertical: 16,
                horizontal: 24,
              ),
              child: Column(
                children: [
                  SvgPicture.asset(kfileSvg),
                  const Gap(10),
                  Text(
                    'Add Certificate of Incorporation',
                    style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Palette.orangePrimaryDark),
                  ),
                  const Gap(5),
                  Text(
                    'upload .pdf, .jpg or .png fileMax file size is [size limit]',
                    style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w400,
                        color: Palette.blackSecondary),
                  ),
                  //
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget customCardUploaded() {
    final theme = Theme.of(context);
    return Card(
      color: theme.scaffoldBackgroundColor,
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    padding: const EdgeInsets.all(4),
                    color: Palette.statusPending.withOpacity(.1),
                    child: SvgPicture.asset(
                      kfileSvg,
                      color: Palette.statusPending,
                    )),
                const Gap(5),
                Text('CAC_doc.pdf', style: theme.textTheme.bodySmall)
              ],
            ),
            Row(
              children: [
                Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                        color: Palette.placeholder.withOpacity(.1),
                        borderRadius: BorderRadius.circular(8)),
                    child: Text('Upload new',
                        style: theme.textTheme.bodySmall
                            ?.copyWith(fontWeight: FontWeight.w600))),
                const Gap(5),
                Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                        color: Palette.deleteRed.withOpacity(.1),
                        borderRadius: BorderRadius.circular(8)),
                    child: Text('Delete',
                        style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: Palette.deleteRed)))
              ],
            ),
          ],
        ),
      ),
    );
  }
}
