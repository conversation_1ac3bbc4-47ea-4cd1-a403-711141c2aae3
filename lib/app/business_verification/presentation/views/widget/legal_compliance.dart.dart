import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/index.dart' hide Container;
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/business_verification/domain/params/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/use_cases/business_use_cases.dart';
import 'package:td_procurement/app/business_verification/presentation/logic/states/business_verification_state.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

const businessStatuses = [
  "Corporation",
  "Cooperative",
  "Limited Liability Company",
  "Partnership",
  "Sole Trader",
  "Government Organization",
];

class LegalCompliance extends ConsumerStatefulWidget {
  const LegalCompliance({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _LegalComplianceState();
}

class _LegalComplianceState extends ConsumerState<LegalCompliance> {
  String? selectedBusinessStatus;
  Map<String, dynamic>? selectedCountry;
  late TextEditingController controller;
  final _key = GlobalKey<FormState>();
  final ValueNotifier<bool> _loader = ValueNotifier(false);

  @override
  void initState() {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    selectedCountry = countries
        .where((country) => country['code'] == outlet?.country?.toUpperCase())
        .first;
    selectedBusinessStatus = outlet?.company?.kyb?.businessType;
    controller = TextEditingController(text: outlet?.company?.kyb?.rcNumber);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;

    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(kLegalSvg),
            const Gap(20),
            Text(
              'Legal and Compliance',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'Fill in your organization\'s legal details ',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            Theme(
              data: ThemeData(
                  popupMenuTheme: PopupMenuThemeData(color: Palette.kFFFFFF)),
              child: PopupMenuButton<String>(
                itemBuilder: (context) {
                  var list = <PopupMenuEntry<String>>[];
                  for (var e in businessStatuses) {
                    list.add(
                      PopupMenuItem(
                        value: e,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 18),
                          child: Text(
                            e,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    );
                  }
                  return list;
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                  side: const BorderSide(color: Colors.transparent),
                ),
                elevation: 0.7,
                padding: const EdgeInsets.all(0),
                onSelected: (value) {
                  setState(() {
                    selectedBusinessStatus = value;
                  });
                },
                tooltip: null,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        color: Theme.of(context).colorScheme.outline,
                        width: .2),
                  ),
                  height: 50,
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child:
                            Text(selectedBusinessStatus ?? 'Business Status'),
                      ),
                      const Icon(Icons.arrow_drop_down),
                    ],
                  ),
                ),
              ),
            ),
            const Gap(10),
            Form(
              key: _key,
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Field required";
                        }
                        return null;
                      },
                      controller: controller,
                      decoration: const InputDecoration(
                          hintText: 'Business registration number'),
                      keyboardType: TextInputType.emailAddress,
                      //style: ,
                    ),
                  ),
                ],
              ),
            ),
            const Gap(10),
            Theme(
              data: ThemeData(
                  popupMenuTheme: PopupMenuThemeData(color: Palette.kFFFFFF)),
              child: PopupMenuButton<String>(
                itemBuilder: (context) {
                  var list = <PopupMenuEntry<String>>[];
                  for (var e in countries) {
                    list.add(
                      PopupMenuItem(
                        value: e['code'],
                        child: Padding(
                          padding: const EdgeInsets.only(left: 18),
                          child: Text(
                            e['name'] ?? '',
                            maxLines: 1,
                          ),
                        ),
                      ),
                    );
                  }
                  return list;
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                  side: const BorderSide(color: Colors.transparent),
                ),
                elevation: 0.7,
                padding: const EdgeInsets.all(0),
                onSelected: (value) {
                  setState(() {
                    selectedCountry = countries
                        .where(
                            (country) => country['code'] == value.toUpperCase())
                        .first;
                  });
                },
                tooltip: null,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        color: Theme.of(context).colorScheme.outline,
                        width: .2),
                  ),
                  height: 50,
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(selectedCountry != null
                            ? selectedCountry!['name']
                            : 'Country'),
                      ),
                      const Icon(Icons.arrow_drop_down),
                    ],
                  ),
                ),
              ),
            ),
            const Gap(10),
            Row(
              children: [
                Expanded(
                  child: CustomFilledButton(
                    onPressed: () {
                      if (_key.currentState!.validate()) {
                        if (selectedBusinessStatus == null ||
                            selectedCountry == null) {
                          Toast.error('Select all fields', context);
                          return;
                        } else if (selectedCountry!['code'].toLowerCase() !=
                            outlet?.country?.toLowerCase()) {
                          Toast.error(
                              'Selected Country doesn\'t match outlet\'s country',
                              context);
                        } else {
                          submit(outlet!);
                        }
                      }
                    },
                    text: 'Save Data',
                    loaderNotifier: _loader,
                  ),
                ),
              ],
            ),
            const Gap(10),
            /*  Center(
                child: TextButton(
                    onPressed: () {
                      ref
                          .read(businessVerificationProvider.notifier)
                          .navigatePageForward(
                              outlet?.country?.toLowerCase() == "ng");
                    },
                    child: const Text('Skip')))*/
          ],
        ),
      ),
    );
  }

  Future<void> submit(RetailOutlet outlet) async {
    bool step2Complete = outlet.company?.kyb?.bvn != null &&
        outlet.walletAccount?.accountNumber != null &&
        outlet.walletAccount?.bankName != null;
    bool? step4Complete = outlet.kyc?.idVerified;

    if (_key.currentState!.validate()) {
      if (selectedBusinessStatus == null) {
        Toast.error('Select Business status', context);
        return;
      }
    }

    _loader.value = true;

    final response = await ref.read(updateBusinessUseCaseProvider(
        BusinessVerificationRequest(
            id: outlet.id,
            businessName: outlet.outletBusinessName,
            businessStatus: selectedBusinessStatus,
            registrationNumber: controller.text,
            countryOfIncorporation: selectedCountry!['code'])));
    switch (response) {
      case Success():
        if (outlet.country?.toLowerCase() == "ng") {
          if (step2Complete) {
            ref
                .read(businessVerificationProvider.notifier)
                .updateNavigationState(2);
          } else {
            ref
                .read(businessVerificationProvider.notifier)
                .navigatePageForward(outlet.country?.toLowerCase() == "ng");
          }
        } else {
          if (step4Complete == true) {
            Toast.success('Verification complete', context);
            // ignore: use_build_context_synchronously
            context.pop();
          } else {
            ref
                .read(businessVerificationProvider.notifier)
                .updateNavigationState(3);
          }
        }
        _loader.value = false;
        ref.read(userControllerProvider.notifier).updateOutlet(response.data);
      case Failure error:
        {
          _loader.value = false;
          if (mounted) {
            Toast.apiError(error.error, context);
          }
        }
    }
  }
}
