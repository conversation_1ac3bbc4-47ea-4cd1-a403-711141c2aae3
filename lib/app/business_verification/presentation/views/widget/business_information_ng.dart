import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

class BusinessInformationNG extends StatefulWidget {
  const BusinessInformationNG({super.key});

  @override
  State<BusinessInformationNG> createState() => _BusinessInformationNGState();
}

class _BusinessInformationNGState extends State<BusinessInformationNG> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Information',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'Search or enter your legal business name and Companies House information',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            const Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration:
                        InputDecoration(hintText: 'Legal business name'),
                    keyboardType: TextInputType.emailAddress,
                    //style: ,
                  ),
                ),
              ],
            ),
            const Gap(10),
            const Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration:
                        InputDecoration(hintText: 'CAC registration number'),
                    keyboardType: TextInputType.emailAddress,
                    //style: ,
                  ),
                ),
              ],
            ),
            const Gap(10),
            PopupMenuButton<int>(
              itemBuilder: (context) {
                var list = <PopupMenuEntry<int>>[];

                return list;
              },
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: const BorderSide(),
              ),
              elevation: 0.7,
              padding: const EdgeInsets.all(0),
              onSelected: (val) {},
              tooltip: null,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                      color: Theme.of(context).colorScheme.outline, width: .2),
                ),
                height: 50,
                padding: const EdgeInsets.only(left: 10, right: 10),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text('Business Type'),
                    ),
                    Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
