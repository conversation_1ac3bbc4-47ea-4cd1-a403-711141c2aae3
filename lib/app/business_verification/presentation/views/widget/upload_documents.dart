import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/business_verification/domain/params/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/params/file_upload_params.dart';
import 'package:td_procurement/app/business_verification/domain/use_cases/business_use_cases.dart';
import 'package:td_procurement/app/business_verification/presentation/logic/file_upload.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class UploadDocuments extends ConsumerStatefulWidget {
  const UploadDocuments({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _UploadDocumentsState();
}

class _UploadDocumentsState extends ConsumerState<UploadDocuments>
    with FileUpload {
  Map<String, dynamic> filesMap = {};
  String nationalId = "National Identity Card";
  String certOfInco = "bnCertificate";
  String partOfPartners = "partnerParticulars";
  /* String nationalId = "National ID e.g driver's license,passport";
  String certOfInco = "Certificate of Incorporation";
  String partOfPartners = "Particulars of patners";*/
  final ValueNotifier<bool> _loader = ValueNotifier(false);

  fieldTitle(String key) {
    switch (key) {
      case 'National Identity Card':
        return 'National ID e.g driver\'s license,passport';
      case 'bnCertificate':
        return 'Certificate of Incorporation';
      case 'partnerParticulars':
        return 'Particulars of patners';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(kUploadSvg),
            const Gap(20),
            Text(
              'Upload required documents',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(5),
            Text(
              'Upload your registration documents',
              style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w400, color: Palette.blackSecondary),
            ),
            const Gap(10),
            filesMap[nationalId] != null
                ? customCardUploaded(filesMap[nationalId])
                : customCard(nationalId),
            const Gap(10),
            filesMap[certOfInco] != null
                ? customCardUploaded(filesMap[certOfInco])
                : customCard(certOfInco),
            const Gap(10),
            filesMap[partOfPartners] != null
                ? customCardUploaded(filesMap[partOfPartners])
                : customCard(partOfPartners),
            const Gap(10),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                        color: Palette.k202020.withOpacity(.06),
                        borderRadius: BorderRadius.circular(8)),
                    child: const Text(
                        'Only upload .pdf, .jpg or .png file, maximum file size is 1mb'),
                  ),
                ),
              ],
            ),
            const Gap(10),
            Row(
              children: [
                Expanded(
                  child: CustomFilledButton(
                    onPressed: () async {
                      if (filesMap[nationalId] != null &&
                          filesMap[certOfInco] != null &&
                          filesMap[partOfPartners] != null) {
                        _loader.value = true;

                        final response = await ref.read(
                            updateBusinessUseCaseProvider(
                                BusinessVerificationRequest(
                                    id: outlet?.id,
                                    businessName: outlet?.outletBusinessName,
                                    images: [
                              filesMap[nationalId].toJson(),
                              filesMap[certOfInco].toJson(),
                              filesMap[partOfPartners].toJson(),
                            ])));
                        switch (response) {
                          case Success():
                            ref
                                .read(userControllerProvider.notifier)
                                .updateOutlet(response.data);
                            _loader.value = false;
                            Toast.success('Verification complete', context);
                            context.pop();
                          case Failure error:
                            {
                              _loader.value = false;
                              if (mounted) {
                                Toast.apiError(error.error, context);
                              }
                            }
                        }
                        return;
                      }
                      Toast.error("Upload all documents", context);
                    },
                    text: 'Finish and Submit',
                    loaderNotifier: _loader,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget customCard(String title) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: () => fetchDoc(title),
      child: Card(
        color: Palette.kFCFCFC,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              SvgPicture.asset(kfileSvg),
              const Gap(10),
              Text(
                fieldTitle(title),
                style: theme.textTheme.bodyMedium
                    ?.copyWith(fontWeight: FontWeight.w500),
              )
            ],
          ),
        ),
      ),
    );
  }

  void fetchDoc(String key) async {
    final allowedExtensions = ['pdf', 'jpg', 'png', 'jpeg'];
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: allowedExtensions,
    );
    if (result != null) {
      PlatformFile file = result.files.single;
      String? ext = file.extension!.toLowerCase();

      if (!allowedExtensions.contains(ext)) {
        Toast.error("Unsupported File format", context);
        return;
      }

      if (!isValidFileSize(file.bytes!, context)) {
        return;
      }

      setState(() {
        filesMap[key] = FileUploadParams(
            file.extension!, file.bytes!, file, file.extension, key);
      });
    }
  }

  Widget customCardUploaded(FileUploadParams params) {
    final theme = Theme.of(context);
    return Card(
      color: theme.scaffoldBackgroundColor,
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 3,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      padding: const EdgeInsets.all(4),
                      color: Palette.statusPending.withOpacity(.1),
                      child: SvgPicture.asset(
                        kfileSvg,
                        color: Palette.statusPending,
                      )),
                  const Gap(5),
                  Expanded(
                      child: Text(
                    params.file.name,
                    style: theme.textTheme.bodySmall,
                    maxLines: 2,
                  ))
                ],
              ),
            ),
            const Gap(10),
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      fetchDoc(filesMap[params.key!]);
                    },
                    child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            color: Palette.placeholder.withOpacity(.1),
                            borderRadius: BorderRadius.circular(8)),
                        child: Text('Upload new',
                            style: theme.textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w600))),
                  ),
                  const Gap(5),
                  InkWell(
                    onTap: () {
                      setState(() {
                        filesMap[params.key!] = null;
                      });
                    },
                    child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                            color: Palette.deleteRed.withOpacity(.1),
                            borderRadius: BorderRadius.circular(8)),
                        child: Text('Delete',
                            style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: Palette.deleteRed))),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
