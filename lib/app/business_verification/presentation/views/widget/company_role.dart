import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'dart:ui' as ui show PlaceholderAlignment;

const optionsList = [
  {
    "title": "I’m the only director and significant shareholder",
    "desc": "You are the only owner with more than 25% of the business"
  },
  {
    "title": "I’m one of several directors or significant shareholders",
    "desc":
        "There are other directors or shareholders with greater than 25% ownership"
  },
  {
    "title": "I’m neither a director or shareholder",
    "desc": "You are applying on behalf of your employer "
  },
  {"title": "I’m not sure what to choose", "desc": ""},
];

class CompanyRole extends StatefulWidget {
  const CompanyRole({super.key});

  @override
  State<CompanyRole> createState() => _CompanyRoleState();
}

class _CompanyRoleState extends State<CompanyRole> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 500,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your role in the company',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(10),
            Expanded(
                child: ListView(
              shrinkWrap: true,
              children: optionsList
                  .map((e) => customCard(title: e['title']!, desc: e['desc']!))
                  .toList(),
            ))
          ],
        ),
      ),
    );
  }

  Widget customCard({required String title, required String desc}) {
    final theme = Theme.of(context);
    return Card(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: theme.inputDecorationTheme.outlineBorder!.copyWith(
            width: 0.5,
          )),
      color: theme.scaffoldBackgroundColor,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Icon(
                Icons.check_circle,
                color: theme.inputDecorationTheme.outlineBorder!.color,
                size: 15,
              ),
            ),
            const Gap(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyLarge
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                  desc.isEmpty
                      ? const SizedBox.shrink()
                      : Column(
                          children: [
                            Text(
                              desc,
                              style: theme.textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w400),
                            ),
                          ],
                        )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
