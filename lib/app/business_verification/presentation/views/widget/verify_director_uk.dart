import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class VerifyDirectorUK extends StatefulWidget {
  const VerifyDirectorUK({super.key});

  @override
  State<VerifyDirectorUK> createState() => _VerifyDirectorUKState();
}

class _VerifyDirectorUKState extends State<VerifyDirectorUK> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: 400,
      height: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 40),
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verify a director',
              style: theme.textTheme.bodyLarge
                  ?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Gap(10),
            Text(
              'Choose from one of the company directors listed to verify their information',
              style: theme.textTheme.bodyMedium
                  ?.copyWith(fontWeight: FontWeight.w400),
            ),
            const Gap(20),
            Expanded(
              child: ListView(
                children: [
                  customCard(
                      title: 'Director 1',
                      description: 'Peter Oghenakhogie Abu-Ekpeshie'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget customCard({required String title, required String description}) {
    final theme = Theme.of(context);
    return Card(
      color: theme.secondaryHeaderColor,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.2,
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            SvgPicture.asset(
              kUserSvg,
              width: 20,
            ),
            const Gap(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium
                        ?.copyWith(color: Palette.blackSecondary),
                  ),
                  const Gap(5),
                  Text(
                    description,
                    softWrap: true,
                    style: theme.textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const Gap(10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: Palette.orangePrimaryDark),
                        child: Row(
                          children: [
                            Text(
                              'Verify',
                              style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.scaffoldBackgroundColor),
                            ),
                            const Gap(5),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: theme.scaffoldBackgroundColor,
                              size: 10,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
