import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/business_verification/presentation/logic/states/business_verification_state.dart';
import 'package:td_procurement/app/business_verification/presentation/views/widget/banking_information.dart';
import 'package:td_procurement/app/business_verification/presentation/views/widget/legal_compliance.dart.dart';
import 'package:td_procurement/app/business_verification/presentation/views/widget/upload_documents.dart';
import 'package:td_procurement/app/business_verification/presentation/views/widget/verify_identity.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class BusinessVerification extends ConsumerStatefulWidget {
  const BusinessVerification({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _BusinessVerificationUK();
  }
}

class _BusinessVerificationUK extends ConsumerState<BusinessVerification> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final businessVerificationState = ref.watch(businessVerificationProvider);
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;

    return Scaffold(
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 10),
            color: theme.secondaryHeaderColor,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            context.pop();
                          },
                          child: const Icon(
                            Icons.clear,
                            color: Colors.black,
                            size: 15,
                          ),
                        ),
                        const Gap(20),
                        Text(
                          'Complete Onboarding',
                          style: theme.textTheme.bodyLarge,
                        )
                      ],
                    ),
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            context.pop();
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: const Color(0XFFFF8D06).withOpacity(.12),
                            ),
                            child: const Text(
                              'Finish Later',
                              style: TextStyle(
                                color: Color(0XFFFF8D06),
                              ),
                            ),
                          ),
                        ),
                        const Gap(20),
                        businessVerificationState == 3
                            ? const SizedBox.shrink()
                            : InkWell(
                                onTap: () {
                                  if (businessVerificationState == 2) {
                                    context.pop();
                                    return;
                                  }
                                  skipHandler(businessVerificationState);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      color: Palette.brandBlack),
                                  child: const Text(
                                    'Skip',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                              ),
                      ],
                    ),
                  ],
                ),
                SizedBox(
                  width: 550,
                  child: Row(
                    children: [
                      InkWell(
                        onTap: () {
                          ref
                              .read(businessVerificationProvider.notifier)
                              .navigatePageBackward(
                                  outlet?.country?.toLowerCase() == "ng");
                        },
                        child: const Icon(
                          Icons.arrow_back_ios_new,
                          size: 12,
                        ),
                      ),
                      const Gap(10),
                      SizedBox(
                        width: 500,
                        child: LinearProgressIndicator(
                          color: Palette.orangePrimaryDark,
                          backgroundColor: Colors.grey.withOpacity(.2),
                          borderRadius: BorderRadius.circular(8),
                          minHeight: 7.5,
                          value: businessVerificationState / 3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: Center(
                child: pageMapper(businessVerificationState),
              ),
            ),
          ),
        ],
      ),
    );
  }

  skipHandler(int state) {
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    bool step2Complete = outlet?.company?.kyb?.bvn != null &&
        outlet?.walletAccount?.accountNumber != null &&
        outlet?.walletAccount?.bankName != null;
    bool? step4Complete = outlet?.kyc?.idVerified;

    if (outlet?.country?.toLowerCase() == "ng") {
      switch (state) {
        case 0:
          if (step2Complete) {
            ref
                .read(businessVerificationProvider.notifier)
                .updateNavigationState(2);
          } else {
            ref
                .read(businessVerificationProvider.notifier)
                .navigatePageForward(outlet?.country?.toLowerCase() == "ng");
          }

        case 1:
          ref
              .read(businessVerificationProvider.notifier)
              .navigatePageForward(outlet?.country?.toLowerCase() == "ng");
        case 2:
          context.pop();
      }
    } else {
      switch (state) {
        case 0:
          if (step4Complete == true) {
            context.pop();
          } else {
            ref
                .read(businessVerificationProvider.notifier)
                .updateNavigationState(3);
          }
        case 3:
          context.pop();
      }
    }
  }

  Widget pageMapper(int index) {
    switch (index) {
      case 0:
        return const LegalCompliance();
      case 1:
        return const BankingInformation();
      case 2:
        return const UploadDocuments();
      case 3:
        return VerifyIdentity();

      default:
        return const SizedBox.shrink();
    }
  }
}
