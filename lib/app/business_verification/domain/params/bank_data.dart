class BankData {
  BankData({
    required this.status,
    required this.data,
  });
  late final String status;
  late final List<BankListData> data;

  BankData.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data =
        List.from(json['data']).map((e) => BankListData.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['status'] = status;
    _data['data'] = data.map((e) => e.toJson()).toList();
    return _data;
  }
}

class BankListData {
  BankListData({
    required this.name,
    required this.code,
  });
  late final String name;
  late final String code;

  BankListData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['name'] = name;
    _data['code'] = code;
    return _data;
  }
}
