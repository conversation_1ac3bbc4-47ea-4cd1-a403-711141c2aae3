import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';

class FileUploadParams {
  String mediaType;
  Uint8List bytes;
  PlatformFile file;
  String? ext;
  String? key;
  FileUploadParams(this.mediaType, this.bytes, this.file, this.ext, this.key);

  to<PERSON>son() {
    return {"type": key, "file": file.name, "file64": base64Encode(bytes)};
  }
}
