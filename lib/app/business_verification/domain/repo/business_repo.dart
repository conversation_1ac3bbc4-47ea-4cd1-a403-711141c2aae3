import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/params/initiate_stripe_response.dart';
import 'package:td_procurement/app/business_verification/domain/params/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/params/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/repo/business_repo_impl.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

abstract class BusinessRepo {
  Future<ApiResponse<RetailOutlet>> updateBusinessDetails(
      BusinessVerificationRequest params);

  Future<ApiResponse<InitiateStripeResponse>> initiateStripeVerification(
      InitiateStripeRequest request);

  Future<ApiResponse<BankData>> getBankList();
}

final businessRepoProvider = Provider<BusinessRepo>((ref) {
  return BusinessRepoImplementation(ref);
});
