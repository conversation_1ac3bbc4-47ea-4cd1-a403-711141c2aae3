import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/auth/domain/params/initiate_stripe_response.dart';
import 'package:td_procurement/app/business_verification/data/data_source/business_data_source.dart';
import 'package:td_procurement/app/business_verification/domain/params/bank_data.dart';
import 'package:td_procurement/app/business_verification/domain/params/business_verification_request.dart';
import 'package:td_procurement/app/business_verification/domain/repo/business_repo.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

class BusinessRepoImplementation extends BusinessRepo {
  final Ref _ref;

  BusinessRepoImplementation(this._ref);

  late final BusinessDataSource _dataSource =
      _ref.read(businessDataSourceProvider);

  late final BusinessDataSource _dataSource2 =
      _ref.read(businessDataSourceProvider2);

  @override
  Future<ApiResponse<RetailOutlet>> updateBusinessDetails(
      BusinessVerificationRequest params) {
    return dioInterceptor(
        () => _dataSource.updateBusinessDetails(params), _ref);
  }

  @override
  Future<ApiResponse<InitiateStripeResponse>> initiateStripeVerification(
      InitiateStripeRequest request) {
    return dioInterceptor(
        () => _dataSource2.initiateStripeVerification(request), _ref);
  }

  @override
  Future<ApiResponse<BankData>> getBankList() {
    return dioInterceptor(() => _dataSource2.getBankList(), _ref);
  }
}
