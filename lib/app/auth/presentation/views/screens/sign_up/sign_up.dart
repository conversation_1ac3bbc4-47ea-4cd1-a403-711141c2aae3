import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/auth/data/models/verified_otp.dart';
import 'package:td_procurement/app/auth/domain/params/check_phone.dart';
import 'package:td_procurement/app/auth/domain/params/login_token.dart';
import 'package:td_procurement/app/auth/domain/params/partner_reg.dart';
import 'package:td_procurement/app/auth/domain/params/send_otp.dart';
import 'package:td_procurement/app/auth/domain/params/verify_otp.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_up/company_registration.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_up/outlet_registration.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_up/partner_registration.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_up/retailer_registration.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_up/sign_up_options.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/verification_code/verification_code.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/flexible_box/flexible_box.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class SignUp extends ConsumerStatefulWidget {
  const SignUp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _SignUp();
  }
}

class _SignUp extends ConsumerState<SignUp> {
  final TextEditingController _codeController = TextEditingController();
  final ValueNotifier<bool> _loader = ValueNotifier(false);
  final ValueNotifier<bool> _disableConfirmOTPButton = ValueNotifier(false);
  final ValueNotifier<SignUpView> _view = ValueNotifier(SignUpView.options);
  SignUpOption? option;
  OutletParams? _outletParams;
  RetailerParams? _retailerParams;
  CompanyParams? _companyParams;
  PartnerParams? _partnerParams;

  @override
  void dispose() {
    _codeController.dispose();
    _loader.dispose();
    _view.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      body: FlexibleConstrainedBox(
        minHeight: 650,
        padding: EdgeInsets.zero,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(
              kSideBrandSvg,
              height: MediaQuery.of(context).size.flexible(650),
            ),
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: _view,
                builder: (context, state, _) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 70,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                              border: Border.all(color: Palette.kE7E7E7),
                              color: Colors.white),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Gap(40),
                              if (state != SignUpView.options &&
                                  state != SignUpView.success &&
                                  state != SignUpView.otp)
                                InkWell(
                                  onTap: onPreviousView,
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(kChevronLeftSvg),
                                      const Gap(10),
                                      Text(
                                        'Back',
                                        style: theme.textTheme.bodyLarge
                                            ?.copyWith(color: Palette.k6B797C),
                                      ),
                                    ],
                                  ),
                                ),
                              const Spacer(),
                              Text(
                                'Already have an account?',
                                style: theme.textTheme.bodyMedium
                                    ?.copyWith(color: Palette.blackSecondary),
                              ),
                              const Gap(20),
                              ElevatedButton(
                                onPressed: () => context.goNamed(kLoginRoute),
                                child: const Text('Login'),
                              ),
                              const Gap(30),
                            ],
                          ),
                        ),
                      ),
                      const Gap(100),
                      switch (state) {
                        SignUpView.outletRegister => OutletRegistration(
                            (params) {
                              _outletParams = params;
                              _view.value = SignUpView.retailerRegister;
                            },
                            _outletParams,
                          ),
                        SignUpView.otp => VerificationCodeView(
                            controller: _codeController,
                            loader: _loader,
                            onPop: () {
                              _codeController.text = '';
                              _view.value = SignUpView.outletRegister;
                            },
                            onPressed: validateOTP,
                            resendOtp: resendOtp,
                            route: AuthRoute.signUp,
                            isButtonDisabled: _disableConfirmOTPButton,
                          ),
                        SignUpView.options => SignUpOptions(onNext: (option) {
                            this.option = option;
                            _view.value = switch (option) {
                              SignUpOption.retailer =>
                                SignUpView.outletRegister,
                              SignUpOption.partner =>
                                SignUpView.companyRegister,
                            };
                          }),
                        SignUpView.retailerRegister => RetailerRegistration(
                              (params) {
                            _retailerParams = params;
                            _view.value = SignUpView.otp;
                          },
                              _retailerParams,
                              _outletParams!.address!.countryShort,
                              (params) => _retailerParams = params),
                        SignUpView.companyRegister => CompanyRegistration(
                            (params) {
                              _companyParams = params;
                              _view.value = SignUpView.partnerRegister;
                            },
                            _companyParams,
                          ),
                        SignUpView.partnerRegister => PartnerRegistration(
                              (params) {
                            _partnerParams = params;
                            signUpPartner();
                          },
                              _partnerParams,
                              _loader,
                              _companyParams!.address!.countryShort,
                              (params) => _partnerParams = params),
                        SignUpView.success => const SignUpSuccess(),
                      }
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> resendOtp() async {
    final params = SendOTParams(
      mode: PhoneAuthMode.Email,
      email: _retailerParams!.email,
      url: '${ref.read(appConfigProvider).firebaseServiceUrl}$kSendOtpApiPath',
    );
    final res = await ref.read(
      sendOtpUseCaseProvider(params),
    );
    _loader.value = false;
    switch (res) {
      case Failure error:
        if (mounted) {
          Toast.apiError(error.error, context);
        }
        return false;
      case _:
        return true;
    }
  }

  Future<void> validateOTP() async {
    final otp = _codeController.text;
    if (otp.length == 6) {
      _loader.value = true;
      final params = VerifyOTParams(
        email: _retailerParams!.email,
        token: otp,
        countryCode: '',
        url:
            '${ref.read(appConfigProvider).firebaseServiceUrl}$kVerifyOtpApiPath',
      );
      final res = await ref.read(
        verifyOtpUseCaseProvider(params),
      );
      switch (res) {
        case Success verified:
          verifyEmailAddress(verified.data);
        case Failure error:
          if (mounted) {
            _codeController.text = '';
            _loader.value = false;
            Toast.apiError(error.error, context);
          }
      }
    } else {
      Toast.error("Invalid OTP Format", context);
    }
  }

  void verifyEmailAddress(VerifiedOTP verified) async {
    final response = await ref.read(
      verifyEmailUseCaseProvider(
        CheckPhoneParams(email: _retailerParams!.email),
      ),
    );
    switch (response) {
      case Success(data: var data):
        if (data.hasRetailStore) {
          loginWithToken(verified);
        }
      case Failure error:
        {
          if (error.statusCode == 404) {
            signUpRetailer(verified);
          } else if (mounted) {
            _loader.value = false;
            Toast.apiError(error.error, context);
          }
        }
    }
  }

  Future<void> signUpRetailer(VerifiedOTP data) async {
    final res = await ref.read(
      signUpUseCaseProvider(
        {..._outletParams!.toMap(), ..._retailerParams!.toMap()},
      ),
    );
    switch (res) {
      case Success():
        if (mounted) {
          loginWithToken(data);
        }
      case Failure error:
        _loader.value = false;
        if (mounted) {
          Toast.apiError(error.error, context);
        }
    }
  }

  Future<void> signUpPartner() async {
    _loader.value = true;
    final res = await ref.read(
      registerPartnerUseCaseProvider(
        PartnerRegParams(
          partnerParams: _partnerParams?.toMap(),
          companyParams: _companyParams?.toMap(),
        ),
      ),
    );
    _loader.value = false;
    switch (res) {
      case Success():
        if (mounted) _view.value = SignUpView.success;
      case Failure error:
        if (mounted) {
          Toast.apiError(error.error, context);
        }
    }
  }

  Future<void> loginWithToken(VerifiedOTP data) async {
    final res = await ref.read(userControllerProvider.notifier).loginWithOtp(
          LoginTokenParams(
              email: _retailerParams!.email, token: data.accessToken),
        );
    _loader.value = false;
    switch (res) {
      case Failure error:
        if (mounted) {
          Toast.apiError(error.error, context);
        }
      case _:
    }
  }

  onPreviousView() {
    _view.value = switch (_view.value) {
      SignUpView.outletRegister => SignUpView.options,
      SignUpView.otp => SignUpView.retailerRegister,
      SignUpView.options => SignUpView.options,
      SignUpView.retailerRegister => SignUpView.outletRegister,
      SignUpView.companyRegister => SignUpView.options,
      SignUpView.partnerRegister => SignUpView.companyRegister,
      SignUpView.success => SignUpView.options,
    };
  }
}

enum SignUpView {
  outletRegister,
  otp,
  options,
  retailerRegister,
  companyRegister,
  partnerRegister,
  success
}

class SignUpSuccess extends StatelessWidget {
  const SignUpSuccess({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SizedBox(
      width: 400,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Palette.primary,
            child: SvgPicture.asset(
              kBrandMailSvg,
              width: 50,
              height: 50,
            ),
          ),
          const Gap(20),
          Text(
            'Thank you for signing up to our distribution network!',
            style: theme.textTheme.titleMedium
                ?.copyWith(color: Palette.primaryBlack),
          ),
          const Gap(10),
          Text(
            'We have sent you an email detailing the next steps.',
            style: theme.textTheme.bodyLarge
                ?.copyWith(color: Palette.blackSecondary),
          )
        ],
      ),
    );
  }
}
