import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/auth/data/models/verified_otp.dart';
import 'package:td_procurement/app/auth/domain/params/check_phone.dart';
import 'package:td_procurement/app/auth/domain/params/login_token.dart';
import 'package:td_procurement/app/auth/domain/params/send_otp.dart';
import 'package:td_procurement/app/auth/domain/params/verify_otp.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_in/sign_in_email.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/verification_code/verification_code.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/components/flexible_box/flexible_box.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/storage_keys/storage_keys.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class SignIn extends ConsumerStatefulWidget {
  const SignIn({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _SignIn();
  }
}

class _SignIn extends ConsumerState<SignIn> {
  final TextEditingController _controller = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final ValueNotifier<bool> _loader = ValueNotifier(false);
  final ValueNotifier<bool> _disableButton = ValueNotifier(true);
  final ValueNotifier<bool> _disableConfirmOTPButton = ValueNotifier(true);
  final ValueNotifier<SignInView> _view = ValueNotifier(SignInView.email);
  String? email;
  bool? hasRetailStore;

  @override
  void dispose() {
    _controller.dispose();
    _codeController.dispose();
    _loader.dispose();
    _view.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      body: FlexibleConstrainedBox(
        minHeight: 500,
        padding: EdgeInsets.zero,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(
              kSideBrandSvg,
              height: MediaQuery.of(context).size.flexible(500),
            ),
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: _view,
                builder: (context, state, _) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 70,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                              border: Border.all(color: Palette.kE7E7E7),
                              color: Colors.white),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Gap(40),
                              if (state == SignInView.otp)
                                InkWell(
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(kChevronLeftSvg),
                                      const Gap(10),
                                      Text(
                                        'Back',
                                        style: theme.textTheme.bodyLarge
                                            ?.copyWith(color: Palette.k6B797C),
                                      ),
                                    ],
                                  ),
                                  onTap: () => _view.value = SignInView.email,
                                ),
                              const Spacer(),
                              Text(
                                "Don't have an account?",
                                style: theme.textTheme.bodyMedium
                                    ?.copyWith(color: Palette.blackSecondary),
                              ),
                              const Gap(20),
                              ElevatedButton(
                                onPressed: () =>
                                    context.goNamed(kRegisterRoute),
                                child: const Text('Sign up'),
                              ),
                              const Gap(30),
                            ],
                          ),
                        ),
                      ),
                      const Gap(100),
                      SizedBox(
                        width: 379,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(kLogoSvg),
                            const Gap(30),
                            switch (state) {
                              SignInView.email => SignInEmailView(
                                  controller: _controller,
                                  loader: _loader,
                                  onPressed: verifyEmailAddress,
                                  isButtonDisabled: _disableButton,
                                ),
                              SignInView.otp => VerificationCodeView(
                                  controller: _codeController,
                                  loader: _loader,
                                  route: AuthRoute.signIn,
                                  onPop: () => _view.value = SignInView.email,
                                  onPressed: validateOTP,
                                  resendOtp: resendOtp,
                                  isButtonDisabled: _disableConfirmOTPButton,
                                )
                            }
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> verifyEmailAddress() async {
    final store = ref.read(storageProvider);
    store.refresh().whenComplete(() async {
      if (store.check(StorageKeys.user)) {
        if (mounted) {
          Toast.info(
              'You signed in with another tab or window, kindly reload to refresh your session',
              context);
          return;
        }
      }
      if (Validators.validateEmail(_controller.text.trim())) {
        _loader.value = true;
        email = _controller.text.trim().toString().toLowerCase();
        final response = await ref.read(
          verifyEmailUseCaseProvider(
            CheckPhoneParams(email: email),
          ),
        );
        switch (response) {
          case Success(data: var data):
            sendOtpToEmail(email!, data.hasRetailStore);
          case Failure error:
            {
              _loader.value = false;
              if (mounted) {
                Toast.apiError(error.error, context);
              }
            }
        }
      } else {
        if (mounted) {
          Toast.error("Invalid Email Address", context);
        }
      }
    });
  }

  Future<void> sendOtpToEmail(String email, bool hasRetailStore) async {
    this.hasRetailStore = hasRetailStore;
    final params = SendOTParams(
      mode: PhoneAuthMode.Email,
      email: email,
      url: '${ref.read(appConfigProvider).firebaseServiceUrl}$kSendOtpApiPath',
    );
    final res = await ref.read(
      sendOtpUseCaseProvider(params),
    );
    _loader.value = false;
    switch (res) {
      case Success():
        _view.value = SignInView.otp;
      case Failure error:
        if (mounted) {
          Toast.apiError(error.error, context);
        }
    }
  }

  Future<bool> resendOtp() async {
    final params = SendOTParams(
      mode: PhoneAuthMode.Email,
      email: email,
      url: '${ref.read(appConfigProvider).firebaseServiceUrl}$kSendOtpApiPath',
    );
    final res = await ref.read(
      sendOtpUseCaseProvider(params),
    );
    _loader.value = false;
    switch (res) {
      case Failure error:
        if (mounted) {
          Toast.apiError(error.error, context);
        }
        return false;
      case _:
        return true;
    }
  }

  Future<void> validateOTP() async {
    final otp = _codeController.text;
    if (otp.length == 6) {
      _loader.value = true;
      final params = VerifyOTParams(
        email: _controller.text.trim().toLowerCase(),
        token: otp,
        countryCode: '',
        url:
            '${ref.read(appConfigProvider).firebaseServiceUrl}$kVerifyOtpApiPath',
      );
      final res = await ref.read(
        verifyOtpUseCaseProvider(params),
      );
      switch (res) {
        case Success data:
          {
            if (hasRetailStore ?? false) {
              loginWithToken(data.data);
            } else {
              // ref.read(storageProvider).save(StorageKeys.tdAppEmail, email);
              _loader.value = false;
              if (mounted) {
                context.goNamed(kRegisterRoute);
              }
            }
          }
        case Failure error:
          if (mounted) {
            _loader.value = false;
            Toast.apiError(error.error, context);
          }
      }
    } else {
      Toast.error("Invalid OTP Format", context);
    }
  }

  Future<void> loginWithToken(VerifiedOTP data) async {
    final res = await ref.read(userControllerProvider.notifier).loginWithOtp(
          LoginTokenParams(email: email, token: data.accessToken),
        );
    _loader.value = false;
    switch (res) {
      case Failure error:
        if (mounted) {
          Toast.apiError(error.error, context);
        }
      case _:
    }
  }
}

enum SignInView { email, otp }
