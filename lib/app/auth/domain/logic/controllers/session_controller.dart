import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_before_logout.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_login.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_logout.dart';

class SessionController extends ChangeNotifier
    implements OnLogout, OnBeforeLogout, OnLogin {
  String? accessToken;

  SessionController() : super();

  void setToken(String? token) {
    if (token != null && accessToken != token) {
      accessToken = token;
    }
  }

  @override
  Future<void> onLogout() async {
    accessToken = '';
    notifyListeners();
  }

  @override
  Future<void> onBeforeLogout() async {
    accessToken = '';
    notifyListeners();
  }

  @override
  Future<void> onLogin(User user) async {
    setToken(user.token);
    notifyListeners();
  }
}

final sessionController = Provider((_) => SessionController());
