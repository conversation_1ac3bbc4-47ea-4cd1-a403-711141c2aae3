class InitiateStripeResponse {
  late final String? verificationUrl;
  late final String? redirectUrl;

  InitiateStripeResponse({this.verificationUrl, this.redirectUrl});

  InitiateStripeResponse.fromJson(Map<String, dynamic> json) {
    verificationUrl = json['verificationUrl'];
    redirectUrl = json['redirectUrl'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};

    _data['verificationUrl'] = verificationUrl;
    _data['redirectUrl'] = verificationUrl;

    return _data;
  }
}

class InitiateStripeRequest {
  late final String? redirectUrl;

  InitiateStripeRequest({this.redirectUrl});

  InitiateStripeRequest.fromJson(Map<String, dynamic> json) {
    redirectUrl = json['redirectUrl'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
//    if (redirectUrl != null) {
    _data['redirectUrl'] = redirectUrl;

    // }

    return _data;
  }
}
