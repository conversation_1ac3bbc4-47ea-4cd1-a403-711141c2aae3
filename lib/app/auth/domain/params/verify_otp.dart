class VerifyOTParams {
  VerifyOTParams({
    this.phoneNumber,
    this.email,
    required this.token,
    required this.countryCode,
    required this.url,
  }) : assert(
  (phoneNumber != null && email == null) ||
      (phoneNumber == null && email != null),
  'Either phoneNumber or email must be provided, but not both.');

  final String? phoneNumber;
  final String? email;
  final String token;
  final String countryCode;
  final String url;

  Map<String, String> toMap() {
    if (phoneNumber != null) {
      return {
        'phoneNumber': phoneNumber!,
        'token': token,
        'countryCode': countryCode,
      };
    }

    if (email != null) {
      return {
        'email': email!,
        'token': token,
        'countryCode': countryCode,
      };
    }

    return {
      'token': token,
      'countryCode': countryCode,
    };
  }
}