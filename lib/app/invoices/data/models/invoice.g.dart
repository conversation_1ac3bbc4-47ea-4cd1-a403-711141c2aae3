// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Invoice _$InvoiceFromJson(Map<String, dynamic> json) => Invoice(
      json['outletBusinessName'] as String,
      json['total'] as num,
      Invoice.invoiceNumberFromJson(json['invoiceNumber'] as Object),
      DateTime.parse(json['createdAt'] as String),
      json['dueAt'] == null
          ? DateTime.now()
          : DateTime.parse(json['dueAt'] as String),
      $enumDecode(_$InvoiceStatusEnumMap, json['status']),
      CurrencyData.fromJson(json['currency'] as Map<String, dynamic>),
      json['transactions'] == null
          ? []
          : Invoice.transactionFromMap(json['transactions'] as List),
      ShippingAddress.fromMap(json['shippingAddress'] as Map<String, dynamic>),
      Invoice.orderFromMap(json['orders'] as List?),
      json['shippingDiscount'] as num,
      json['subTotal'] as num,
      json['amount'] as num,
      json['shippingCosts'] as num,
      json['processingCost'] as num,
      json['taxes'] as num,
      Invoice.orderItemFromMap(json['items'] as List?),
      json['bankAccount'] == null
          ? null
          : BankAccount.fromJson(json['bankAccount'] as Map<String, dynamic>),
      json['retailOutletId'] as String?,
      $enumDecode(_$InvoiceTypeEnumMap, json['invoiceType']),
    );

Map<String, dynamic> _$InvoiceToJson(Invoice instance) => <String, dynamic>{
      'outletBusinessName': instance.outletBusinessName,
      'total': instance.total,
      'invoiceNumber': instance.invoiceNumber,
      'createdAt': instance.createdAt.toIso8601String(),
      'dueAt': instance.dueAt.toIso8601String(),
      'currency': instance.currency,
      'status': _$InvoiceStatusEnumMap[instance.status]!,
      'bankAccount': instance.bankAccount,
      'shippingDiscount': instance.shippingDiscount,
      'shippingCosts': instance.shippingCosts,
      'processingCost': instance.processingCost,
      'subTotal': instance.subTotal,
      'amount': instance.amount,
      'taxes': instance.taxes,
      'retailOutletId': instance.retailOutletId,
      'orders': Invoice.orderToMap(instance.orders),
      'items': Invoice.orderItemToMap(instance.items),
      'shippingAddress': Invoice.addressToMap(instance.address),
      'transactions': Invoice.transactionToMap(instance.transactions),
      'invoiceType': _$InvoiceTypeEnumMap[instance.invoiceType]!,
    };

const _$InvoiceStatusEnumMap = {
  InvoiceStatus.all: 'all',
  InvoiceStatus.unpaid: 'unpaid',
  InvoiceStatus.paid: 'paid',
  InvoiceStatus.overdue: 'overdue',
  InvoiceStatus.cancelled: 'cancelled',
};

const _$InvoiceTypeEnumMap = {
  InvoiceType.purchase: 'Purchase',
  InvoiceType.sales: 'Sales',
};

PagePagination _$PagePaginationFromJson(Map<String, dynamic> json) =>
    PagePagination(
      PagePagination.pageNumberFromJson(json['page'] as Object),
      (json['perPage'] as num).toInt(),
      (json['totalPages'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PagePaginationToJson(PagePagination instance) =>
    <String, dynamic>{
      'page': instance.page,
      'perPage': instance.perPage,
      'totalPages': instance.totalPages,
    };

CurrencyData _$CurrencyDataFromJson(Map<String, dynamic> json) => CurrencyData(
      json['symbol'] as String,
      json['iso'] as String,
    );

Map<String, dynamic> _$CurrencyDataToJson(CurrencyData instance) =>
    <String, dynamic>{
      'symbol': instance.symbol,
      'iso': instance.iso,
    };

BankAccount _$BankAccountFromJson(Map<String, dynamic> json) => BankAccount(
      json['bankName'] as String?,
      json['accountName'] as String?,
      json['accountNumber'] as String?,
    );

Map<String, dynamic> _$BankAccountToJson(BankAccount instance) =>
    <String, dynamic>{
      'bankName': instance.bankName,
      'accountName': instance.accountName,
      'accountNumber': instance.accountNumber,
    };

InvoiceStatement _$InvoiceStatementFromJson(Map<String, dynamic> json) =>
    InvoiceStatement(
      (json['invoices'] as List<dynamic>?)
              ?.map((e) => Invoice.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      (json['totalCount'] as num?)?.toInt() ?? 0,
      PagePagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InvoiceStatementToJson(InvoiceStatement instance) =>
    <String, dynamic>{
      'invoices': instance.invoices,
      'totalCount': instance.totalCount,
      'pagination': instance.pagination,
    };
