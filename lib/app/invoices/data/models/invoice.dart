import 'package:json_annotation/json_annotation.dart';
import 'package:td_commons_flutter/models/order_item.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/shipping_address.dart';
import 'package:td_procurement/core/models/transaction.dart';

part 'invoice.g.dart';

@JsonSerializable()
class Invoice {
  final String outletBusinessName;
  final num total;
  @JsonKey(fromJson: invoiceNumberFromJson)
  final String invoiceNumber;
  final DateTime createdAt;
  @Json<PERSON>ey(defaultValue: DateTime.now)
  final DateTime dueAt;
  final CurrencyData currency;
  final InvoiceStatus status;
  final BankAccount? bankAccount;
  final num shippingDiscount;
  final num shippingCosts;
  final num processingCost;
  final num subTotal;
  final num amount;
  final num taxes;
  final String? retailOutletId;
  @JsonKey(fromJson: orderFromMap, toJson: orderToMap)
  final List<Order>? orders;
  @JsonKey(fromJson: orderItemFromMap, toJson: orderItemToMap)
  final List<OrderItem>? items;
  @JsonKey(
      fromJson: ShippingAddress.fromMap,
      toJson: addressToMap,
      name: 'shippingAddress')
  final ShippingAddress address;
  @JsonKey(
      fromJson: transactionFromMap, toJson: transactionToMap, defaultValue: [])
  final List<Transaction> transactions;
  final InvoiceType invoiceType;

  factory Invoice.fromJson(Map<String, Object?> json) =>
      _$InvoiceFromJson(json);

  Invoice(
    this.outletBusinessName,
    this.total,
    this.invoiceNumber,
    this.createdAt,
    this.dueAt,
    this.status,
    this.currency,
    this.transactions,
    this.address,
    this.orders,
    this.shippingDiscount,
    this.subTotal,
    this.amount,
    this.shippingCosts,
    this.processingCost,
    this.taxes,
    this.items,
    this.bankAccount,
    this.retailOutletId,
    this.invoiceType,
  );

  Map<String, Object?> toJson() => _$InvoiceToJson(this);

  Transaction get transaction => Transaction.fromMap({
        ...?transactions.firstOrNull?.toMap(),
        'amount': amount,
        'itemTotal': total,
        'shippingCost': shippingCosts,
        'processingCost': processingCost,
        'tax': taxes,
        'status': status.name,
        'discounts': shippingDiscount
      });

  factory Invoice.loadValue() => Invoice(
        "bill",
        1000,
        "567890",
        DateTime.now(),
        DateTime.now(),
        InvoiceStatus.all,
        CurrencyData('', ''),
        [],
        ShippingAddress(id: ''),
        [],
        0,
        0,
        0,
        0,
        0,
        0,
        [],
        BankAccount('', '', ''),
        '',
        InvoiceType.purchase,
      );

  static List<Transaction> transactionFromMap(List<dynamic> transactions) {
    return transactions
        .map((e) => Transaction.fromMap(e as Map<String, dynamic>))
        .toList();
  }

  static String invoiceNumberFromJson(Object number) {
    return number is int ? number.toString() : number as String;
  }

  static List<dynamic> transactionToMap(List<Transaction> transactions) {
    return [];
  }

  static List<Order> orderFromMap(List<dynamic>? orders) {
    return orders == null
        ? []
        : orders.map((e) => Order.fromMap(e as Map<String, dynamic>)).toList();
  }

  static List<dynamic> orderToMap(List<Order>? orders) {
    return [];
  }

  static List<OrderItem>? orderItemFromMap(List<dynamic>? items) {
    return items == null
        ? []
        : items
            .map((e) => OrderItem.fromMap(e as Map<String, dynamic>))
            .toList();
  }

  static List<dynamic> orderItemToMap(List<OrderItem>? orders) {
    return [];
  }

  static Map<String, dynamic> addressToMap(ShippingAddress address) {
    return address.toMap();
  }
}

@JsonSerializable()
class PagePagination {
  @JsonKey(fromJson: pageNumberFromJson)
  final int page;
  final int perPage;
  final int? totalPages;

  factory PagePagination.fromJson(Map<String, Object?> json) =>
      _$PagePaginationFromJson(json);
  Map<String, Object?> toJson() => _$PagePaginationToJson(this);
  PagePagination(this.page, this.perPage, this.totalPages);

  static int pageNumberFromJson(Object number) {
    return number is String ? int.parse(number) : number as int;
  }
}

@JsonSerializable()
class CurrencyData {
  final String symbol;
  final String iso;

  factory CurrencyData.fromJson(Map<String, Object?> json) =>
      _$CurrencyDataFromJson(json);
  Map<String, Object?> toJson() => _$CurrencyDataToJson(this);
  CurrencyData(this.symbol, this.iso);
}

@JsonSerializable()
class BankAccount {
  final String? bankName;
  final String? accountName;
  final String? accountNumber;

  factory BankAccount.fromJson(Map<String, Object?> json) =>
      _$BankAccountFromJson(json);
  Map<String, Object?> toJson() => _$BankAccountToJson(this);
  BankAccount(this.bankName, this.accountName, this.accountNumber);
}

@JsonSerializable()
class InvoiceStatement {
  @JsonKey(defaultValue: [])
  final List<Invoice> invoices;
  @JsonKey(defaultValue: 0)
  final int totalCount;
  final PagePagination pagination;

  factory InvoiceStatement.fromJson(Map<String, Object?> json) =>
      _$InvoiceStatementFromJson(json);
  Map<String, Object?> toJson() => _$InvoiceStatementToJson(this);
  InvoiceStatement(this.invoices, this.totalCount, this.pagination);
}

enum InvoiceStatus {
  all('All invoices', '#0679FF'),
  unpaid('Unpaid', '#0679FF'),
  paid('Paid', '#08AA49'),
  overdue('Overdue', '#FF0606'),
  cancelled('Cancelled', '#FF0606');

  final String value;
  final String foregroundColor;
  const InvoiceStatus(this.value, this.foregroundColor);
}

enum PayoutStatus {
  all('All', '#0679FF'),
  inProgress('In Progress', '#FF0606'),
  completed('Completed', '#FF0606');

  final String value;
  final String foregroundColor;
  const PayoutStatus(this.value, this.foregroundColor);
}

enum PayoutInfoStatus {
  invoices('Invoices', '#0679FF'),
  customers('Customers', '#0679FF');

  final String value;
  final String foregroundColor;
  const PayoutInfoStatus(this.value, this.foregroundColor);
}

enum InvoiceType {
  @JsonValue('Purchase')
  purchase('Purchase'),
  @JsonValue('Sales')
  sales('Sales');

  final String value;
  const InvoiceType(this.value);

  String get title => switch (this) {
        InvoiceType.purchase => 'Purchase Invoice',
        InvoiceType.sales => 'Sales Invoice',
      };

  static InvoiceType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'purchase':
      case 'Purchase':
        return InvoiceType.purchase;
      case 'sales':
      case 'Sales':
        return InvoiceType.sales;
      default:
        throw ArgumentError('Invalid InvoiceType value: $value');
    }
  }

  @override
  String toString() => value;
}
