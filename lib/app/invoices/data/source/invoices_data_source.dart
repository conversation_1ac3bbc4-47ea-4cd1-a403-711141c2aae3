import 'dart:convert';
import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/domain/params/invoices_params.dart';
import 'package:td_procurement/app/invoices/domain/params/payout_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

class InvoicesDataSourceImplementation extends InvoicesDataSource {
  final Ref _ref;

  InvoicesDataSourceImplementation(this._ref);

  late final TdApiClient _apiClient = _ref.read(apiClientProvider);
  late final config = _ref.read(appConfigProvider);

  @override
  Future<InvoiceStatement> fetchInvoiceStatement(InvoicesParams params) async {
    final res = await _apiClient.get(
      "${config.awsApiUrlV2}$kInvoicesApiPath",
      queryParameters: params.toMap(),
    );
    return InvoiceStatement.fromJson(res.data['data']);
  }

  @override
  Future<String> downloadInvoice(String id) async {
    final res =
        await _apiClient.get('${config.consoleUrl}$kGetInvoiceApiPath/$id');
    return res.data['data']['invoiceUrl'];
  }

  @override
  Future<String> downloadPayoutInvoice(PayoutInvoiceParams params) async {
    final res = await _apiClient.get(
        'https://console.sandbox.tradedepot.co/api/v3/manager-invoice/${params.outletId}/${params.orderId}');
    return res.data['data']['invoiceUrl'];
  }

  @override
  Future<InvoiceStatement> getSalesInvoices(InvoicesParams params) async {
    final res = await _apiClient.get(
      "${config.consoleUrl}$kSalesInvoicesApiPath",
      queryParameters: params.toMap(),
    );

    return InvoiceStatement.fromJson(res.data['data']);
  }

  @override
  Future<String> downloadSalesInvoice(String orderId) async {
    final res = await _apiClient
        .get('${config.consoleUrl}$kGetSalesInvoiceApiPath/$orderId');
    return res.data['data']['invoiceUrl'];
  }

  @override
  Future<PayoutResponse> getPayoutData(PayoutParams params) async {
    final res = await _apiClient.get(
        '${config.consoleUrl}/api/v3/partners/collections',
        queryParameters: params.toMap());

    return PayoutResponse.fromJson(res.data);
  }

  @override
  Future<InvoiceDetailResult> fetchInvoiceDetail(
      InvoiceDetailParams params) async {
    final res = await _apiClient.get(
        '${config.consoleUrl}${params.path.replaceFirst(':orderId', params.orderId)}');
    return InvoiceDetailResult.fromMap(res.data['data']);
  }

  @override
  Future<Payout> getPayoutDetails(String reference) async {
    final res = await _apiClient.get(
        '${config.consoleUrl}${kGetPayoutDetailsApiPath.replaceFirst(':reference', reference)}');
    log(jsonEncode(res.data));
    return Payout.fromMap(res.data['data']);
  }
}

abstract class InvoicesDataSource {
  Future<InvoiceStatement> fetchInvoiceStatement(InvoicesParams params);
  Future<InvoiceStatement> getSalesInvoices(InvoicesParams params);
  Future<String> downloadInvoice(String id);
  Future<String> downloadPayoutInvoice(PayoutInvoiceParams params);
  Future<String> downloadSalesInvoice(String orderId);

  Future<PayoutResponse> getPayoutData(PayoutParams params);
  Future<InvoiceDetailResult> fetchInvoiceDetail(InvoiceDetailParams params);
  Future<Payout> getPayoutDetails(String reference);
}

final invoicesDataProvider = Provider<InvoicesDataSource>((ref) {
  return InvoicesDataSourceImplementation(ref);
});
