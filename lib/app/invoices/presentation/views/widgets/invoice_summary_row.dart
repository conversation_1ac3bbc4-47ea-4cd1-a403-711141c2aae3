import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class InvoiceSummaryRow extends StatelessWidget {
  final String title;
  final String content;
  final bool? showBorder;
  final bool? useBoldFontWeight;
  final int? flex;
  const InvoiceSummaryRow(
    this.title,
    this.content, {
    super.key,
    this.showBorder = true,
    this.useBoldFontWeight = false,
    this.flex,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: flex ?? 2,
            child: Container(),
          ),
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    border: Border(
                        bottom: showBorder!
                            ? BorderSide(color: Palette.kE7E7E7)
                            : BorderSide.none),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          title,
                          style: useBoldFontWeight!
                              ? textTheme.headlineSmall?.copyWith(fontSize: 14)
                              : textTheme.bodyMedium,
                        ),
                      ),
                      Flexible(
                        child: Text(
                          content,
                          style: useBoldFontWeight!
                              ? textTheme.headlineSmall?.copyWith(fontSize: 14)
                              : textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
