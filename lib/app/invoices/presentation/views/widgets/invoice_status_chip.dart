import 'package:flutter/material.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class InvoiceStatusChip extends StatelessWidget {
  final InvoiceStatus status;

  const InvoiceStatusChip({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 22,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(34),
          color: HexColor(status.foregroundColor).withOpacity(0.1),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
          child: Text(
            status.value,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                  color: HexColor(status.foregroundColor),
                ),
          ),
        ),
      ),
    );
  }
}
