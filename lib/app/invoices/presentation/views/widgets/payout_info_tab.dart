import 'package:flutter/material.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class PayoutInfoTab extends StatelessWidget {
  final PayoutInfoStatus option;
  final bool isActive;
  const PayoutInfoTab(
      {super.key, required this.option, required this.isActive});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: isActive
            ? Border.all(width: 2, color: Palette.primaryBlack)
            : Border.all(color: Palette.stroke),
        boxShadow: [
          if (isActive)
            const BoxShadow(
                offset: Offset(0, 1),
                blurRadius: 1,
                spreadRadius: 0,
                color: Palette.k0000001A)
        ],
      ),
      padding: const EdgeInsets.all(4),
      margin: const EdgeInsets.only(right: 10),
      width: 157,
      height: 41,
      child: Center(
        child: Text(
          option.value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: isActive ? null : Palette.blackSecondary,
              fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
}
