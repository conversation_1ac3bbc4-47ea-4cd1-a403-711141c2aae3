import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

class InvoicesParams {
  final int batch;
  final int limit;
  final InvoiceType type;
  final InvoiceStatus status;
  final DateTime? startDate;
  final DateTime? endDate;

  InvoicesParams(
      {this.batch = 1,
      this.limit = 10,
      this.status = InvoiceStatus.all,
      this.endDate,
      this.startDate,
      required this.type});

  InvoicesParams paginate(int index) => InvoicesParams(
      batch: index,
      status: status,
      limit: limit,
      startDate: startDate,
      type: type,
      endDate: endDate);

  InvoicesParams switchStatus(InvoiceStatus tab) => InvoicesParams(
      batch: batch,
      status: tab,
      limit: limit,
      type: type,
      startDate: startDate,
      endDate: endDate);

  InvoicesParams filterByDate(DateTime? start, DateTime? end) => InvoicesParams(
      batch: batch,
      status: status,
      limit: limit,
      type: type,
      startDate: start,
      endDate: end);

  Map<String, Object?> toMap() {
    return {
      switch (type) {
        InvoiceType.purchase => 'batch',
        InvoiceType.sales => 'page',
      }: batch,
      'limit': limit,
      if (status != InvoiceStatus.all) 'status': status.name,
      if (startDate != null && endDate != null) ...{
        'startDate': switch (type) {
          InvoiceType.purchase => startDate!.millisecondsSinceEpoch ~/ 1000,
          InvoiceType.sales => startDate,
        },
        'endDate': switch (type) {
          InvoiceType.purchase => endDate!
                  .add(
                    const Duration(hours: 23, minutes: 59),
                  )
                  .millisecondsSinceEpoch ~/
              1000,
          InvoiceType.sales => endDate!.add(
              const Duration(hours: 23, minutes: 59),
            ),
        },
      }
    };
  }
}

class InvoiceDetailParams {
  final String orderId;
  final String outletId;
  final String invoiceType;

  String get path => invoiceType.toLowerCase().contains('sales')
      ? kSalesInvoiceDetailsPath
      : kPurchaseInvoiceDetailsPath;

  InvoiceDetailParams(
      {required this.orderId,
      required this.outletId,
      required this.invoiceType});
}

class InvoiceDetailResult {
  final Invoice invoice;
  final String invoiceUrl;

  InvoiceDetailResult(this.invoice, this.invoiceUrl);

  factory InvoiceDetailResult.fromMap(Map<String, dynamic> map) {
    return InvoiceDetailResult(
      Invoice.fromJson(map['invoice']),
      map['invoiceUrl'],
    );
  }
}
