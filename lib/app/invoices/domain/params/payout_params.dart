import 'package:td_procurement/app/invoices/data/models/invoice.dart';

class PayoutParams {
  final int batch;
  final int limit;
  final PayoutStatus status;
  final String? query;

  PayoutParams(
      {this.batch = 1,
      this.limit = 10,
      this.status = PayoutStatus.all,
      this.query});

  PayoutParams paginate(int index) =>
      PayoutParams(batch: index, status: status, limit: limit, query: query);

  PayoutParams switchStatus(PayoutStatus tab) =>
      PayoutParams(batch: batch, status: tab, limit: limit, query: query);

  PayoutParams querySearch(String text, PayoutStatus tab) =>
      PayoutParams(batch: batch, status: tab, limit: limit, query: text);

  Map<String, Object?> toMap() {
    return {
      'batch': batch,
      'limit': limit,
      if (query != null && query != '') 'searchQuery': query,
      if (status != PayoutStatus.all) 'status': mapStatus(status.name),
    };
  }

  String mapStatus(String status) {
    if (status.toLowerCase() == "completed") {
      return "success";
    } else if (status.toLowerCase() == "inprogress") {
      return "inprogress";
    } else {
      return "";
    }
  }
}

class PayoutInvoiceParams {
  String orderId;
  String outletId;

  PayoutInvoiceParams(this.orderId, this.outletId);
}
//