import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/invoices/data/models/payout.dart';
import 'package:td_procurement/app/invoices/domain/params/payout_params.dart';
import 'package:td_procurement/app/invoices/domain/use_cases/invoices_use_cases.dart';

class PayoutController
    extends AutoDisposeFamilyAsyncNotifier<PayoutResponse, PayoutParams> {
  @override
  FutureOr<PayoutResponse> build(arg) async {
    return (await ref.read(
      payoutUseCaseProvider(arg),
    ))
        .extract();
  }
}

final payoutControllerProvider = AutoDisposeAsyncNotifierProviderFamily<
    PayoutController, PayoutResponse, PayoutParams>(() {
  return PayoutController();
});

final payoutArgProvider = AutoDisposeStateProvider<PayoutParams>(
  (_) => PayoutParams(),
);
