import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/domain/params/invoices_params.dart';
import 'package:td_procurement/app/invoices/domain/use_cases/invoices_use_cases.dart';

class SalesInvoicesController
    extends AutoDisposeFamilyAsyncNotifier<InvoiceStatement, InvoicesParams> {
  @override
  FutureOr<InvoiceStatement> build(arg) async {
    return (await ref.read(
      salesInvoicesUseCaseProvider(arg),
    ))
        .extract();
  }
}

final salesInvoicesControllerProvider = AutoDisposeAsyncNotifierProviderFamily<
    SalesInvoicesController, InvoiceStatement, InvoicesParams>(() {
  return SalesInvoicesController();
});

final salesInvoicesArgProvider = AutoDisposeStateProvider<InvoicesParams>(
  (_) => InvoicesParams(type: InvoiceType.sales),
);
