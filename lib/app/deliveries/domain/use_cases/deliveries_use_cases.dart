import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/data/repo/deliveries_repo.dart';
import 'package:td_procurement/app/deliveries/domain/params/deliveries_params.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final fetchDeliveriesUseCaseProvider =
    Provider.autoDispose<FetchDeliveriesUseCase>((ref) {
  return (FetchDeliveriesParam params) {
    return ref.read(deliveriesRepoProvider).fetchDeliveries(params);
  };
});

typedef FetchDeliveriesUseCase = Future<ApiResponse<DeliveriesResponse>>
    Function(FetchDeliveriesParam params);

final fetchDeliveryOrdersUseCaseProvider =
    Provider.autoDispose<FetchDeliveryOrdersUseCase>((ref) {
  return (String deliveryId) {
    return ref.read(deliveriesRepoProvider).fetchDeliveryOrders(deliveryId);
  };
});

typedef FetchDeliveryOrdersUseCase = Future<ApiResponse<List<Order>>> Function(
    String deliveryId);

final cancelDeliveryUseCaseProvider =
    Provider.autoDispose<CancelDeliveryUseCase>((ref) {
  return (String deliveryId) {
    return ref.read(deliveriesRepoProvider).cancelDelivery(deliveryId);
  };
});

typedef CancelDeliveryUseCase = Future<ApiResponse<void>> Function(
    String deliveryId);

final fetchDeliveryDetailsUseCaseProvider =
    Provider.autoDispose<FetchDeliveryDetailsUseCase>((ref) {
  return (String deliveryId) {
    return ref.read(deliveriesRepoProvider).fetchDeliveryDetails(deliveryId);
  };
});

typedef FetchDeliveryDetailsUseCase = Future<ApiResponse<Deliveries>> Function(
    String deliveryId);
