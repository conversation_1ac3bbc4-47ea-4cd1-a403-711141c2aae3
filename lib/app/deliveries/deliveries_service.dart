import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/domain/params/deliveries_params.dart';
import 'package:td_procurement/app/deliveries/domain/use_cases/deliveries_use_cases.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final deliveriesServiceProvider =
    Provider.autoDispose<DeliveriesService>((ref) {
  final fetchDeliveriesUseCase = ref.read(fetchDeliveriesUseCaseProvider);
  final fetchDeliveryOrdersUseCase =
      ref.read(fetchDeliveryOrdersUseCaseProvider);
  final cancelDeliveryUseCase = ref.read(cancelDeliveryUseCaseProvider);
  final fetchDeliveryDetailsUseCase =
      ref.read(fetchDeliveryDetailsUseCaseProvider);
  return DeliveriesServiceImplementation(
    fetchDeliveriesUseCase,
    fetchDeliveryOrdersUseCase,
    cancelDeliveryUseCase,
    fetchDeliveryDetailsUseCase,
  );
});

abstract class DeliveriesService {
  Future<ApiResponse<DeliveriesResponse>> fetchDeliveries(
      FetchDeliveriesParam params);
  Future<ApiResponse<List<Order>>> fetchDeliveryOrders(String deliveryId);
  Future<ApiResponse<void>> cancelDelivery(String deliveryId);
  Future<ApiResponse<Deliveries>> fetchDeliveryDetails(String deliveryId);
}

class DeliveriesServiceImplementation implements DeliveriesService {
  DeliveriesServiceImplementation(
      this._fetchDeliveriesUseCase,
      this._fetchDeliveryOrdersUseCase,
      this._cancelDeliveryUseCase,
      this._fetchDeliveryDetailsUseCase);

  final FetchDeliveriesUseCase _fetchDeliveriesUseCase;
  final FetchDeliveryOrdersUseCase _fetchDeliveryOrdersUseCase;
  final CancelDeliveryUseCase _cancelDeliveryUseCase;
  final FetchDeliveryDetailsUseCase _fetchDeliveryDetailsUseCase;

  @override
  Future<ApiResponse<DeliveriesResponse>> fetchDeliveries(
      FetchDeliveriesParam params) async {
    return await _fetchDeliveriesUseCase.call(params);
  }

  @override
  Future<ApiResponse<List<Order>>> fetchDeliveryOrders(
      String deliveryId) async {
    return await _fetchDeliveryOrdersUseCase.call(deliveryId);
  }

  @override
  Future<ApiResponse<void>> cancelDelivery(String deliveryId) async {
    return await _cancelDeliveryUseCase.call(deliveryId);
  }

  @override
  Future<ApiResponse<Deliveries>> fetchDeliveryDetails(
      String deliveryId) async {
    return await _fetchDeliveryDetailsUseCase.call(deliveryId);
  }
}
