import 'package:flutter/material.dart';

class DeliveryStatusChip extends StatelessWidget {
  final String status;

  const DeliveryStatusChip({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    final (color, label) = _getStatusInfo(status);

    return SizedBox(
      height: 22,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(34),
          color: color.withOpacity(0.1),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                  color: color,
                ),
          ),
        ),
      ),
    );
  }

  (Color, String) _getStatusInfo(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return (Colors.green, 'Completed');
      case 'pending':
        return (Colors.orange, 'Pending');
      case 'cancelled':
        return (Colors.red, 'Cancelled');
      case 'skipped':
        return (Colors.brown, 'Skipped');
      default:
        return (Colors.grey, 'Unknown');
    }
  }
}
