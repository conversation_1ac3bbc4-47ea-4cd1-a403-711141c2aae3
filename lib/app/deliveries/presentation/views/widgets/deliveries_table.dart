import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_commons_flutter/utils/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class DeliveriesTableWidget extends ConsumerStatefulWidget {
  final List<Deliveries> deliveries;

  const DeliveriesTableWidget(this.deliveries, {super.key});

  @override
  ConsumerState<DeliveriesTableWidget> createState() {
    return _DeliveriesTableWidgetState();
  }
}

class _DeliveriesTableWidgetState extends ConsumerState<DeliveriesTableWidget> {
  final loading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                final delivery = widget.deliveries[index];
                return HoverableContainer(
                    index: index,
                    builder: (isHovered) => InkWell(
                          onTap: () {
                            context.goNamed(kDeliveryDetailsRoute,
                                pathParameters: {'id': delivery.id ?? ''});
                          },
                          child: _buildTableRow(
                              delivery, isHovered, textTheme, context, ref),
                        ));
              }, childCount: widget.deliveries.length))
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTableRow(Deliveries delivery, bool isHovered,
      TextTheme textTheme, BuildContext context, WidgetRef ref) {
    final name = delivery.driver?.profile!.fullName ?? "-";
    final currency = delivery.visits[0].currency;
    final completionRate =
        (delivery.visits.where((v) => v.status == 'completed').length /
            delivery.visits.length *
            100);
    final totalQuantity = delivery.visits.fold<num>(
      0,
      (sum, visit) => sum + (visit.totalAmount ?? 0),
    );

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(0.10),
        1: FlexColumnWidth(0.65),
        2: FlexColumnWidth(.75),
        3: FlexColumnWidth(0.37),
        4: FlexColumnWidth(0.49),
        5: FlexColumnWidth(0.5),
        6: FlexColumnWidth(0.5),
      },
      children: [
        TableRow(
          decoration: BoxDecoration(
              border: Border(
            bottom: BorderSide(color: Palette.stroke),
          )),
          children: [
            Container(),
            _buildContentCell(
                Text(
                  name,
                  style: textTheme.bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                  overflow: TextOverflow.ellipsis,
                ),
                textTheme),
            _buildContentCell(
                delivery.createdAt?.toFullDateTime() ?? '-', textTheme),
            _buildContentCell(delivery.visits.length.toString(), textTheme),
            _buildContentCell(
                delivery.visits
                    .fold<num>(
                        0, (sum, visit) => sum + (visit.itemsQuantity ?? 0))
                    .toString(),
                textTheme),
            _buildContentCell(
                '${completionRate.toStringAsFixed(0)}%', textTheme),
            _buildContentCell(
                CurrencyWidget(totalQuantity, currency!.symbol!), textTheme),
            _buildContentCell(DeliveryStatusBadge(delivery.status), textTheme),
            _buildMoreOptionsButton(delivery),
            // Padding(
            //   padding: const EdgeInsets.only(top: 5.5),
            //   child: Align(
            //     alignment: Alignment.centerRight,
            //     child: Row(
            //       mainAxisAlignment: MainAxisAlignment.end,
            //       crossAxisAlignment: CrossAxisAlignment.center,
            //       children: [
            //         Flexible(
            //           child: OutlinedButton(
            //             onPressed: () {
            //               context.goNamed(kDeliveryDetailsRoute,
            //                   pathParameters: {'id': delivery.id ?? ''});

            //               // showCustomGeneralDialog(
            //               //   onDismiss: () {
            //               //     ref.read(visitsProvider.notifier).clearVisit();
            //               //   },
            //               //   context,
            //               //   percentage: 0.5,
            //               //   minRightSectionWidth: 809,
            //               //   child: DeliveriesSummary(
            //               //     deliveryId: delivery.id!,
            //               //   ),
            //               // );
            //             },
            //             style: OutlinedButton.styleFrom(
            //               padding: const EdgeInsets.symmetric(
            //                   vertical: 0, horizontal: 26),
            //               shape: RoundedRectangleBorder(
            //                 borderRadius: BorderRadius.circular(8),
            //               ),
            //               side: BorderSide(color: Palette.primaryBlack, width: 1),
            //             ),
            //             child: Text(
            //               'View',
            //               style: textTheme.bodyMedium?.copyWith(
            //                 fontWeight: FontWeight.w500,
            //               ),
            //             ),
            //           ),
            //         ),
            //         const Gap(40),
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildMoreOptionsButton(Deliveries delivery) {
    return Align(
      alignment: Alignment.centerRight,
      child: PopupMenuButton<String>(
        useRootNavigator: true,
        icon: const Icon(Icons.more_vert, color: Colors.black),
        onSelected: (value) => _handleMenuSelection(value, delivery),
        itemBuilder: (context) => _buildMenuItems(delivery),
        color: Colors.white,
        tooltip: '',
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: EdgeInsets.zero,
        menuPadding: EdgeInsets.zero,
        enableFeedback: false,
      ),
    );
  }

  void _handleMenuSelection(String value, Deliveries delivery) {
    if (value == 'copy') {
      Clipboard.setData(ClipboardData(text: delivery.id.toString()));
      Toast.show("Delivery ID copied to clipboard", context,
          duration: 2, title: '');
    }
  }

  List<PopupMenuItem<String>> _buildMenuItems(Deliveries delivery) {
    final items = <PopupMenuItem<String>>[
      const PopupMenuItem(
        value: 'copy',
        child: ListTile(
          leading: Icon(Icons.copy_outlined),
          title: Text(
            'Copy Delivery ID',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
    ];

    return items;
  }

  Widget _buildContentCell(
    dynamic content,
    TextTheme textTheme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: content is String
          ? Text(
              content,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            )
          : content,
    );
  }
}

class DeliveryStatusBadge extends StatelessWidget {
  const DeliveryStatusBadge(this.deliveryStatus, {super.key});

  final String? deliveryStatus;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    String status;
    Color color;

    switch (deliveryStatus?.toLowerCase()) {
      case 'completed':
        status = 'Completed';
        color = Colors.green;
        break;
      case 'planned':
        status = 'Planned';
        color = Colors.orange;
        break;
      case 'started':
        status = 'Started';
        color = Colors.blue;
        break;
      case 'ended':
        status = 'Ended';
        color = Colors.brown;
        break;
      case 'cancelled':
        status = 'Cancelled';
        color = Colors.red;
      default:
        status = 'Unknown';
        color = Colors.grey;
        break;
    }

    return Row(
      children: [
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: color.withOpacity(0.12),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            capitalize(status),
            style: textTheme.bodySmall
                ?.copyWith(color: color, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }
}
