import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class EmptyDeliveriesWidget extends ConsumerWidget {
  const EmptyDeliveriesWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset('$kSvgDir/order/box.svg'),
          Gap(10.h),
          Text(
            'No deliveries available',
            style: textTheme.headlineSmall,
          ),
        ],
      ),
    );
  }
}
