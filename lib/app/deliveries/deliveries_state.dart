import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/deliveries.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_procurement/app/deliveries/data/models/deliveries.dart';
import 'package:td_procurement/app/deliveries/domain/params/deliveries_params.dart';

@immutable
class DeliveriesState extends Equatable {
  final List<String> deliveriesStatusOptions;
  final AsyncValue<DeliveriesResponse?> deliveries;
  final FetchDeliveriesParam fetchDeliveriesParam;
  final AsyncValue<List<Order>?> deliveryOrders;
  final AsyncValue<Deliveries?> deliveryDetails;

  const DeliveriesState(
      {this.deliveriesStatusOptions = const [
        'All',
        'Planned',
        'Started',
        'Ended'
      ],
      required this.deliveries,
      required this.fetchDeliveriesParam,
      required this.deliveryOrders,
      required this.deliveryDetails});

  factory DeliveriesState.initial() {
    return DeliveriesState(
        deliveries: const AsyncData(null),
        fetchDeliveriesParam: FetchDeliveriesParam.defaultValue(),
        deliveryOrders: const AsyncData(null),
        deliveryDetails: const AsyncData(null));
  }

  @override
  List<Object?> get props => [
        deliveriesStatusOptions,
        deliveries,
        fetchDeliveriesParam,
        deliveryOrders,
      ];

  DeliveriesState copyWith({
    List<String>? deliveriesStatusOptions,
    AsyncValue<DeliveriesResponse?>? deliveries,
    FetchDeliveriesParam? fetchDeliveriesParam,
    AsyncValue<List<Order>>? deliveryOrders,
    AsyncValue<Deliveries>? deliveryDetails,
  }) {
    return DeliveriesState(
        deliveriesStatusOptions:
            deliveriesStatusOptions ?? this.deliveriesStatusOptions,
        deliveries: deliveries ?? this.deliveries,
        fetchDeliveriesParam: fetchDeliveriesParam ?? this.fetchDeliveriesParam,
        deliveryOrders: deliveryOrders ?? this.deliveryOrders,
        deliveryDetails: deliveryDetails ?? this.deliveryDetails);
  }
}
