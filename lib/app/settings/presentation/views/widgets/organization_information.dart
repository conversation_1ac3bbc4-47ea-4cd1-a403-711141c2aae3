import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/settings/presentation/views/widgets/disabled_textfield.dart';
import 'package:td_procurement/src/extensions/strings.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class OrganizationInformation extends ConsumerWidget {
  final PageController controller;
  const OrganizationInformation(this.controller, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final outlet = ref.read(userControllerProvider)?.currentRetailOutlet;
    return SizedBox(
      width: double.infinity,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.kE7E7E7),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  InkWell(
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    child: Text(
                      'Settings',
                      style: textTheme.bodyMedium
                          ?.copyWith(color: Palette.k6B797C),
                    ),
                    onTap: () => controller.animateToPage(1,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.linear),
                  ),
                  const Gap(12),
                  SvgPicture.asset(kChevronRightSvg),
                  const Gap(12),
                  Text(
                    'Organization profile',
                    style: textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                  )
                ],
              ),
              const Gap(18),
              Text('Organization Information', style: textTheme.headlineSmall),
              const Gap(20),
              DisabledTextField(
                  label: 'Organization name',
                  value: outlet?.outletBusinessName),
              const Gap(20),
              DisabledTextField(
                  label: 'Business location',
                  value: outlet?.company?.addressBook?.first.address1),
              const Gap(20),
              DisabledTextField(
                  label: 'Phone number', value: outlet?.phoneNumber),
              const Gap(20),
              DisabledTextField(label: 'Email address', value: outlet?.email),
              const Gap(20),
              Divider(
                color: Palette.stroke,
              ),
              const Gap(20),
              Text('Business status', style: textTheme.headlineSmall),
              const Gap(16),
              const DisabledTextField(
                  label: 'Business status', value: 'Registered'),
              const Gap(20),
              DisabledTextField(
                  label: 'Type of business',
                  value: outlet?.company?.kyb?.businessType?.capitalize),
              const Gap(20),
              DisabledTextField(
                  label: 'Business registration number',
                  value: outlet?.company?.kyb?.rcNumber),
              const Gap(20),
              Divider(
                color: Palette.stroke,
              ),
              const Gap(20),
              if (outlet?.country != 'GB') ...[
                Text(
                  'Banking Information',
                  style: textTheme.headlineSmall,
                ),
                const Gap(16),
                DisabledTextField(
                    label: 'Account name', value: outlet?.outletBusinessName),
                const Gap(20),
                DisabledTextField(
                    label: 'Bank Name',
                    value: outlet?.walletAccount?.colBankName),
                const Gap(20),
                DisabledTextField(
                    label: 'Account number',
                    value: outlet?.walletAccount?.accountNumber),
                const Gap(20),
                DisabledTextField(
                    label: 'Account signatory BVN',
                    value: outlet?.company?.kyb?.bvn),
                const Gap(8),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
