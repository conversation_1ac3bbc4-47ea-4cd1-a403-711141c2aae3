import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';
import 'package:url_launcher/url_launcher.dart';

class AccountSettings extends StatefulWidget {
  final PageController controller;
  const AccountSettings(this.controller, {super.key});

  @override
  State<StatefulWidget> createState() {
    return _AccountSettings();
  }
}

class _AccountSettings extends State<AccountSettings> {
  final _controller = MenuController();

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return SizedBox(
      width: double.infinity,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.kE7E7E7),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Column(
            children: [
              ListTile(
                leading: SvgPicture.asset(
                  kUserCircleSvg,
                  colorFilter:
                      ColorFilter.mode(Palette.primaryBlack, BlendMode.srcIn),
                ),
                title: const Text('Your profile'),
                titleTextStyle:
                    textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                subtitle: const Text('View your personal profile information'),
                subtitleTextStyle:
                    textTheme.bodySmall?.copyWith(color: Palette.k6B797C),
                trailing: SvgPicture.asset(kChevronRightSvg),
                onTap: () => widget.controller.animateToPage(0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.linear),
              ),
              const Gap(2),
              ListTile(
                leading: SvgPicture.asset(kBuildingsSvg),
                title: const Text('Organization profile'),
                titleTextStyle:
                    textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                subtitle:
                    const Text('View your organization profile information'),
                subtitleTextStyle:
                    textTheme.bodySmall?.copyWith(color: Palette.k6B797C),
                trailing: SvgPicture.asset(kChevronRightSvg),
                onTap: () => widget.controller.animateToPage(2,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.linear),
              ),
              const Gap(2),
              MenuAnchor(
                controller: _controller,
                alignmentOffset: const Offset(300, -20),
                style: MenuStyle(
                  backgroundColor: const WidgetStatePropertyAll(Colors.white),
                  side: WidgetStatePropertyAll(
                    BorderSide(color: Palette.kE7E7E7),
                  ),
                  shape: WidgetStatePropertyAll(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                builder: (BuildContext context, MenuController controller,
                    Widget? child) {
                  return child!;
                },
                menuChildren: [
                  MenuItemButton(
                    requestFocusOnHover: false,
                    style: const ButtonStyle(
                      minimumSize: WidgetStatePropertyAll(
                        Size(206, 58),
                      ),
                    ),
                    onPressed: () => _launchUrl(kTermsOfUse),
                    leadingIcon: Icon(
                      Icons.info_outline,
                      color: Palette.primaryBlack.withOpacity(0.9),
                    ),
                    child: Text('Terms of use', style: textTheme.bodySmall),
                  ),
                  MenuItemButton(
                    requestFocusOnHover: false,
                    style: const ButtonStyle(
                      minimumSize: WidgetStatePropertyAll(
                        Size(206, 58),
                      ),
                    ),
                    onPressed: () => _launchUrl(kPrivacyPolicy),
                    leadingIcon: Icon(
                      Icons.info_outline,
                      color: Palette.primaryBlack.withOpacity(0.9),
                    ),
                    child: Text('Privacy policy', style: textTheme.bodySmall),
                  ),
                ],
                child: ListTile(
                  onTap: () {
                    if (_controller.isOpen) {
                      _controller.close();
                    } else {
                      _controller.open();
                    }
                  },
                  leading: SvgPicture.asset(kScrollSvg),
                  title: const Text('Legal'),
                  titleTextStyle: textTheme.bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w500),
                  subtitle: const Text('View terms of use & privacy policy'),
                  subtitleTextStyle:
                      textTheme.bodySmall?.copyWith(color: Palette.k6B797C),
                  trailing: SvgPicture.asset(kChevronRightSvg),
                ),
              ),
              const Gap(2),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    if (!await launchUrl(Uri.parse(url))) {
      if (mounted) {
        Toast.error('Could not launch $url', context);
      }
    }
  }
}
