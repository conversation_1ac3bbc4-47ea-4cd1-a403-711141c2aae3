import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/settings/presentation/views/widgets/account_settings.dart';
import 'package:td_procurement/app/settings/presentation/views/widgets/organization_information.dart';
import 'package:td_procurement/app/settings/presentation/views/widgets/profile_information.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class Settings extends ConsumerStatefulWidget {
  const Settings({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _Settings();
  }
}

class _Settings extends ConsumerState<Settings> {
  final _pageController = PageController(initialPage: 1);

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      children: [
        Container(
          color: Palette.kFCFCFC,
          height: 70,
          padding: const EdgeInsets.only(left: 40),
          child: Row(
            children: [
              IconButton(
                onPressed: context.pop,
                icon: const Icon(Icons.close_outlined),
              ),
              const Gap(20),
              Text(
                'Your account',
                style: textTheme.bodyLarge?.copyWith(
                  color: Palette.k6B797C,
                ),
              ),
            ],
          ),
        ),
        const Gap(20),
        Expanded(
            child: Padding(
          padding: const EdgeInsets.all(40),
          child: PageView(
            physics: const NeverScrollableScrollPhysics(),
            controller: _pageController,
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(0),
                child: ProfileInformation(_pageController),
              ),
              SingleChildScrollView(
                padding: const EdgeInsets.all(0),
                child: AccountSettings(_pageController),
              ),
              SingleChildScrollView(
                padding: const EdgeInsets.all(0),
                child: OrganizationInformation(_pageController),
              )
            ],
          ),
        ))
      ],
    );
  }
}
