import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/domain/params/statement_params.dart';
import 'package:td_procurement/app/account/domain/use_cases/account_use_cases.dart';

class AccountTableController extends AutoDisposeFamilyAsyncNotifier<
    AccountStatement?, StatementParams?> {
  @override
  FutureOr<AccountStatement?> build(arg) async {
    return arg != null
        ? (await ref.read(
            accountUseCaseProvider(arg),
          ))
            .extract()
        : null;
  }
}

final accountTableControllerProvider = AutoDisposeAsyncNotifierProviderFamily<
    AccountTableController, AccountStatement?, StatementParams?>(() {
  return AccountTableController();
});

final statementArgProvider = StateProvider<StatementParams?>((_) => null);

final statementControllerProvider = AutoDisposeFutureProvider((ref) async {
  ref.invalidate(statementArgProvider);
  return (await ref.read(
    accountUseCaseProvider(
      StatementParams(),
    ),
  ))
      .extract();
});
