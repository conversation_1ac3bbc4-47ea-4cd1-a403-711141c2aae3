import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/domain/params/statement_params.dart';
import 'package:td_procurement/app/account/data/repo/account_repo.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final accountUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<AccountStatement>>, StatementParams>(
  (ref, arg) => UseCase<AccountStatement>().call(
    () => ref.read(accountRepoProvider).fetchAccountStatement(arg),
  ),
);

final sendAccountUseCaseProvider =
AutoDisposeProviderFamily<Future<ApiResponse<bool>>, StatementParams>(
      (ref, arg) => UseCase<bool>().call(
        () => ref.read(accountRepoProvider).sendAccountStatement(arg),
  ),
);