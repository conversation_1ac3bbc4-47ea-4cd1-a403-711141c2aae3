import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/data/models/account_transactions.dart';
import 'package:td_procurement/app/account/domain/logic/controllers/account_controller.dart';
import 'package:td_procurement/app/account/domain/params/statement_params.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/empty_widget.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class AccountTableWidget extends ConsumerStatefulWidget {
  final List<AccountTransactions> transactions;
  final Currency currency;
  final int? total;
  const AccountTableWidget(this.transactions, this.currency,
      {super.key, this.total});

  @override
  ConsumerState<AccountTableWidget> createState() => _AccountTableWidgetState();
}

class _AccountTableWidgetState extends ConsumerState<AccountTableWidget> {
  @override
  Widget build(BuildContext context) {
    final transactionsState = ref.watch(
      accountTableControllerProvider(
        ref.watch(statementArgProvider),
      ),
    );
    return transactionsState.when(
      data: (data) {
        if ((data?.transactions ?? widget.transactions).isEmpty) {
          return const EmptyWidget(
            icon: kReceiptSvg,
            title: 'No statements',
            subTitle: 'There are no statements matching your filter criteria',
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: _TableContent(
                  transactions: data?.transactions ?? widget.transactions,
                  currency: widget.currency,
                ),
              ),
            ),
            const Gap(20),
            Pagination(total: widget.total),
            const Gap(20),
          ],
        );
      },
      error: (error, __) => FailureWidget(
        e: error,
        retry: () => ref.invalidate(accountTableControllerProvider),
      ),
      loading: () => const TableLoadingView(),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }
}

class TableLoadingView extends StatelessWidget {
  const TableLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    final defaultLoadTransaction = AccountTransactions("accountId", '1000',
        "reference", 1000, DateTime.now(), AccountTransactionType.debits);
    return Skeletonizer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: _TableContent(
                transactions: List.filled(20, defaultLoadTransaction),
              ),
            ),
          ),
          const Gap(20),
          const Pagination(),
          const Gap(20),
        ],
      ),
    );
  }
}

class _TableContent extends ConsumerWidget {
  final List<AccountTransactions> transactions;
  final Currency? currency;
  const _TableContent({required this.transactions, this.currency});

  @override
  Widget build(BuildContext context, ref) {
    final textTheme = Theme.of(context).textTheme;
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Palette.kE7E7E7,
        ),
      ),
      child: DataTable(
        showCheckboxColumn: false,
        headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
        columns: [
          DataColumn(
            label: _buildHeaderCell('Date', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Reference Number', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Debit', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Credit', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Balance', textTheme),
          ),
          const DataColumn(
            label: SizedBox.shrink(),
          ), // Empty space for floating icon
        ],
        rows: transactions
            .map(
              (element) => DataRow(
                onSelectChanged: (_) {},
                /* decoration: BoxDecoration(
                border: Border.all(
                    color: Palette.kE7E7E7.withOpacity(0.5), width: 0.5),
              ),*/
                cells: [
                  DataCell(
                    _buildContentText(element.createdAt.toDate(), textTheme),
                  ),
                  DataCell(
                    _buildContentText(
                        element.invoiceNumber.toString(), textTheme),
                  ),
                  DataCell(
                    element.transactionType == AccountTransactionType.debits
                        ? _buildContentAmount(element.amount, textTheme,
                            Palette.kE61010, currency)
                        : const SizedBox.shrink(),
                  ),
                  DataCell(
                    element.transactionType == AccountTransactionType.credits
                        ? _buildContentAmount(element.amount, textTheme,
                            Palette.k0CA653, currency)
                        : const SizedBox.shrink(),
                  ),
                  DataCell(
                    _buildContentAmount(
                        element.amount, textTheme, null, currency),
                  ),
                  const DataCell(
                    SizedBox.shrink(),
                  )
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme,
      [Color? color, Currency? currency]) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: CurrencyWidget(
        amount,
        currency?.iso ?? kDefaultCurrency,
        amountStyle: textTheme.bodyMedium?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: Palette.blackSecondary,
        ),
      ),
    );
  }
}

class Pagination extends ConsumerWidget {
  final int? total;
  const Pagination({super.key, this.total});

  @override
  Widget build(BuildContext context, ref) {
    final params = ref.watch(statementArgProvider) ?? StatementParams();
    final int batches = ((ref
                    .read(accountTableControllerProvider(params))
                    .valueOrNull
                    ?.totalCount ??
                total ??
                0) /
            params.limit)
        .ceil();
    return Align(
      alignment: Alignment.topRight,
      child: SizedBox(
        width: 500,
        child: NumberPagination(
          onPageChanged: (int pageNumber) {
            ref.read(statementArgProvider.notifier).state =
                params.paginate(pageNumber);
          },
          visiblePagesCount: batches > 5 || batches == 0 ? 5 : batches,
          buttonElevation: 0.3,
          totalPages: batches,
          currentPage: params.batch,
          buttonRadius: 8,
          selectedButtonColor: Palette.primaryBlack,
          selectedNumberColor: Colors.white,
          unSelectedButtonColor: Colors.white,
          unSelectedNumberColor: Palette.blackSecondary,
          fontSize: 14,
          firstPageIcon: SvgPicture.asset(
            kDoubleChevronLeftSvg,
            width: 30,
            height: 30,
          ),
          previousPageIcon: SvgPicture.asset(
            kChevronLeftSvg,
            colorFilter:
                ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
            width: 12,
            height: 12,
          ),
          lastPageIcon: SvgPicture.asset(
            kDoubleChevronRightSvg,
            width: 30,
            height: 30,
          ),
          nextPageIcon: SvgPicture.asset(
            kChevronRightSvg,
            colorFilter:
                ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
            width: 12,
            height: 12,
          ),
        ),
      ),
    );
  }
}
