import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_procurement/app/account/domain/logic/controllers/account_controller.dart';
import 'package:td_procurement/app/account/domain/params/statement_params.dart';
import 'package:td_procurement/app/shipments/widgets/date_picker.dart';
import 'package:td_procurement/src/components/date_picker/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AccountFilter extends ConsumerStatefulWidget {
  const AccountFilter({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _AccountFilter();
  }
}

class _AccountFilter extends ConsumerState<AccountFilter> {
  final _layerDateLink = LayerLink();
  OverlayEntry? _overlayEntry;
  final ValueNotifier<List<DateTime>> _selectedDates = ValueNotifier([]);

  @override
  void dispose() {
    _selectedDates.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.kE7E7E7),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
              color: Palette.k0000000A,
              offset: Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0)
        ],
      ),
      height: 34,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          DropdownMenu<StatementSorting>(
            trailingIcon: SvgPicture.asset(
              kChevronDownSvg,
              fit: BoxFit.cover,
            ),
            hintText: 'Sort by',
            width: 145,
            enableSearch: false,
            requestFocusOnTap: false,
            selectedTrailingIcon: SvgPicture.asset(
              kChevronDownSvg,
              fit: BoxFit.cover,
            ),
            inputDecorationTheme: InputDecorationTheme(
              hintStyle: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  color: Palette.primaryBlack),
              enabledBorder: OutlineInputBorder(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
                borderSide: BorderSide(color: Palette.kE7E7E7, width: 0.1),
              ),
              contentPadding: const EdgeInsets.only(left: 10),
              constraints: BoxConstraints.tight(
                const Size.fromHeight(34),
              ),
            ),
            onSelected: sortTable,
            dropdownMenuEntries: StatementSorting.values
                .map<DropdownMenuEntry<StatementSorting>>(
                  (option) => DropdownMenuEntry<StatementSorting>(
                    value: option,
                    label: option.label,
                    style: MenuItemButton.styleFrom(
                        foregroundColor: Palette.primaryBlack),
                  ),
                )
                .toList(),
          ),
          VerticalDivider(
            color: Palette.kE7E7E7,
            thickness: 1.5,
            width: 20,
          ),
          ValueListenableBuilder<List<DateTime>>(
            valueListenable: _selectedDates,
            builder: (context, selectedDates, _) {
              return DatePickerWidget(
                initialDate: selectedDates.firstOrNull,
                selectedStartDate: selectedDates.firstOrNull,
                selectedEndDate: selectedDates.lastOrNull,
                offset: const Offset(455, 40),
                decoration: const BoxDecoration(),
                getValueLabel: () {
                  return getFormattedDateRange(selectedDates.firstOrNull,
                      selectedDates.lastOrNull, 'Select Date');
                },
                onDatesSelected: ({startDate, endDate}) async {
                  final selectedStartDate = startDate ??= endDate;
                  final selectedEndDate = endDate ??= startDate;
                  final params = ref.read(statementArgProvider);
                  ref.read(statementArgProvider.notifier).state =
                      StatementParams(
                          startDate: selectedStartDate,
                          endDate: selectedEndDate,
                          paymentType: params?.paymentType);
                  _selectedDates.value = [selectedStartDate!, selectedEndDate!];
                },
                onCancel: () async {
                  final params = ref.read(statementArgProvider);
                  ref.read(statementArgProvider.notifier).state =
                      StatementParams(
                          startDate: null,
                          endDate: null,
                          paymentType: params?.paymentType);
                  _selectedDates.value = [];
                },
              );
            },
          ),
          // InkWell(
          //   child: CompositedTransformTarget(
          //     link: _layerDateLink,
          //     child: Row(
          //       children: [
          //         ValueListenableBuilder<List<DateTime>>(
          //           valueListenable: _selectedDates,
          //           builder: (context, dates, _) => Text(
          //             dates.isEmpty
          //                 ? 'Select Date'
          //                 : '${dates.first.toDayMonth()} - ${dates.last.toDayMonth()}',
          //             style: textTheme.bodyMedium
          //                 ?.copyWith(fontWeight: FontWeight.w500),
          //           ),
          //         ),
          //         const Gap(10),
          //         SvgPicture.asset(kCalendarSvg),
          //         const Gap(8),
          //       ],
          //     ),
          //   ),
          //   onTap: () => _showDatePickerOverlay(context),
          // )
        ],
      ),
    );
  }

  void sortTable(StatementSorting? sort) {
    if (sort != null) {
      final params = ref.read(statementArgProvider);
      ref.read(statementArgProvider.notifier).state =
          params?.sort(sort) ?? StatementParams(sortBy: sort);
    }
  }

  void _showDatePickerOverlay(BuildContext context) {
    _overlayEntry = createOverlayEntry(
      context,
      Config(width: 585, height: 325, top: 0.15, right: 0.012), // w580, h383
      _layerDateLink,
      Padding(
        padding: const EdgeInsets.only(top: 4),
        child: MultiDatePickerOverlay(
          currentStart: _selectedDates.value.firstOrNull,
          currentEnd: _selectedDates.value.lastOrNull,
          onSelectRange: (startDate, endDate) {
            _closeOverlay();
            final params = ref.read(statementArgProvider);
            ref.read(statementArgProvider.notifier).state = StatementParams(
                startDate: startDate,
                endDate: endDate,
                paymentType: params?.paymentType);
            _selectedDates.value = (startDate != null && endDate != null)
                ? [startDate, endDate]
                : [];
          },
          onCancel: _closeOverlay,
        ),
      ),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  OverlayEntry createOverlayEntry(
    BuildContext context,
    Config size,
    LayerLink layerLink,
    Widget child,
  ) {
    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          // A GestureDetector to close the overlay when tapped outside
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                _closeOverlay();
              },
            ),
          ),
          // Positioned Overlay Entry
          Positioned(
            // Align using `right` for right-side alignment
            right: MediaQuery.of(context).size.width -
                (size.width + 35), // Adjusted for right alignment

            // Use `top` to place it just below the target
            top: MediaQuery.of(context).size.height *
                (size.top ??
                    0 + size.height), // Positioned just below the target

            width: size.width,
            height: size.height + 25,

            // Follower setup
            child: CompositedTransformFollower(
              link: layerLink,
              // Adjust `offset` to align with bottom-right of the target
              offset: const Offset(
                  -170, 30), // Align horizontally (right) and place below
              showWhenUnlinked: false,

              // The material card
              child: Material(
                elevation: 0.0,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Palette.stroke),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: child,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
