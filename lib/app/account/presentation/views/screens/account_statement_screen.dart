import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/account/data/models/account_transactions.dart';
import 'package:td_procurement/app/account/domain/params/statement_params.dart';
import 'package:td_procurement/app/account/domain/use_cases/account_use_cases.dart';
import 'package:td_procurement/app/account/domain/logic/controllers/account_controller.dart';
import 'package:td_procurement/app/account/presentation/views/widgets/account_card.dart';
import 'package:td_procurement/app/account/presentation/views/widgets/account_filter.dart';
import 'package:td_procurement/app/account/presentation/views/widgets/account_table.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/src/components/buttons/custom_elevated_button.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AccountStatementScreen extends ConsumerStatefulWidget {
  const AccountStatementScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _AccountStatementScreen();
  }
}

class _AccountStatementScreen extends ConsumerState<AccountStatementScreen> {
  late final user = ref.read(userControllerProvider);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final accountState = ref.watch(
      statementControllerProvider,
    );
    return accountState.when(
      data: (data) {
        final List<(num, StatementPaymentType)> cards = [
          (data.balance, StatementPaymentType.balance),
          (data.totalCredits, StatementPaymentType.credits),
          (data.totalDebits, StatementPaymentType.debits),
        ];
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 30, top: 20, bottom: 20, right: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Account Statement', style: textTheme.headlineMedium),
                  const Gap(20),
                  Row(
                    children: cards
                        .map(
                          (element) => AccountCard(
                            element.$1,
                            element.$2,
                            currency: data.currency,
                          ),
                        )
                        .toList(),
                  ),
                  const Gap(20),
                  Row(
                    children: [
                      const AccountFilter(),
                      const Spacer(),
                      CustomElevatedButton(
                        text: 'Send to Email',
                        icon: kMailSvg,
                        onPressed: () => sendEmail(data.pagination.page),
                        size: const Size(133, 38),
                      ),
                      const Gap(10),
                    ],
                  ),
                ],
              ),
            ),
            Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
            Expanded(
              child: AccountTableWidget(
                data.transactions,
                data.currency,
                total: data.totalCount.toInt(),
              ),
            ),
          ],
        );
      },
      error: (error, _) => FailureWidget(
        e: error,
        retry: () => ref.invalidate(statementControllerProvider),
      ),
      loading: () => const AccountLoadingView(),
    );
  }

  void sendEmail(int page) async {
    Toast.info(
        'Please hold on while we process and send this statement to ${user?.email ?? user?.currentRetailOutlet?.email ?? 'you'}',
        context,
        title: "Processing...",
        duration: 3);
    final response = await ref.read(
      sendAccountUseCaseProvider(
        ref.read(statementArgProvider)?.email() ?? StatementParams().email(),
      ),
    );
    response.when(success: (_) {
      if (mounted) {
        Toast.success(
            "Your account statement has been sent to ${user?.email ?? user?.currentRetailOutlet?.email ?? 'you'}",
            context,
            title: 'Successful!');
      }
    }, failure: (error, _) {
      if (mounted) Toast.apiError(error, context);
    });
  }
}

class AccountLoadingView extends StatelessWidget {
  const AccountLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    final defaultLoadTransaction = AccountTransactions("accountId", '1000',
        "reference", 1000, DateTime.now(), AccountTransactionType.debits);
    return Skeletonizer(
      child: Column(
        children: [
          Padding(
            padding:
                const EdgeInsets.only(left: 30, top: 20, bottom: 20, right: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Account Statement'),
                const Gap(20),
                Row(
                  children: List.filled(
                    3,
                    const AccountCard(0, StatementPaymentType.credits),
                  ),
                ),
                const Gap(20),
                Row(
                  children: [
                    const AccountFilter(),
                    const Spacer(),
                    CustomElevatedButton(
                      text: 'Send to Email',
                      icon: kMailSvg,
                      onPressed: () {},
                      size: const Size(133, 38),
                    ),
                    const Gap(10),
                  ],
                ),
              ],
            ),
          ),
          Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
          Expanded(
            child: AccountTableWidget(
              List.filled(20, defaultLoadTransaction),
              Currency("", ""),
            ),
          ),
        ],
      ),
    );
  }
}
