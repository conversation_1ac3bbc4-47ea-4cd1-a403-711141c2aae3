import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/core/models/optional.dart';

import 'line_item_param.dart';

class CreateRetailInvoiceParam extends Equatable {
  final String id;
  final String issuerOutletId;
  final String recipientOutletId;
  final String? note;
  final List<LineItemParam> lineItems;
  final List<TaxRate>? taxes;
  final List<AdditionalCharge>? charges;
  final Discount? discount;
  final bool isFinanced;
  final WalletBank? bankAccount;
  final String currencyCode;
  final Currency? currency;

  const CreateRetailInvoiceParam({
    required this.id,
    required this.issuerOutletId,
    required this.recipientOutletId,
    this.note,
    required this.lineItems,
    this.taxes,
    this.charges,
    this.discount,
    required this.isFinanced,
    this.bankAccount,
    required this.currencyCode,
    this.currency,
  });

  bool get isComplete =>
      lineItems.isNotEmpty &&
      issuerOutletId.isNotEmpty &&
      recipientOutletId.isNotEmpty &&
      bankAccount != null;

  CreateRetailInvoiceParam copyWith({
    String? issuerOutletId,
    String? recipientOutletId,
    Optional<String?>? note,
    List<LineItemParam>? lineItems,
    List<TaxRate>? taxes,
    List<AdditionalCharge>? charges,
    Optional<Discount?>? discount,
    bool? isFinanced,
    Optional<WalletBank?>? bankAccount,
    String? currencyCode,
    Currency? currency,
  }) {
    return CreateRetailInvoiceParam(
      id: id,
      issuerOutletId: issuerOutletId ?? this.issuerOutletId,
      recipientOutletId: recipientOutletId ?? this.recipientOutletId,
      note: note != null ? note.value : this.note,
      lineItems: lineItems ?? this.lineItems,
      taxes: taxes ?? this.taxes,
      charges: charges ?? this.charges,
      discount: discount != null ? discount.value : this.discount,
      isFinanced: isFinanced ?? this.isFinanced,
      bankAccount: bankAccount != null ? bankAccount.value : this.bankAccount,
      currencyCode: currencyCode ?? this.currencyCode,
      currency: currency ?? this.currency,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'issuerOutletId': issuerOutletId,
      'recipientOutletId': recipientOutletId,
      'note': note,
      'lineItems': lineItems.map((x) => x.toMap()).toList(),
      'taxes': taxes?.map((x) => x.toMap()).toList(),
      'charges': charges?.map((x) => x.toMap()).toList(),
      'discount': discount?.toMap(),
      'isFinanced': isFinanced,
      'bankAccount': bankAccount?.toMap(),
      'currency': currency?.toMap(),
    };
  }

  Map<String, dynamic> toData() {
    // final otherTaxes = (taxes ?? [])
    //     .where((x) => x.name.toLowerCase() != 'vat')
    //     .map((x) => x.toData())
    //     .toList();
    final otherCharges = (charges ?? [])
        .where((x) =>
            x.name.toLowerCase() != 'shipping fee' &&
            x.name.toLowerCase() != 'processing fee')
        .map((x) => x.toFullData())
        .toList();

    return <String, dynamic>{
      'issuerOutletId': issuerOutletId,
      'recipientOutletId': recipientOutletId,
      'note': note ?? '',
      'items': lineItems.map((x) => x.toData()).toList(),
      'shippingCost': (charges ?? [])
              .firstWhereOrNull((x) => x.name.toLowerCase() == 'shipping fee')
              ?.amount ??
          0,
      'processingCost': (charges ?? [])
              .firstWhereOrNull((x) => x.name.toLowerCase() == 'processing fee')
              ?.amount ??
          0,
      'taxRate': (taxes ?? [])
              .firstWhereOrNull((x) => x.name.toLowerCase() == 'vat')
              ?.rate ??
          0,
      // if (otherTaxes.isNotEmpty) 'otherTaxes': otherTaxes,
      if (otherCharges.isNotEmpty) 'otherCharges': otherCharges,
      if (discount != null) 'discount': discount!.toMap(),
      'isFinanced': isFinanced,
      'bankAccount': bankAccount?.toData(),
      'currency': currency?.toMap(),
    };
  }

  // factory CreateRetailInvoiceParam.fromMap(Map<String, dynamic> map) {
  //   return CreateRetailInvoiceParam(
  //     id: map['_id'] ?? map['id'] ?? '',
  //     issuerOutletId: map['issuerOutletId'] as String,
  //     recipientOutletId: map['recipientOutletId'] as String,
  //     note: map['note'] as String?,
  //     lineItems: List<LineItem>.from(
  //       (map['lineItems'] as List).map<LineItem>(
  //         (x) => LineItem.fromMap(x as Map<String, dynamic>),
  //       ),
  //     ),
  //     taxes: map['taxes'] != null
  //         ? List<TaxRate>.from(
  //             (map['taxes'] as List).map<TaxRate?>(
  //               (x) => TaxRate.fromMap(x as Map<String, dynamic>),
  //             ),
  //           )
  //         : null,
  //     charges: map['charges'] != null
  //         ? List<AdditionalCharge>.from(
  //             (map['charges'] as List).map<AdditionalCharge?>(
  //               (x) => AdditionalCharge.fromMap(x as Map<String, dynamic>),
  //             ),
  //           )
  //         : null,
  //     discount: map['discount'] != null
  //         ? Discount.fromMap(map['discount'] as Map<String, dynamic>)
  //         : null,
  //     isFinanced: map['isFinanced'] as bool,
  //     bankAccount: map['bankAccount'] != null
  //         ? WalletBank.fromMap(map['bankAccount'])
  //         : null,
  //     currencyCode: map['currencyCode'] as String,
  //     currency: map['currency'] != null
  //         ? Currency.fromMap(map['currency'] as Map<String, dynamic>)
  //         : null,
  //   );
  // }

  @override
  List<Object?> get props {
    return [
      id,
      issuerOutletId,
      recipientOutletId,
      note,
      lineItems,
      taxes,
      charges,
      discount,
      isFinanced,
      bankAccount,
      currencyCode,
      currency,
    ];
  }
}
