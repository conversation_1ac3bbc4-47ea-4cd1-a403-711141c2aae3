import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/data/repo/advance_invoice_repo.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/create_retail_invoice_param.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final fetchAdvanceInvoiceSettingsUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<AdvanceInvoiceSettings>>, NoParams>(
  (ref, _) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<AdvanceInvoiceSettings>();
    return useCase.call(() => advanceInvoiceRepo.fetchAdvanceInvoiceSettings());
  },
);

final createAdditionalChargeUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<AdditionalCharge>>, AdditionalCharge>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<AdditionalCharge>();
    return useCase
        .call(() => advanceInvoiceRepo.createAdditionalCharge(params));
  },
);

final createTaxRateUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<TaxRate>>, TaxRate>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<TaxRate>();
    return useCase.call(() => advanceInvoiceRepo.createTaxRate(params));
  },
);

final createRetailInvoiceUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<RetailInvoice>>, CreateRetailInvoiceParam>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<RetailInvoice>();
    return useCase.call(() => advanceInvoiceRepo.createRetailInvoice(params));
  },
);

final fetchAdvanceInvoiceUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<RetailInvoice>>, String>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<RetailInvoice>();
    return useCase.call(() => advanceInvoiceRepo.fetchAdvanceInvoice(params));
  },
);

final deleteAdvanceInvoiceUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<void>>, String>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<void>();
    return useCase.call(() => advanceInvoiceRepo.deleteAdvanceInvoice(params));
  },
);

final fetchAdvanceInvoicesUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<FetchAdvanceInvoicesResponse>>,
    FetchAdvanceInvoiceParams>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<FetchAdvanceInvoicesResponse>();
    return useCase.call(() => advanceInvoiceRepo.fetchAdvanceInvoices(params));
  },
);

final uploadAdvanceInvoiceDocUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<void>>, (String, String)>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<void>();
    return useCase.call(
        () => advanceInvoiceRepo.uploadAdvanceInvoiceDoc(params.$1, params.$2));
  },
);

final uploadLogoUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<void>>, String>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<void>();
    return useCase.call(() => advanceInvoiceRepo.uploadLogo(params));
  },
);

final getLoanContractUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<LoanContract>>, NoParams>(
  (ref, _) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<LoanContract>();
    return useCase.call(() => advanceInvoiceRepo.getLoanContract());
  },
);

final searchMerchantsUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<List<RetailOutlet>>>, String>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<List<RetailOutlet>>();
    return useCase.call(() => advanceInvoiceRepo.searchMerchant(params));
  },
);

// get bank accounts
final getBankAccountsUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<List<WalletBank>>>, NoParams>(
  (ref, _) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<List<WalletBank>>();
    return useCase.call(() => advanceInvoiceRepo.getBankAccounts());
  },
);

// get settlement banks
final getSettlementBanksUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<List<SettlementBank>>>, NoParams>(
  (ref, _) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<List<SettlementBank>>();
    return useCase.call(() => advanceInvoiceRepo.getSettlementBanks());
  },
);

// validate bank account
final validateBankAccountUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<(String bankName, String? bvn)>>, (String, String)>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<(String bankName, String? bvn)>();
    return useCase.call(
        () => advanceInvoiceRepo.validateBankAccount(params.$1, params.$2));
  },
);

// link bank account
final linkBankAccountUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<bool>>, LinkBankParams>(
  (ref, params) {
    final advanceInvoiceRepo = ref.read(advanceInvoiceRepoProvider);
    final useCase = UseCase<bool>();
    return useCase.call(() => advanceInvoiceRepo.linkBankAccount(params));
  },
);
