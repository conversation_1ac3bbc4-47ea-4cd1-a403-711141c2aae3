// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class LoanContract extends Equatable {
  final String id;
  final num creditLimit;
  final String retailOutletId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final num monthlyInterest;
  final String status;
  final num tenorInWeeks;
  final num creditBalance;
  final String extChannel;
  final String userId;
  final num interest;
  final num extContractId;
  final num weeklyInterest;
  final String paymentCycle;
  final bool creditDisabled;
  final num annualInterest;
  final String loanProductId;
  final String loanType;
  final num tenor;
  final num tenorInMonths;
  final num tenorInYears;
  final num lastCreditLimit;
  final num installments;
  // final List<CreditLimitHistory> creditLimitHistory;
  // final List<History> history;

  bool get isActive => status.toLowerCase() == 'active';

  factory LoanContract.defaultValue() {
    return LoanContract(
      id: 'id',
      creditLimit: 0,
      retailOutletId: 'retailOutletId',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      monthlyInterest: 0,
      status: 'status',
      tenorInWeeks: 0,
      creditBalance: 0,
      extChannel: 'extChannel',
      userId: 'userId',
      interest: 0,
      extContractId: 0,
      weeklyInterest: 0,
      paymentCycle: 'paymentCycle',
      creditDisabled: false,
      annualInterest: 0,
      loanProductId: 'loanProductId',
      loanType: 'loanType',
      tenor: 0,
      tenorInMonths: 0,
      tenorInYears: 0,
      lastCreditLimit: 0,
      installments: 0,
      // creditLimitHistory: const [],
      // history: const [],
    );
  }

  const LoanContract({
    required this.id,
    required this.creditLimit,
    required this.retailOutletId,
    required this.createdAt,
    required this.updatedAt,
    required this.monthlyInterest,
    required this.status,
    required this.tenorInWeeks,
    required this.creditBalance,
    required this.extChannel,
    required this.userId,
    required this.interest,
    required this.extContractId,
    required this.weeklyInterest,
    required this.paymentCycle,
    required this.creditDisabled,
    required this.annualInterest,
    required this.loanProductId,
    required this.loanType,
    required this.tenor,
    required this.tenorInMonths,
    required this.tenorInYears,
    required this.lastCreditLimit,
    required this.installments,
    // required this.creditLimitHistory,
    // required this.history,
  });

  LoanContract copyWith({
    num? creditLimit,
    String? retailOutletId,
    DateTime? createdAt,
    DateTime? updatedAt,
    num? monthlyInterest,
    String? status,
    num? tenorInWeeks,
    num? creditBalance,
    String? extChannel,
    String? userId,
    num? interest,
    num? extContractId,
    num? weeklyInterest,
    String? paymentCycle,
    bool? creditDisabled,
    num? annualInterest,
    String? loanProductId,
    String? loanType,
    num? tenor,
    num? tenorInMonths,
    num? tenorInYears,
    num? lastCreditLimit,
    num? installments,
    // List<CreditLimitHistory>? creditLimitHistory,
    // List<History>? history,
  }) {
    return LoanContract(
      id: id,
      creditLimit: creditLimit ?? this.creditLimit,
      retailOutletId: retailOutletId ?? this.retailOutletId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      monthlyInterest: monthlyInterest ?? this.monthlyInterest,
      status: status ?? this.status,
      tenorInWeeks: tenorInWeeks ?? this.tenorInWeeks,
      creditBalance: creditBalance ?? this.creditBalance,
      extChannel: extChannel ?? this.extChannel,
      userId: userId ?? this.userId,
      interest: interest ?? this.interest,
      extContractId: extContractId ?? this.extContractId,
      weeklyInterest: weeklyInterest ?? this.weeklyInterest,
      paymentCycle: paymentCycle ?? this.paymentCycle,
      creditDisabled: creditDisabled ?? this.creditDisabled,
      annualInterest: annualInterest ?? this.annualInterest,
      loanProductId: loanProductId ?? this.loanProductId,
      loanType: loanType ?? this.loanType,
      tenor: tenor ?? this.tenor,
      tenorInMonths: tenorInMonths ?? this.tenorInMonths,
      tenorInYears: tenorInYears ?? this.tenorInYears,
      lastCreditLimit: lastCreditLimit ?? this.lastCreditLimit,
      installments: installments ?? this.installments,
      // creditLimitHistory: creditLimitHistory ?? this.creditLimitHistory,
      // history: history ?? this.history,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'creditLimit': creditLimit,
      'retailOutletId': retailOutletId,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'monthlyInterest': monthlyInterest,
      'status': status,
      'tenorInWeeks': tenorInWeeks,
      'creditBalance': creditBalance,
      'extChannel': extChannel,
      'userId': userId,
      'interest': interest,
      'extContractId': extContractId,
      'weeklyInterest': weeklyInterest,
      'paymentCycle': paymentCycle,
      'creditDisabled': creditDisabled,
      'annualInterest': annualInterest,
      'loanProductId': loanProductId,
      'loanType': loanType,
      'tenor': tenor,
      'tenorInMonths': tenorInMonths,
      'tenorInYears': tenorInYears,
      'lastCreditLimit': lastCreditLimit,
      'installments': installments,
      // 'creditLimitHistory': creditLimitHistory.map((x) => x.toMap()).toList(),
      // 'history': history.map((x) => x.toMap()).toList(),
    };
  }

  factory LoanContract.fromMap(Map<String, dynamic> map) {
    return LoanContract(
      id: map['_id'] ?? map['id'] as String,
      creditLimit: map['creditLimit'] as num,
      retailOutletId: map['retailOutletId'] as String,
      createdAt: parseDate(map['createdAt'])!,
      updatedAt: parseDate(map['updatedAt'])!,
      monthlyInterest: map['monthlyInterest'] as num,
      status: map['status'] as String,
      tenorInWeeks: map['tenorInWeeks'] as num,
      creditBalance: map['creditBalance'] as num,
      extChannel: map['extChannel'] as String,
      userId: map['userId'] as String,
      interest: map['interest'] as num,
      extContractId: map['extContractId'] as num,
      weeklyInterest: map['weeklyInterest'] as num,
      paymentCycle: map['paymentCycle'] as String,
      creditDisabled: map['creditDisabled'] as bool,
      annualInterest: map['annualInterest'] as num,
      loanProductId: map['loanProductId'] as String,
      loanType: map['loanType'] as String,
      tenor: map['tenor'] as num,
      tenorInMonths: map['tenorInMonths'] as num,
      tenorInYears: map['tenorInYears'] as num,
      lastCreditLimit: map['lastCreditLimit'] as num,
      installments: map['installments'] as num,
      // creditLimitHistory: List<CreditLimitHistory>.from(
      //   (map['creditLimitHistory'] as List<int>).map<CreditLimitHistory>(
      //     (x) => CreditLimitHistory.fromMap(x as Map<String, dynamic>),
      //   ),
      // ),
      // history: List<History>.from(
      //   (map['history'] as List<int>).map<History>(
      //     (x) => History.fromMap(x as Map<String, dynamic>),
      //   ),
      // ),
    );
  }

  String toJson() => json.encode(toMap());

  factory LoanContract.fromJson(String source) =>
      LoanContract.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      id,
      creditLimit,
      retailOutletId,
      createdAt,
      updatedAt,
      monthlyInterest,
      status,
      tenorInWeeks,
      creditBalance,
      extChannel,
      userId,
      interest,
      extContractId,
      weeklyInterest,
      paymentCycle,
      creditDisabled,
      annualInterest,
      loanProductId,
      loanType,
      tenor,
      tenorInMonths,
      tenorInYears,
      lastCreditLimit,
      installments,
      // creditLimitHistory,
      // history,
    ];
  }
}

//  "_id":"SHCe2G7c5mYyeDjEr",
//  "creditLimit":999999,
//  "retailOutletId":"9LfCpBt7kJQFxjKdb",
//  "createdAt":"2022-02-01T12:46:02.973Z",
//  "updatedAt":"2025-06-06T16:05:29.781Z",
//  "monthlyInterest":0.75,
//  "status":"active",
//  "tenorInWeeks":8.714285714285714,
//  "creditBalance":50000,
//  "extChannel":"CONSOLE",
//  "userId":"Ax9sjhxfwgNGG4BLn",
//  "interest":9,
//  "extContractId":21,
//  "weeklyInterest":0.16071428571428573,
//  "paymentCycle":"biweekly",
//  "creditDisabled":false,
//  "annualInterest":9,
//  "loanProductId":"RLeBbTtqtoysmgfPC",
//  "loanType":"TERM LOAN",
//  "tenor":2,
//  "tenorInMonths":2.0333333,
//  "tenorInYears":0.1694444,
//  "lastCreditLimit":300000,
//  "installments":1

class CreditLimitHistory extends Equatable {
  final num? oldValue;
  final num? newValue;
  final DateTime? updatedAt;
  final String? userId;
  const CreditLimitHistory({
    this.oldValue,
    this.newValue,
    this.updatedAt,
    this.userId,
  });

  CreditLimitHistory copyWith({
    num? oldValue,
    num? newValue,
    DateTime? updatedAt,
    String? userId,
  }) {
    return CreditLimitHistory(
      oldValue: oldValue ?? this.oldValue,
      newValue: newValue ?? this.newValue,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'oldValue': oldValue,
      'newValue': newValue,
      'updatedAt': updatedAt,
      'userId': userId,
    };
  }

  factory CreditLimitHistory.fromMap(Map<String, dynamic> map) {
    return CreditLimitHistory(
      oldValue: parseNum(map['oldValue']),
      newValue: parseNum(map['newValue']),
      updatedAt: parseDate(map['updatedAt']),
      userId: map['userId'] != null ? map['userId'] as String : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory CreditLimitHistory.fromJson(String source) =>
      CreditLimitHistory.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [oldValue, newValue, updatedAt, userId];
}

class History extends Equatable {
  final String? field;
  final num? oldValue;
  final num? newValue;
  final DateTime? updatedAt;
  final String? userId;

  const History({
    this.field,
    this.oldValue,
    this.newValue,
    this.updatedAt,
    this.userId,
  });

  History copyWith({
    String? field,
    num? oldValue,
    num? newValue,
    DateTime? updatedAt,
    String? userId,
  }) {
    return History(
      field: field ?? this.field,
      oldValue: oldValue ?? this.oldValue,
      newValue: newValue ?? this.newValue,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'field': field,
      'oldValue': oldValue,
      'newValue': newValue,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'userId': userId,
    };
  }

  factory History.fromMap(Map<String, dynamic> map) {
    return History(
      field: map['field'] != null ? map['field'] as String : null,
      oldValue: parseNum(['oldValue']),
      newValue: parseNum(['newValue']),
      updatedAt: parseDate(map['updatedAt']),
      userId: map['userId'] != null ? map['userId'] as String : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory History.fromJson(String source) =>
      History.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      field,
      oldValue,
      newValue,
      updatedAt,
      userId,
    ];
  }
}
