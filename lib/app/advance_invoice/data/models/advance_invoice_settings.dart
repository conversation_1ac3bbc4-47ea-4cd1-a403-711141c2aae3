import 'package:equatable/equatable.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';

class AdvanceInvoiceSettings extends Equatable {
  final String? logoUrl;
  final List<TaxRate> taxes;
  final List<AdditionalCharge> charges;

  const AdvanceInvoiceSettings({
    required this.logoUrl,
    required this.taxes,
    required this.charges,
  });

  bool get isComplete => taxes.isNotEmpty && charges.isNotEmpty;

  AdvanceInvoiceSettings copyWith({
    String? logoUrl,
    List<TaxRate>? taxes,
    List<AdditionalCharge>? additionalCharges,
  }) {
    return AdvanceInvoiceSettings(
      logoUrl: logoUrl ?? this.logoUrl,
      taxes: taxes ?? this.taxes,
      charges: additionalCharges ?? charges,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'logoUrl': logoUrl,
      'taxes': taxes.map((x) => x.toMap()).toList(),
      'additionalCharges': charges.map((x) => x.toMap()).toList(),
    };
  }

  factory AdvanceInvoiceSettings.fromMap(Map<String, dynamic> map) {
    return AdvanceInvoiceSettings(
      logoUrl: map['logoUrl'] as String,
      taxes: List<TaxRate>.from(
        ((map['taxes'] as List).map<TaxRate>(
          (x) => TaxRate.fromMap(x as Map<String, dynamic>),
        ))
            .fold<Map<String, TaxRate>>({}, (map, tax) {
              map[tax.name] = tax;
              return map;
            })
            .values
            .toList(),
      ),
      charges: List<AdditionalCharge>.from(
        ((map['charges'] as List).map<AdditionalCharge>(
          (x) => AdditionalCharge.fromMap(x as Map<String, dynamic>),
        ))
            .fold<Map<String, AdditionalCharge>>({}, (map, charge) {
              map[charge.name] = charge;
              return map;
            })
            .values
            .toList(),
      ),
    );
  }

  factory AdvanceInvoiceSettings.defaultValue() {
    return AdvanceInvoiceSettings(
      logoUrl:
          'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9WjXZh0AAAAASUVORK5CYII=',
      taxes: List.filled(5, TaxRate.defaultValue()),
      charges: List.filled(5, AdditionalCharge.defaultValue()),
    );
  }

  @override
  List<Object?> get props => [logoUrl, taxes, charges];
}
