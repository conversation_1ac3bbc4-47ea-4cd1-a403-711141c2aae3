class WalletBank {
  final String? bankName;
  final String? bankCode;
  final String? cbnCode;
  final String? id;
  final String? accountName;
  final String? accountNumber;
  final String? accountId;
  final String? icon;
  final WalletBankType? type;

  WalletBank({
    this.bankName,
    this.bankCode,
    this.cbnCode,
    this.id,
    this.accountName,
    this.accountNumber,
    this.accountId,
    this.icon,
    this.type,
  });

  factory WalletBank.fromMap(Map<String, dynamic> item, {String? icon}) {
    return WalletBank(
      bankName: item['bankName'],
      bankCode: item['bankCode'],
      cbnCode: item['cbnCode'],
      id: item['_id'] ?? item['id'],
      accountName: item["accountName"],
      accountNumber: item["accountNumber"],
      accountId: item["accountId"],
      icon: icon,
      type: WalletBankType.values.byName(item["type"] ?? "payment"),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bankName': bankName,
      'bankCode': bankCode,
      'cbnCode': cbnCode,
      'accountName': accountName,
      'accountNumber': accountNumber,
      'accountId': accountId,
      'icon': icon,
      'type': type?.name,
    };
  }

  Map<String, dynamic> toData() {
    return {
      '_id': id,
      'bankName': bankName,
      'accountType': type?.name,
      'accountId': accountId,
      'accountNumber': accountNumber,
      'bankCode': bankCode,
      'accountName': accountName,
    };
  }
}

enum WalletBankType { settlement, payment, accountVerification }
