import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_controller.dart';
import 'package:td_procurement/core/router/routes.dart';

class AdvanceInvoiceListScreen extends ConsumerStatefulWidget {
  const AdvanceInvoiceListScreen({super.key});

  @override
  ConsumerState<AdvanceInvoiceListScreen> createState() =>
      _AdvanceInvoiceListScreenState();
}

class _AdvanceInvoiceListScreenState
    extends ConsumerState<AdvanceInvoiceListScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(advanceInvoiceControllerProvider.notifier).fetchAdvanceInvoices(
          ref.read(advanceInvoiceControllerProvider).fetchAdvanceInvoiceParams);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text('Advance Invoices'),
          TextButton(
            onPressed: () => context.pushNamed(kCreateAdvanceInvoiceRoute),
            child: const Text('Create Advance Invoice'),
          ),
        ],
      ),
    );
  }
}
