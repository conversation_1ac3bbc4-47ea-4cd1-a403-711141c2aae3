import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/additional_charge.dart';
import 'package:td_procurement/app/order/widgets/sliver_delegate.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

import 'custom_widgets.dart';

class AdditionalChargeForm extends ConsumerStatefulWidget {
  final AdditionalCharge? initialCharge;

  const AdditionalChargeForm({super.key, this.initialCharge});

  @override
  ConsumerState<AdditionalChargeForm> createState() =>
      _AdditionalChargeFormDialogState();
}

class _AdditionalChargeFormDialogState
    extends ConsumerState<AdditionalChargeForm> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _amountController;
  final _isLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(
        text: widget.initialCharge?.name == null
            ? ''
            : widget.initialCharge!.name);
    _amountController = TextEditingController(
        text: widget.initialCharge?.amount == null
            ? ''
            : widget.initialCharge!.amount.toString());
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverHeaderDelegate(
              minHeight: 60,
              maxHeight: 60,
              child: Container(
                color: Colors.white,
                child: Row(
                  children: [
                    const Gap(18),
                    IconButton(
                      icon: SvgPicture.asset(kCloseSvg),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const Gap(10),
                    Text(
                      widget.initialCharge == null
                          ? 'Add Additional Charge'
                          : 'Edit Additional Charge',
                      style: textTheme.bodyLarge?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                children: [
                  const Gap(24),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Palette.kE7E7E7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Additional Charge Details'),
                            const Gap(16),
                            buildTextFormField(
                              controller: _nameController,
                              label: 'Charge Name',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a charge name';
                                }
                                return null;
                              },
                            ),
                            const Gap(16),
                            buildTextFormField(
                              controller: _amountController,
                              label: 'Amount',
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                Validators.formatCurrency(decimalPlaces: 2),
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter an amount';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter a valid number';
                                }
                                return null;
                              },
                            ),
                            const Gap(24),
                            SizedBox(
                              width: double.maxFinite,
                              child: CustomFilledButton(
                                text: widget.initialCharge == null
                                    ? 'Add'
                                    : 'Save',
                                loaderNotifier: _isLoading,
                                onPressed: _submitForm,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Gap(200),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final textTheme = context.textTheme;
    return Text(
      title,
      style: textTheme.headlineSmall,
    );
  }

  void _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      _isLoading.value = true;
      final charge = AdditionalCharge(
        id: widget.initialCharge?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        amount: double.parse(_amountController.text),
      );
      _isLoading.value = false;
      Navigator.pop(context, charge);
    }
  }
}
