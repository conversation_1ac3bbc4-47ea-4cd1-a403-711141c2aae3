import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Widget buildTextFormField({
  required TextEditingController controller,
  required String label,
  String? Function(String?)? validator,
  TextInputType keyboardType = TextInputType.text,
  List<TextInputFormatter> inputFormatters = const [],
}) {
  return TextFormField(
    controller: controller,
    decoration: InputDecoration(
      labelText: label,
      border: const OutlineInputBorder(),
    ),
    validator: validator,
    keyboardType: keyboardType,
    inputFormatters: inputFormatters,
  );
}

class DecimalNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final newText = newValue.text;

    // Allow empty input
    if (newText.isEmpty) return newValue;

    // Allow only valid number with optional decimal
    final isValid = RegExp(r'^\d*\.?\d*$').hasMatch(newText);

    if (!isValid) return oldValue;

    // Prevent multiple decimals
    final decimalCount = '.'.allMatches(newText).length;
    if (decimalCount > 1) return oldValue;

    return newValue;
  }
}
