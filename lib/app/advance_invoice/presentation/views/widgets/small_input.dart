import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:td_procurement/src/extensions/index.dart';

class SmallInput extends StatelessWidget {
  const SmallInput({
    super.key,
    required this.controller,
    this.autofocus = false,
    this.hint,
    this.prefix,
    this.suffix,
    this.keyboardType,
    this.inputFormatters,
    this.onEditingComplete,
    this.onChanged,
    this.maxLines,
    this.minLines,
  });

  final TextEditingController controller;
  final bool autofocus;
  final String? hint;
  final String? prefix;
  final String? suffix;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Function()? onEditingComplete;
  final Function(String)? onChanged;
  final int? maxLines;
  final int? minLines;
  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      autofocus: autofocus,
      maxLines: maxLines ?? 1,
      minLines: minLines,
      style: context.textTheme.bodyMedium,
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: const TextStyle(fontSize: 12),
        prefixText: prefix,
        suffixText: suffix,
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: Colors.grey),
        ),
      ),
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      onEditingComplete: onEditingComplete,
      onChanged: onChanged,
      cursorHeight: 12,
    );
  }
}
