import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class SmallInput extends StatelessWidget {
  const SmallInput({
    super.key,
    required this.controller,
    this.autofocus = false,
    this.hint,
    this.prefix,
    this.suffix,
    this.keyboardType,
    this.inputFormatters,
    this.onEditingComplete,
    this.onChanged,
    this.maxLines,
    this.minLines,
    this.enabled = true,
    this.height = 36.0,
  });

  final TextEditingController controller;
  final bool autofocus;
  final String? hint;
  final String? prefix;
  final String? suffix;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Function()? onEditingComplete;
  final Function(String)? onChanged;
  final int? maxLines;
  final int? minLines;
  final bool enabled;
  final double height;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SizedBox(
      height: maxLines == 1 ? height : null,
      child: TextField(
        controller: controller,
        autofocus: autofocus,
        maxLines: maxLines ?? 1,
        minLines: minLines,
        enabled: enabled,
        style: textTheme.bodyMedium?.copyWith(
          fontSize: 13,
          fontWeight: FontWeight.w400,
          color: enabled ? Palette.primaryBlack : Palette.placeholder,
        ),
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        onEditingComplete: onEditingComplete,
        onChanged: onChanged,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: textTheme.bodyMedium?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: Palette.placeholder,
          ),
          prefixText: prefix,
          suffixText: suffix,
          filled: true,
          fillColor: enabled ? Colors.white : Palette.kF7F7F7,
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Palette.stroke),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Palette.stroke),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(
              color: Palette.primary,
              width: 2,
            ),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(
              color: Palette.stroke.withValues(alpha: 0.5),
            ),
          ),
        ),
      ),
    );
  }
}
