import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/discount.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_controller.dart';
import 'package:td_procurement/app/order/widgets/sliver_delegate.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

import 'custom_widgets.dart';

class LineItemsWidget extends ConsumerWidget {
  const LineItemsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = context.textTheme;
    final lineItems = ref.watch(createAdvanceInvoiceProvider).lineItems;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Line Items',
                style: textTheme.titleMedium
                    ?.copyWith(fontWeight: FontWeight.w700)),
            TextButton.icon(
              onPressed: () => _showAddLineItemDialog(context, ref),
              icon: const Icon(Icons.add),
              label: const Text('Add Item'),
            ),
          ],
        ),
        const Gap(16),
        if (lineItems.isEmpty)
          Center(
            child: Text(
              'No items added yet',
              style: textTheme.bodyMedium?.copyWith(color: Palette.placeholder),
            ),
          )
        else
          ...lineItems.mapIndexed(
            (index, item) {
              final item = lineItems[index];
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    title: Text(item.name),
                    subtitle: Text(
                      '${item.quantity} x ${item.unitPrice.formattedAmount(context)} = ${(item.quantity * item.unitPrice).formattedAmount(context)}',
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () =>
                              _showEditLineItemDialog(context, ref, item),
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () => ref
                              .read(advanceInvoiceControllerProvider.notifier)
                              .removeLineItem(item),
                        ),
                      ],
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                  if (index < lineItems.length - 1)
                    Divider(
                      indent: 0.1,
                      endIndent: 0.1,
                      height: 1,
                      color: Palette.kE7E7E7,
                    ),
                ],
              );
            },
          ),
      ],
    );
  }

  Future<void> _showAddLineItemDialog(
      BuildContext context, WidgetRef ref) async {
    final result = await showCustomGeneralDialog<LineItemParam?>(
      context,
      child: const LineItemForm(),
      percentage: 0.4,
      minRightSectionWidth: 520,
    );

    if (result != null) {
      ref.read(advanceInvoiceControllerProvider.notifier).addLineItem(result);
    }
  }

  Future<void> _showEditLineItemDialog(
      BuildContext context, WidgetRef ref, LineItemParam item) async {
    final result = await showCustomGeneralDialog<LineItemParam?>(
      context,
      child: LineItemForm(initialItem: item),
      percentage: 0.4,
      minRightSectionWidth: 520,
    );

    if (result != null) {
      ref.read(advanceInvoiceControllerProvider.notifier).removeLineItem(item);
      ref.read(advanceInvoiceControllerProvider.notifier).addLineItem(result);
    }
  }
}

class LineItemForm extends ConsumerStatefulWidget {
  final LineItemParam? initialItem;

  const LineItemForm({super.key, this.initialItem});

  @override
  ConsumerState<LineItemForm> createState() => _LineItemFormDialogState();
}

class _LineItemFormDialogState extends ConsumerState<LineItemForm> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _quantityController;
  late final TextEditingController _unitPriceController;
  late final TextEditingController _taxRateController;
  late final TextEditingController _discountValueController;
  final _isLoading = ValueNotifier<bool>(false);
  DiscountType _selectedDiscountType = DiscountType.fixed;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialItem?.name);
    _quantityController =
        TextEditingController(text: widget.initialItem?.quantity.toString());
    _unitPriceController =
        TextEditingController(text: widget.initialItem?.unitPrice.toString());
    _taxRateController =
        TextEditingController(text: widget.initialItem?.taxRate.toString());
    _discountValueController = TextEditingController(
        text: widget.initialItem?.discount?.value.toString());
    _selectedDiscountType =
        widget.initialItem?.discount?.type ?? DiscountType.fixed;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _taxRateController.dispose();
    _discountValueController.dispose();
    _isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverHeaderDelegate(
              minHeight: 60,
              maxHeight: 60,
              child: Container(
                color: Colors.white,
                child: Row(
                  children: [
                    const Gap(18),
                    IconButton(
                      icon: SvgPicture.asset(kCloseSvg),
                      onPressed: () => Navigator.pop(context),
                    ),
                    const Gap(10),
                    Text(
                      widget.initialItem == null
                          ? 'Add Line Item'
                          : 'Edit Line Item',
                      style: textTheme.bodyLarge?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                children: [
                  const Gap(24),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Palette.kE7E7E7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Line item details'),
                            const Gap(16),
                            buildTextFormField(
                              controller: _nameController,
                              label: 'Name',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter an item name';
                                }
                                return null;
                              },
                            ),
                            const Gap(16),
                            buildTextFormField(
                              controller: _quantityController,
                              label: 'Quantity',
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                Validators.formatInput(),
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a quantity';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter a valid number';
                                }
                                return null;
                              },
                            ),
                            const Gap(16),
                            buildTextFormField(
                              controller: _unitPriceController,
                              label: 'Price',
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                Validators.formatCurrency(decimalPlaces: 2),
                              ],
                              validator: (value) {
                                log(value.toString());
                                value = value?.replaceAll(',', '');
                                log(value.toString());
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a unit price';
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter a valid number';
                                }
                                return null;
                              },
                            ),
                            const Gap(16),
                            buildTextFormField(
                              controller: _taxRateController,
                              label: 'Tax (%)',
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                Validators.validDecimalNumberInput()
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a tax rate';
                                }
                                final rate = double.tryParse(value);
                                if (rate == null) {
                                  return 'Please enter a valid number';
                                }
                                if (rate < 0 || rate > 100) {
                                  return 'Tax rate must be between 0 and 100';
                                }
                                return null;
                              },
                            ),
                            const Gap(16),
                            Row(
                              children: [
                                Expanded(
                                  child: DropdownButtonFormField<DiscountType>(
                                    value: _selectedDiscountType,
                                    decoration: const InputDecoration(
                                      labelText: 'Discount Type',
                                      border: OutlineInputBorder(),
                                    ),
                                    items: DiscountType.values.map((type) {
                                      return DropdownMenuItem(
                                        value: type,
                                        child: Text(type == DiscountType.fixed
                                            ? 'Fixed'
                                            : 'Percentage'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _selectedDiscountType = value;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const Gap(16),
                                Expanded(
                                  child: buildTextFormField(
                                    controller: _discountValueController,
                                    label: _selectedDiscountType ==
                                            DiscountType.fixed
                                        ? 'Discount Amount'
                                        : 'Discount (%)',
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      _selectedDiscountType ==
                                              DiscountType.fixed
                                          ? Validators.formatCurrency(
                                              decimalPlaces: 2)
                                          : Validators.formatRange(0, 100)
                                    ],
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return null; // Optional field
                                      }
                                      final amount = double.tryParse(value);
                                      if (amount == null) {
                                        return 'Please enter a valid number';
                                      }
                                      if (_selectedDiscountType ==
                                              DiscountType.percentage &&
                                          (amount < 0 || amount > 100)) {
                                        return 'Percentage must be between 0 and 100';
                                      }
                                      return null;
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const Gap(24),
                            SizedBox(
                              width: double.maxFinite,
                              child: CustomFilledButton(
                                text:
                                    widget.initialItem == null ? 'Add' : 'Save',
                                loaderNotifier: _isLoading,
                                onPressed: _submitForm,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Gap(200),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final textTheme = context.textTheme;
    return Text(
      title,
      style: textTheme.headlineSmall,
    );
  }

  void _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      _isLoading.value = true;

      // Create discount if value is provided
      Discount? discount;
      if (_discountValueController.text.isNotEmpty) {
        final value = double.parse(_discountValueController.text);
        if (value > 0) {
          discount = Discount(
            type: _selectedDiscountType,
            value: value,
          );
        }
      }

      final lineItem = LineItemParam(
        name: _nameController.text,
        quantity: double.parse(_quantityController.text),
        unitPrice: double.parse(_unitPriceController.text),
        taxRate: double.parse(_taxRateController.text),
        discount: discount,
        index: widget.initialItem?.index,
      );
      _isLoading.value = false;
      Navigator.of(context).pop(lineItem);
    }
  }
}
