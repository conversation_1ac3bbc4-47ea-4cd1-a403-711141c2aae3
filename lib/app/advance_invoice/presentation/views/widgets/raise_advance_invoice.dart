import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/create_retail_invoice_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_state.dart';
import 'package:td_procurement/app/advance_invoice/presentation/views/widgets/small_input.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

class RaiseAdvanceInvoiceWidget extends ConsumerWidget {
  const RaiseAdvanceInvoiceWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final state = ref.watch(advanceInvoiceControllerProvider);
    final notifier = ref.read(advanceInvoiceControllerProvider.notifier);
    final loanContract = state.loanContract.asData?.value;
    final bankAccounts = state.bankAccounts.asData?.value ?? [];
    final textTheme = context.textTheme;

    // Add state for showing the add bank account form
    return _RaiseAdvanceInvoiceWidgetBody(
      createParams: createParams,
      state: state,
      notifier: notifier,
      loanContract: loanContract,
      bankAccounts: bankAccounts,
      textTheme: textTheme,
    );
  }
}

class _RaiseAdvanceInvoiceWidgetBody extends ConsumerStatefulWidget {
  const _RaiseAdvanceInvoiceWidgetBody({
    required this.createParams,
    required this.state,
    required this.notifier,
    required this.loanContract,
    required this.bankAccounts,
    required this.textTheme,
  });

  final CreateRetailInvoiceParam createParams;
  final AdvanceInvoiceState state;
  final AdvanceInvoiceController notifier;
  final LoanContract? loanContract;
  final List<WalletBank> bankAccounts;
  final TextTheme textTheme;

  @override
  ConsumerState<_RaiseAdvanceInvoiceWidgetBody> createState() =>
      _RaiseAdvanceInvoiceWidgetBodyState();
}

class _RaiseAdvanceInvoiceWidgetBodyState
    extends ConsumerState<_RaiseAdvanceInvoiceWidgetBody> {
  bool showAddBankForm = false;
  SettlementBank? selectedBank;
  final TextEditingController _accountNumberController =
      TextEditingController();
  final TextEditingController _accountNameController = TextEditingController();
  bool _consent = false;
  bool _isValidAccountName = false;
  bool _isValidating = false;
  bool _isSubmitting = false;
  String? _validationError;
  String? _submitError;

  @override
  void dispose() {
    _accountNumberController.dispose();
    _accountNameController.dispose();
    super.dispose();
  }

  void _validateAccount() async {
    setState(() {
      _isValidAccountName = false;
      _isValidating = true;
      _validationError = null;
      _accountNameController.clear();
    });
    final bank = selectedBank;
    final number = _accountNumberController.text;
    if (bank == null || number.length != 10) {
      setState(() {
        _isValidating = false;
      });
      return;
    }
    final res =
        await ref.read(validateBankAccountUseCaseProvider((number, bank.code)));
    res.when(
      success: (data) {
        setState(() {
          _isValidating = false;
          _isValidAccountName = data.$1.isNotEmpty;
          _accountNameController.text = data.$1;
          _validationError = data.$1.isEmpty ? 'Account not found' : null;
        });
      },
      failure: (e, _) {
        setState(() {
          _isValidating = false;
          _validationError = e.toString();
        });
      },
    );
  }

  void _submit() async {
    setState(() {
      _isSubmitting = true;
      _submitError = null;
    });
    final bank = selectedBank;
    final number = _accountNumberController.text;
    final name = _accountNameController.text;
    if (bank == null || number.length != 10 || name.isEmpty || !_consent) {
      setState(() {
        _isSubmitting = false;
        _submitError = 'Please complete all fields.';
      });
      return;
    }
    final params = LinkBankParams(
      number,
      bank.code,
      name,
      bank.name,
      null,
      LinkBankType.terminal,
    );
    final res = await ref.read(linkBankAccountUseCaseProvider(params));
    res.when(
      success: (_) async {
        await ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchBankAccounts(forced: true);
        setState(() {
          _isSubmitting = false;
          showAddBankForm = false;
          _accountNumberController.clear();
          _accountNameController.clear();
          _consent = false;
          _isValidAccountName = false;
          _validationError = null;
          _submitError = null;
          selectedBank = null;
        });
        if (mounted) {
          Toast.success('Bank account added successfully', context);
        }
      },
      failure: (e, _) {
        setState(() {
          _isSubmitting = false;
          _submitError = e.toString();
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final settlementBanks = ref
            .watch(advanceInvoiceControllerProvider)
            .settlementBanks
            .asData
            ?.value ??
        [];
    final textTheme = widget.textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 100),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Gap(40),
          const OutletSearchAutoCompleteWidget(isAdvanceInvoice: true),
          const Gap(40),
          // Items Section
          const _ItemsSection(key: ValueKey('items')),
          const Gap(40),
          // Service Options Section (for Taxes/Charges)
          const _ServiceOptionsSection(
            key: ValueKey('service_options'),
          ),
          const Gap(40),
          // Payment & Financing Section
          Card(
            margin: EdgeInsets.zero,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Payment & Financing Options',
                      style: textTheme.titleMedium),
                  const Gap(12),
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    title: Text('Apply Financing', style: textTheme.bodyLarge),
                    subtitle: Text(
                      'Enable this to use TradeDepot financing for this invoice. If disabled, you must select a payment account.',
                      style:
                          textTheme.bodySmall?.copyWith(color: Colors.black54),
                    ),
                    trailing: Switch.adaptive(
                      value: widget.createParams.isFinanced,
                      activeColor: Palette.primary,
                      onChanged: (val) {
                        widget.notifier.toggleFinancing(val);
                        if (val) {
                          setState(() {
                            showAddBankForm = false;
                          });
                        }
                      },
                    ),
                  ),
                  const Gap(12),
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder:
                        (Widget child, Animation<double> animation) {
                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(0, -0.1),
                          end: Offset.zero,
                        ).animate(CurvedAnimation(
                          parent: animation,
                          curve: Curves.easeInOut,
                        )),
                        child: FadeTransition(
                          opacity: animation,
                          child: child,
                        ),
                      );
                    },
                    child: widget.createParams.isFinanced
                        ? Container(
                            key: const ValueKey('financing'),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.05),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.percent, color: Colors.blue),
                                const Gap(8),
                                Text(
                                  widget.loanContract != null
                                      ? 'Your financing rate: ${widget.loanContract?.monthlyInterest ?? 0}%'
                                      : 'Loading financing rate...',
                                  style: textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          )
                        : Container(
                            key: const ValueKey('bank-selection'),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Text('Select Payment Account',
                                //     style: textTheme.bodyLarge),
                                Skeletonizer(
                                  enabled: ref
                                          .watch(
                                              advanceInvoiceControllerProvider)
                                          .bankAccounts
                                          .isLoading ||
                                      showAddBankForm,
                                  child: SizedBox(
                                    height: 32,
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: DropdownButtonHideUnderline(
                                            child: DropdownButtonFormField<
                                                WalletBank?>(
                                              value: widget
                                                      .bankAccounts.isNotEmpty
                                                  ? widget.bankAccounts
                                                      .firstWhereOrNull((bank) =>
                                                          bank.accountNumber ==
                                                          widget
                                                              .createParams
                                                              .bankAccount
                                                              ?.accountNumber)
                                                  : null,
                                              // isExpanded: true,
                                              // isDense: false,
                                              enableFeedback: false,
                                              // menuMaxHeight: 30,
                                              focusColor: Colors.transparent,
                                              elevation: 0,
                                              hint: Text(
                                                  'Select Payment Account',
                                                  style: textTheme.bodyMedium),
                                              items: widget.bankAccounts
                                                  .map((b) => DropdownMenuItem<
                                                          WalletBank?>(
                                                        value: b,
                                                        child: FittedBox(
                                                          fit: BoxFit.scaleDown,
                                                          child: Text(
                                                            '${b.bankName} - ${b.accountNumber ?? ''}',
                                                            style: textTheme
                                                                .bodyMedium,
                                                          ),
                                                        ),
                                                      ))
                                                  .toList(),
                                              onChanged: (val) {
                                                if (val != null) {
                                                  widget.notifier
                                                      .addBankAccount(val);
                                                }
                                              },
                                              decoration: const InputDecoration(
                                                border: OutlineInputBorder(),
                                                isDense: true,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 10),
                                              ),
                                            ),
                                          ),
                                        ),
                                        const Gap(12),
                                        TextButton.icon(
                                          icon: const Icon(Icons.add, size: 18),
                                          label: const Text('Add Bank Account'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            textStyle: const TextStyle(
                                                fontWeight: FontWeight.w600),
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              showAddBankForm =
                                                  !showAddBankForm;
                                              if (!showAddBankForm) {
                                                _accountNumberController
                                                    .clear();
                                                _accountNameController.clear();
                                                _consent = false;
                                                _isValidAccountName = false;
                                                _validationError = null;
                                                _submitError = null;
                                                selectedBank = null;
                                              }
                                            });
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  transitionBuilder: (Widget child,
                                      Animation<double> animation) {
                                    return SlideTransition(
                                      position: Tween<Offset>(
                                        begin: const Offset(0, -0.1),
                                        end: Offset.zero,
                                      ).animate(CurvedAnimation(
                                        parent: animation,
                                        curve: Curves.easeInOut,
                                      )),
                                      child: SizeTransition(
                                        sizeFactor: animation,
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: showAddBankForm
                                      ? Skeletonizer(
                                          key: const ValueKey('add-bank-form'),
                                          enabled: ref
                                              .watch(
                                                  advanceInvoiceControllerProvider)
                                              .settlementBanks
                                              .isLoading,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                top: 8.0, left: 2.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  height: 32,
                                                  child:
                                                      DropdownButtonHideUnderline(
                                                    child:
                                                        DropdownButtonFormField<
                                                            SettlementBank>(
                                                      value: selectedBank,
                                                      isDense: true,
                                                      hint: Text(
                                                          'Settlement Bank',
                                                          style: textTheme
                                                              .bodyMedium),
                                                      items: settlementBanks
                                                          .map((b) =>
                                                              DropdownMenuItem<
                                                                  SettlementBank>(
                                                                value: b,
                                                                child:
                                                                    FittedBox(
                                                                  fit: BoxFit
                                                                      .scaleDown,
                                                                  child: Text(
                                                                      b.name,
                                                                      style: textTheme
                                                                          .bodyMedium),
                                                                ),
                                                              ))
                                                          .toList(),
                                                      onChanged: (val) {
                                                        setState(() {
                                                          selectedBank = val;
                                                        });
                                                        if (val != null &&
                                                            _accountNumberController
                                                                    .text
                                                                    .length ==
                                                                10) {
                                                          _validateAccount();
                                                        }
                                                      },
                                                      decoration:
                                                          const InputDecoration(
                                                        border:
                                                            OutlineInputBorder(),
                                                        isDense: true,
                                                        contentPadding:
                                                            EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        12,
                                                                    vertical:
                                                                        10),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const Gap(12),
                                                SmallInput(
                                                  controller:
                                                      _accountNumberController,
                                                  hint:
                                                      'Enter 10-digit account number',
                                                  keyboardType:
                                                      TextInputType.number,
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly,
                                                    LengthLimitingTextInputFormatter(
                                                        10),
                                                  ],
                                                  onEditingComplete: () {
                                                    if (selectedBank != null &&
                                                        _accountNumberController
                                                                .text.length ==
                                                            10) {
                                                      _validateAccount();
                                                    }
                                                  },
                                                  onChanged: (val) {
                                                    if (val.length == 10 &&
                                                        selectedBank != null) {
                                                      _validateAccount();
                                                    }
                                                  },
                                                ),
                                                // if (_validationError != null)
                                                //   Padding(
                                                //     padding: const EdgeInsets.only(top: 4),
                                                //     child: Text(_validationError!,
                                                //         style: const TextStyle(
                                                //             color: Colors.red,
                                                //             fontSize: 12)),
                                                //   ),
                                                const Gap(12),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: TextField(
                                                        controller:
                                                            _accountNameController,
                                                        readOnly: true,
                                                        style: context.textTheme
                                                            .bodyMedium,
                                                        decoration:
                                                            InputDecoration(
                                                          hintText:
                                                              'Account Name',
                                                          hintStyle:
                                                              const TextStyle(
                                                                  fontSize: 12),
                                                          isDense: true,
                                                          contentPadding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal: 8,
                                                                  vertical: 8),
                                                          border:
                                                              OutlineInputBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                            borderSide:
                                                                const BorderSide(
                                                                    color: Colors
                                                                        .grey),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    if (_isValidating)
                                                      const Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                                left: 8),
                                                        child: SizedBox(
                                                            width: 16,
                                                            height: 16,
                                                            child:
                                                                CircularProgressIndicator(
                                                                    strokeWidth:
                                                                        2)),
                                                      ),
                                                  ],
                                                ),
                                                const Gap(16),
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Skeleton.shade(
                                                      child: Checkbox(
                                                        value: _consent,
                                                        onChanged: (val) {
                                                          setState(() {
                                                            _consent =
                                                                val ?? false;
                                                          });
                                                        },
                                                        materialTapTargetSize:
                                                            MaterialTapTargetSize
                                                                .shrinkWrap,
                                                        visualDensity:
                                                            VisualDensity
                                                                .compact,
                                                      ),
                                                    ),
                                                    const Gap(8),
                                                    Expanded(
                                                      child: Text(
                                                        'I confirm that this bank account belongs to me and I authorize TradeDepot to verify this account.',
                                                        style: textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400,
                                                                color: const Color(
                                                                    0xFF4B5563)),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                if (_submitError != null)
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            top: 4),
                                                    child: Text(_submitError!,
                                                        style: const TextStyle(
                                                            color: Colors.red,
                                                            fontSize: 12)),
                                                  ),
                                                const Gap(20),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    TextButton(
                                                      onPressed: _isSubmitting
                                                          ? null
                                                          : () {
                                                              setState(() {
                                                                showAddBankForm =
                                                                    false;
                                                                _accountNumberController
                                                                    .clear();
                                                                _accountNameController
                                                                    .clear();
                                                                _consent =
                                                                    false;
                                                                _isValidAccountName =
                                                                    false;
                                                                _validationError =
                                                                    null;
                                                                _submitError =
                                                                    null;
                                                                selectedBank =
                                                                    null;
                                                              });
                                                            },
                                                      child:
                                                          const Text('Cancel'),
                                                    ),
                                                    const Gap(8),
                                                    ElevatedButton(
                                                      onPressed: _isSubmitting ||
                                                              !_consent ||
                                                              !_isValidAccountName
                                                          ? null
                                                          : _submit,
                                                      child: _isSubmitting
                                                          ? const SizedBox(
                                                              width: 16,
                                                              height: 16,
                                                              child: CircularProgressIndicator(
                                                                  strokeWidth:
                                                                      2,
                                                                  color: Colors
                                                                      .white))
                                                          : const Text(
                                                              'Add Account'),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                      : const SizedBox.shrink(
                                          key: ValueKey('no-form')),
                                ),
                              ],
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
          const Gap(40),
        ],
      ),
    );
  }
}

class _ItemsSection extends ConsumerStatefulWidget {
  const _ItemsSection({super.key});
  @override
  ConsumerState<_ItemsSection> createState() => _ItemsSectionState();
}

class _ItemsSectionState extends ConsumerState<_ItemsSection> {
  bool showForm = false;
  bool showDiscountFields = false;
  String? editingItemId;
  final _nameController = TextEditingController();
  final _qtyController = TextEditingController();
  final _priceController = TextEditingController();
  final _taxController = TextEditingController();
  final _discountValueController = TextEditingController();
  DiscountType _selectedDiscountType = DiscountType.fixed;

  @override
  void dispose() {
    _nameController.dispose();
    _qtyController.dispose();
    _priceController.dispose();
    _taxController.dispose();
    _discountValueController.dispose();
    super.dispose();
  }

  void _startEditing(LineItemParam item) {
    setState(() {
      editingItemId = item.id;
      _nameController.text = item.name;
      _qtyController.text = item.quantity.toString();
      _priceController.text = item.unitPrice.toString();
      _taxController.text = item.taxRate.toString();
      _discountValueController.text = item.discount?.value.toString() ?? '';
      _selectedDiscountType = item.discount?.type ?? DiscountType.fixed;
      showDiscountFields = item.discount != null;
      showForm = true;
    });
  }

  void _saveItem() {
    final name = _nameController.text.trim();
    final quantity =
        int.tryParse(_qtyController.text.replaceAll(',', '').trim()) ?? 0;
    final price =
        double.tryParse(_priceController.text.replaceAll(',', '').trim()) ?? 0;
    final taxRate =
        double.tryParse(_taxController.text.replaceAll(',', '').trim()) ?? 0;

    if (name.isNotEmpty && quantity > 0 && price > 0) {
      // Create discount if value is provided and discount fields are shown
      Discount? discount;
      if (showDiscountFields && _discountValueController.text.isNotEmpty) {
        final value = double.tryParse(
                _discountValueController.text.replaceAll(',', '').trim()) ??
            0;
        if (value > 0 ||
            (_selectedDiscountType == DiscountType.percentage &&
                value >= 0 &&
                value <= 100)) {
          discount = Discount(
            type: _selectedDiscountType,
            value: value,
          );
        }
      }

      if (editingItemId != null) {
        // Update existing item
        final updatedItem = LineItemParam(
          id: editingItemId!,
          name: name,
          quantity: quantity,
          unitPrice: price,
          taxRate: taxRate,
          discount: discount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateLineItem(updatedItem);
        setState(() {
          editingItemId = null;
          showForm = false;
          showDiscountFields = false;
          _nameController.clear();
          _qtyController.clear();
          _priceController.clear();
          _taxController.clear();
          _discountValueController.clear();
        });
      } else {
        // Add new item
        final newItem = LineItemParam(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          quantity: quantity,
          unitPrice: price,
          taxRate: taxRate,
          discount: discount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addLineItem(newItem);
        setState(() {
          showForm = false;
          showDiscountFields = false;
          _nameController.clear();
          _qtyController.clear();
          _priceController.clear();
          _taxController.clear();
          _discountValueController.clear();
        });
      }
    }
  }

  void _cancelEdit() {
    setState(() {
      editingItemId = null;
      showForm = false;
      showDiscountFields = false;
      _nameController.clear();
      _qtyController.clear();
      _priceController.clear();
      _taxController.clear();
      _discountValueController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final lineItems = ref.watch(createAdvanceInvoiceProvider).lineItems;
    final currencyCode = ref.read(currencyCodeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Items',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Palette.primaryBlack,
          ),
        ),
        const Gap(16),
        // Add Item Section with smooth expansion
        ToggleSection(
          title: 'Add Item',
          initiallyExpanded: showForm,
          onExpansionChanged: (expanded) {
            setState(() {
              editingItemId = null;
              showForm = expanded;
              showDiscountFields = false;
              _nameController.clear();
              _qtyController.clear();
              _priceController.clear();
              _taxController.clear();
              _discountValueController.clear();
            });
          },
          content: _buildItemForm(context, textTheme, currencyCode),
        ),
        const Gap(16),
        // Items List
        _buildItemsList(context, textTheme, currencyCode, lineItems),
      ],
    );
  }

  Widget _buildItemForm(
      BuildContext context, TextTheme textTheme, String currencyCode) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: CompactInput(
                controller: _nameController,
                hint: 'Item name',
                autofocus: true,
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _qtyController,
                hint: 'Quantity',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatInput()],
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _priceController,
                hint: 'Unit Price',
                prefix: '${CurrencyWidget.symbol(context, currencyCode)} ',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatCurrency(decimalPlaces: 2)],
              ),
            ),
            const Gap(12),
            Expanded(
              child: CompactInput(
                controller: _taxController,
                hint: 'Tax (%)',
                suffix: '%',
                keyboardType: TextInputType.number,
                inputFormatters: [Validators.formatRange(0, 100)],
                onEditingComplete: () {
                  if (!showDiscountFields) {
                    _saveItem();
                  }
                },
              ),
            ),
          ],
        ),
        const Gap(12),
        Row(
          children: [
            if (!showDiscountFields)
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Checkbox(
                    value: false,
                    onChanged: (value) {
                      setState(() {
                        showDiscountFields = true;
                      });
                    },
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                  ),
                  Text(
                    'Add discount',
                    style: textTheme.bodySmall?.copyWith(
                      color: Palette.primary,
                    ),
                  ).onTapNoFeedback(() {
                    setState(() {
                      showDiscountFields = true;
                    });
                  }),
                ],
              )
            else ...[
              SizedBox(
                width: 140,
                child: AdvancedDropdown<DiscountType>(
                  selectedValue: _selectedDiscountType,
                  options: DiscountType.values,
                  hint: 'Type',
                  itemToString: (type) =>
                      type == DiscountType.fixed ? 'Fixed' : 'Percentage',
                  onChanged: (type) {
                    if (type != null) {
                      setState(() {
                        _selectedDiscountType = type;
                        _discountValueController.clear();
                      });
                    }
                  },
                  searchable: false,
                  menuMaxHeight: 120,
                ),
              ),
              const Gap(12),
              Expanded(
                flex: 2,
                child: CompactInput(
                  controller: _discountValueController,
                  hint: _selectedDiscountType == DiscountType.fixed
                      ? 'Amount'
                      : 'Value (%)',
                  prefix: _selectedDiscountType == DiscountType.fixed
                      ? '${CurrencyWidget.symbol(context, currencyCode)} '
                      : null,
                  suffix: _selectedDiscountType == DiscountType.percentage
                      ? '%'
                      : null,
                  keyboardType: TextInputType.number,
                  inputFormatters:
                      _selectedDiscountType == DiscountType.percentage
                          ? [Validators.formatRange(0, 100)]
                          : [Validators.formatCurrency(decimalPlaces: 2)],
                  onEditingComplete: _saveItem,
                ),
              ),
              const Gap(8),
              IconButton(
                icon: const Icon(Icons.close, size: 18),
                onPressed: () {
                  setState(() {
                    showDiscountFields = false;
                    _discountValueController.clear();
                  });
                },
              ),
            ],
            const Spacer(),
            OutlinedButton(
              style: OutlinedButton.styleFrom(
                minimumSize: const Size(0, 36),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onPressed: _saveItem,
              child: Text(editingItemId != null ? 'Update' : 'Save'),
            ),
            const Gap(8),
            TextButton(
              style: TextButton.styleFrom(
                minimumSize: const Size(0, 36),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onPressed: _cancelEdit,
              child: const Text('Cancel'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildItemsList(BuildContext context, TextTheme textTheme,
      String currencyCode, List<LineItemParam> lineItems) {
    if (lineItems.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Palette.kF7F7F7,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Palette.stroke),
        ),
        child: Center(
          child: Text(
            'No items added yet',
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.placeholder,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Column(
      children: lineItems.mapIndexed((index, item) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Palette.stroke),
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            title: Text(
              item.name,
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Palette.primaryBlack,
              ),
            ),
            subtitle: Text(
              'Qty: ${item.quantity} × ${CurrencyWidget.value(context, currencyCode, item.unitPrice)}',
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (item.discount != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: Palette.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      item.discount!.type == DiscountType.fixed
                          ? CurrencyWidget.value(
                              context, currencyCode, item.discount!.value)
                          : '${item.discount!.value}%',
                      style: textTheme.bodySmall?.copyWith(
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                Text(
                  CurrencyWidget.value(
                      context, currencyCode, (item.quantity * item.unitPrice)),
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Palette.primaryBlack,
                  ),
                ),
                const Gap(8),
                IconButton(
                  icon: Icon(Icons.edit, size: 18, color: Palette.primary),
                  onPressed: () => _startEditing(item),
                ),
                IconButton(
                  icon: Icon(Icons.delete, size: 18, color: Palette.kE61010),
                  onPressed: () {
                    ref
                        .read(advanceInvoiceControllerProvider.notifier)
                        .removeLineItem(item);
                  },
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}

class _ServiceOptionsSection extends ConsumerStatefulWidget {
  const _ServiceOptionsSection({super.key});
  @override
  ConsumerState<_ServiceOptionsSection> createState() =>
      _ServiceOptionsSectionState();
}

class _ServiceOptionsSectionState extends ConsumerState<_ServiceOptionsSection>
    with TickerProviderStateMixin {
  final List<TaxRate> selectedTaxes = [];
  final List<AdditionalCharge> selectedCharges = [];
  bool showAddOptions = false;
  bool showTaxForm = false;
  bool showChargeForm = false;
  bool showTaxDropdown = false;
  bool showChargeDropdown = false;
  String? editingTaxId;
  String? editingChargeId;
  final _taxNameController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _chargeNameController = TextEditingController();
  final _chargeAmountController = TextEditingController();

  // Discount/Note state
  bool showDiscountForm = false;
  bool editingDiscount = false;
  DiscountType selectedType = DiscountType.fixed;
  final _discountValueController = TextEditingController();
  bool showNoteInput = false;
  final _noteController = TextEditingController();

  // Persistent controllers for each input
  final Map<String, TextEditingController> _taxControllers = {};
  final Map<String, TextEditingController> _chargeControllers = {};

  @override
  void dispose() {
    _taxNameController.dispose();
    _taxRateController.dispose();
    _chargeNameController.dispose();
    _chargeAmountController.dispose();
    _discountValueController.dispose();
    _noteController.dispose();
    for (var c in _taxControllers.values) {
      c.dispose();
    }
    for (var c in _chargeControllers.values) {
      c.dispose();
    }
    super.dispose();
  }

  bool _isValidTaxRate(String? value) {
    if (value == null || value.isEmpty) return false;
    final rate = double.tryParse(value.replaceAll(',', '').trim());
    if (rate == null) return false;
    return rate >= 0 && rate <= 100;
  }

  bool _isValidAmount(String? value) {
    if (value == null || value.isEmpty) return false;
    final amount = double.tryParse(value.replaceAll(',', '').trim());
    if (amount == null) return false;
    return amount >= 0;
  }

  double _calculateTaxAmount(TaxRate tax) {
    final lineItems = ref.read(createAdvanceInvoiceProvider).lineItems;
    final subtotal = lineItems.fold<double>(
        0, (sum, item) => sum + (item.quantity * item.unitPrice));
    return (subtotal * (tax.rate ?? 0)) / 100;
  }

  void _startEditingTax(TaxRate tax) {
    setState(() {
      editingTaxId = tax.id;
      _taxNameController.text = tax.name;
      _taxRateController.text = tax.rate != null ? tax.rate.toString() : '';
      showTaxForm = true;
      showTaxDropdown = false;
      showChargeForm = false;
      showChargeDropdown = false;
    });
  }

  void _startEditingCharge(AdditionalCharge charge) {
    setState(() {
      editingChargeId = charge.id;
      _chargeNameController.text = charge.name;
      _chargeAmountController.text =
          charge.amount != null ? charge.amount.toString() : '';
      showChargeForm = true;
      showChargeDropdown = false;
      showTaxForm = false;
      showTaxDropdown = false;
    });
  }

  void _startEditingDiscount() {
    setState(() {
      showDiscountForm = !showDiscountForm;
      showNoteInput = false;
      showTaxDropdown = false;
      showChargeDropdown = false;
      showTaxForm = false;
      showChargeForm = false;
      // Set up discount form state
      final discount = ref.read(createAdvanceInvoiceProvider).discount;
      editingDiscount = discount != null;
      if (discount != null) {
        selectedType = discount.type;
        _discountValueController.text = discount.value.toString();
      } else {
        selectedType = DiscountType.fixed;
        _discountValueController.clear();
      }
    });
  }

  void _startEditingNote() {
    setState(() {
      showNoteInput = !showNoteInput;
      showDiscountForm = false;
      showTaxDropdown = false;
      showChargeDropdown = false;
      showTaxForm = false;
      showChargeForm = false;
      // Set up note form state
      final note = ref.read(createAdvanceInvoiceProvider).note;
      _noteController.text = note ?? '';
    });
  }

  void _prepareTaxForm(TaxRate tax) {
    setState(() {
      _taxNameController.text = tax.name;
      _taxRateController.text = tax.rate != null ? tax.rate.toString() : '';
      showTaxForm = true;
      showTaxDropdown = false;
      showChargeForm = false;
      showChargeDropdown = false;
    });
  }

  void _prepareChargeForm(AdditionalCharge charge) {
    setState(() {
      _chargeNameController.text = charge.name;
      _chargeAmountController.text =
          charge.amount != null ? charge.amount.toString() : '';
      showChargeForm = true;
      showChargeDropdown = false;
      showTaxForm = false;
      showTaxDropdown = false;
    });
  }

  void _saveTax() {
    final name = _taxNameController.text.trim();
    final rate =
        double.tryParse(_taxRateController.text.replaceAll(',', '').trim()) ??
            0;

    if (name.isNotEmpty && _isValidTaxRate(_taxRateController.text)) {
      if (editingTaxId != null) {
        // Update existing tax
        final updatedTax = TaxRate(
          id: editingTaxId!,
          name: name,
          rate: rate,
          amount: 0,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateTax(updatedTax);
        setState(() {
          final index = selectedTaxes.indexWhere((t) => t.id == editingTaxId);
          if (index != -1) {
            selectedTaxes[index] = updatedTax;
          }
          editingTaxId = null;
          showTaxForm = false;
          showAddOptions = false;
          _taxNameController.clear();
          _taxRateController.clear();
        });
      } else {
        // Add new tax
        final newTax = TaxRate(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          rate: rate,
          amount: 0,
        );
        ref.read(advanceInvoiceControllerProvider.notifier).addTax(newTax);
        setState(() {
          selectedTaxes.add(newTax);
          showTaxForm = false;
          showAddOptions = false;
          _taxNameController.clear();
          _taxRateController.clear();
        });

        // add tax to the backend in the background
        createTask(newTax);
      }
    }
  }

  Future<void> createTask(TaxRate newTax) async {
    final res = await ref.read(createTaxRateUseCaseProvider(newTax));
    res.when(
      success: (_) {},
      failure: (err, _) {},
    );
  }

  void _saveCharge() {
    final name = _chargeNameController.text.trim();
    final amount = double.tryParse(
            _chargeAmountController.text.replaceAll(',', '').trim()) ??
        0;

    if (name.isNotEmpty && _isValidAmount(_chargeAmountController.text)) {
      if (editingChargeId != null) {
        // Update existing charge
        final updatedCharge = AdditionalCharge(
          id: editingChargeId!,
          name: name,
          amount: amount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateCharge(updatedCharge);
        setState(() {
          final index =
              selectedCharges.indexWhere((c) => c.id == editingChargeId);
          if (index != -1) {
            selectedCharges[index] = updatedCharge;
          }
          editingChargeId = null;
          showChargeForm = false;
          showAddOptions = false;
          _chargeNameController.clear();
          _chargeAmountController.clear();
        });
      } else {
        // Add new charge
        final newCharge = AdditionalCharge(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          amount: amount,
        );
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addCharge(newCharge);
        setState(() {
          selectedCharges.add(newCharge);
          showChargeForm = false;
          showAddOptions = false;
          _chargeNameController.clear();
          _chargeAmountController.clear();
        });

        // add charge to the backend in the background
        createCharge(newCharge);
      }
    }
  }

  Future<void> createCharge(AdditionalCharge newCharge) async {
    final res =
        await ref.read(createAdditionalChargeUseCaseProvider(newCharge));
    res.when(
      success: (_) {},
      failure: (err, _) {},
    );
  }

  void _cancelEdit() {
    setState(() {
      editingTaxId = null;
      editingChargeId = null;
      showTaxForm = false;
      showChargeForm = false;
      showAddOptions = false;
      _taxNameController.clear();
      _taxRateController.clear();
      _chargeNameController.clear();
      _chargeAmountController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    final settings =
        ref.watch(advanceInvoiceControllerProvider).settings.asData?.value;

    final textTheme = context.textTheme;
    final currencyCode = ref.read(currencyCodeProvider);

    // Filter out already selected taxes and charges
    final availableTaxes = (settings?.taxes ?? [])
        .where((tax) => !selectedTaxes.any((t) => t.name == tax.name))
        .toList();
    final availableCharges = (settings?.charges ?? [])
        .where((charge) => !selectedCharges.any((c) => c.name == charge.name))
        .toList();

    // Ensure persistent controllers for each tax/charge
    for (final tax in selectedTaxes) {
      _taxControllers.putIfAbsent(
          tax.id,
          () => TextEditingController(
              text: tax.rate != null ? tax.rate.toString() : ''));
    }
    for (final charge in selectedCharges) {
      _chargeControllers.putIfAbsent(
          charge.id,
          () => TextEditingController(
              text: charge.amount != null ? charge.amount.toString() : ''));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Options',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18,
            color: Palette.primaryBlack,
          ),
        ),
        const Gap(16),
        ToggleSection(
          title: 'Add Service Option',
          initiallyExpanded: showAddOptions,
          onExpansionChanged: (expanded) {
            setState(() {
              showAddOptions = expanded;
              if (!showAddOptions) {
                showTaxForm = false;
                showChargeForm = false;
                showTaxDropdown = false;
                showChargeDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
              }
            });
          },
          content: _buildServiceOptionsContent(context, textTheme, currencyCode,
              availableTaxes, availableCharges),
        ),
        const Gap(16),
        // Display selected taxes and charges
        _buildSelectedItems(context, textTheme, currencyCode),
      ],
    );
  }

  Widget _buildServiceOptionsContent(
    BuildContext context,
    TextTheme textTheme,
    String currencyCode,
    List<TaxRate> availableTaxes,
    List<AdditionalCharge> availableCharges,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Service option buttons
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildServiceOptionButton(
              'Add Tax',
              'Add a tax to your invoice. Set the rate as a percentage (0 - 100%).',
              () => setState(() {
                showTaxDropdown = !showTaxDropdown;
                showChargeDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
                showTaxForm = false;
                showChargeForm = false;
              }),
            ),
            _buildServiceOptionButton(
              'Add Charge',
              'Add a charge to your invoice eg Shipping fee, Processing fee, etc.',
              () => setState(() {
                showChargeDropdown = !showChargeDropdown;
                showTaxDropdown = false;
                showDiscountForm = false;
                showNoteInput = false;
                showTaxForm = false;
                showChargeForm = false;
              }),
            ),
            _buildServiceOptionButton(
              'Add Discount',
              'Add a discount to your invoice. Choose fixed or percentage.',
              _startEditingDiscount,
            ),
            _buildServiceOptionButton(
              'Add Note',
              'Add a note to your invoice.',
              _startEditingNote,
            ),
          ],
        ),
        const Gap(16),
        // Tax selection
        if (showTaxDropdown) _buildTaxSelection(availableTaxes),
        // Charge selection
        if (showChargeDropdown) _buildChargeSelection(availableCharges),
        // Forms
        if (showTaxForm) _buildTaxForm(textTheme),
        if (showChargeForm) _buildChargeForm(textTheme, currencyCode),
        if (showDiscountForm) _buildDiscountForm(textTheme, currencyCode),
        if (showNoteInput) _buildNoteForm(textTheme),
      ],
    );
  }

  Widget _buildServiceOptionButton(String title, String tooltip, VoidCallback onPressed) {
    return Tooltip(
      message: tooltip,
      child: OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          minimumSize: const Size(0, 40),
          side: BorderSide(color: Palette.stroke),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onPressed: onPressed,
        icon: Icon(Icons.add, size: 16, color: Palette.primary),
        label: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Palette.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildTaxSelection(List<TaxRate> availableTaxes) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.kF7F7F7,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Tax',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...availableTaxes.map((tax) => ActionChip(
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Palette.stroke),
                    label: Text(
                      tax.name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => _prepareTaxForm(tax),
                  )),
              ActionChip(
                backgroundColor: Palette.primary.withValues(alpha: 0.1),
                side: BorderSide(color: Palette.primary),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 16, color: Palette.primary),
                    const Gap(4),
                    Text(
                      'Add new tax',
                      style: TextStyle(
                        fontSize: 13,
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onPressed: () {
                  setState(() {
                    showTaxForm = true;
                    showTaxDropdown = false;
                    _taxNameController.clear();
                    _taxRateController.clear();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChargeSelection(List<AdditionalCharge> availableCharges) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Palette.kF7F7F7,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Charge',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Palette.primaryBlack,
            ),
          ),
          const Gap(12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...availableCharges.map((charge) => ActionChip(
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Palette.stroke),
                    label: Text(
                      charge.name,
                      style: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => _prepareChargeForm(charge),
                  )),
              ActionChip(
                backgroundColor: Palette.primary.withValues(alpha: 0.1),
                side: BorderSide(color: Palette.primary),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 16, color: Palette.primary),
                    const Gap(4),
                    Text(
                      'Add new charge',
                      style: TextStyle(
                        fontSize: 13,
                        color: Palette.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                onPressed: () {
                  setState(() {
                    showChargeForm = true;
                    showChargeDropdown = false;
                    _chargeNameController.clear();
                    _chargeAmountController.clear();
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
        // Add Tax Form
        if (showTaxForm)
          Padding(
            padding: const EdgeInsets.only(top: 6.0, left: 2.0),
            child: Row(
              children: [
                Expanded(
                  child: SmallInput(
                    controller: _taxNameController,
                    hint: 'Tax Name',
                    autofocus: _taxNameController.text.isEmpty,
                  ),
                ),
                const Gap(6),
                Expanded(
                  child: SmallInput(
                    controller: _taxRateController,
                    hint: 'Rate (0 - 100%)',
                    suffix: '%',
                    keyboardType: TextInputType.number,
                    inputFormatters: [Validators.formatRange(0, 100)],
                    autofocus: _taxNameController.text.isNotEmpty,
                    onEditingComplete: _saveTax,
                  ),
                ),
                const Gap(6),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  ),
                  onPressed: _saveTax,
                  child: Text(editingTaxId != null ? 'Update' : 'Save'),
                ),
                const Gap(4),
                TextButton(
                  style: TextButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  onPressed: _cancelEdit,
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),
        // Add Charge Form
        if (showChargeForm)
          Padding(
            padding: const EdgeInsets.only(top: 6.0, left: 2.0),
            child: Row(
              children: [
                Expanded(
                  child: SmallInput(
                    controller: _chargeNameController,
                    hint: 'Charge Name',
                    autofocus: _chargeNameController.text.isEmpty,
                  ),
                ),
                const Gap(6),
                Expanded(
                  child: SmallInput(
                    controller: _chargeAmountController,
                    hint: 'Amount',
                    prefix: '${CurrencyWidget.symbol(context, currencyCode)} ',
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      Validators.formatCurrency(decimalPlaces: 2)
                    ],
                    autofocus: _chargeNameController.text.isNotEmpty,
                    onEditingComplete: _saveCharge,
                  ),
                ),
                const Gap(6),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  ),
                  onPressed: _saveCharge,
                  child: Text(editingChargeId != null ? 'Update' : 'Save'),
                ),
                const Gap(4),
                TextButton(
                  style: TextButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  onPressed: _cancelEdit,
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),
        // Add Discount Form
        if (showDiscountForm)
          Padding(
            padding: const EdgeInsets.only(top: 6.0, left: 2.0),
            child: Row(
              children: [
                SizedBox(
                  width: 160,
                  height: 32,
                  child: InputDecorator(
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: const BorderSide(color: Colors.grey),
                      ),
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<DiscountType>(
                        value: selectedType,
                        isExpanded: true,
                        enableFeedback: false,
                        focusColor: Colors.transparent,
                        elevation: 0,
                        items: DiscountType.values
                            .map((type) => DropdownMenuItem(
                                  value: type,
                                  child: Text(
                                    type == DiscountType.fixed
                                        ? 'Fixed'
                                        : 'Percentage',
                                    style: textTheme.bodyMedium,
                                  ),
                                ))
                            .toList(),
                        onChanged: (type) {
                          if (type != null) {
                            setState(() {
                              selectedType = type;
                              _discountValueController.clear();
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ),
                const Gap(6),
                Expanded(
                  child: SmallInput(
                    controller: _discountValueController,
                    hint: selectedType == DiscountType.fixed
                        ? 'Amount'
                        : 'Value (0-100%)',
                    prefix: selectedType == DiscountType.fixed
                        ? '${CurrencyWidget.symbol(context, currencyCode)} '
                        : null,
                    suffix:
                        selectedType == DiscountType.percentage ? '%' : null,
                    keyboardType: TextInputType.number,
                    inputFormatters: selectedType == DiscountType.percentage
                        ? [Validators.formatRange(0, 100)]
                        : [Validators.formatCurrency(decimalPlaces: 2)],
                    onEditingComplete: _saveDiscount,
                  ),
                ),
                const Gap(6),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  ),
                  onPressed: _saveDiscount,
                  child: Text(editingDiscount ? 'Update' : 'Save'),
                ),
                const Gap(4),
                TextButton(
                  style: TextButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  onPressed: () {
                    setState(() {
                      showDiscountForm = false;
                      editingDiscount = false;
                      _discountValueController.clear();
                    });
                  },
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),
        // Add Note Form
        if (showNoteInput)
          Padding(
            padding: const EdgeInsets.only(top: 6.0, left: 2.0),
            child: Row(
              children: [
                Expanded(
                  child: SmallInput(
                    controller: _noteController,
                    hint: 'Enter note...',
                    autofocus: true,
                    maxLines: 3,
                    minLines: 3,
                  ),
                ),
                const Gap(6),
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  ),
                  onPressed: _saveNote,
                  child: Text(
                      ref.read(createAdvanceInvoiceProvider).note != null
                          ? 'Update'
                          : 'Save'),
                ),
                const Gap(4),
                TextButton(
                  style: TextButton.styleFrom(
                    minimumSize: const Size(0, 28),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  onPressed: () {
                    setState(() {
                      showNoteInput = false;
                      _noteController.clear();
                    });
                  },
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),
        const Gap(12),
        // Selected Taxes List
        if (selectedTaxes.isEmpty &&
            selectedCharges.isEmpty &&
            _noteController.text.isEmpty)
          Center(
            child: Text(
              'No service options added yet',
              style: textTheme.bodyMedium?.copyWith(color: Colors.grey),
            ),
          )
        else ...[
          ...selectedTaxes.map((tax) {
            return ListTile(
              dense: true,
              title: Text(tax.name),
              subtitle: Text('Rate: ${tax.rate}%', style: textTheme.bodySmall),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Consumer(
                    builder: (context, ref, _) {
                      ref.watch(createAdvanceInvoiceProvider);
                      return Text(
                          CurrencyWidget.value(
                              context, currencyCode, _calculateTaxAmount(tax)),
                          style: textTheme.bodyMedium);
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit, size: 18),
                    onPressed: () => _startEditingTax(tax),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 18),
                    onPressed: () {
                      ref
                          .read(advanceInvoiceControllerProvider.notifier)
                          .removeTax(tax);
                      setState(() {
                        selectedTaxes.remove(tax);
                        _taxControllers.remove(tax.id);
                      });
                    },
                  ),
                ],
              ),
            );
          }),
          // Selected Charges List
          ...selectedCharges.map((charge) {
            return ListTile(
              dense: true,
              title: Text(charge.name),
              subtitle: Text(
                  'Amount: ${CurrencyWidget.value(context, currencyCode, charge.amount ?? 0)}',
                  style: textTheme.bodySmall),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                      CurrencyWidget.value(
                          context, currencyCode, charge.amount ?? 0),
                      style: textTheme.bodyMedium),
                  IconButton(
                    icon: const Icon(Icons.edit, size: 18),
                    onPressed: () => _startEditingCharge(charge),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 18),
                    onPressed: () {
                      ref
                          .read(advanceInvoiceControllerProvider.notifier)
                          .removeCharge(charge);
                      setState(() {
                        selectedCharges.remove(charge);
                        _chargeControllers.remove(charge.id);
                      });
                    },
                  ),
                ],
              ),
            );
          }),
          // Selected Discount List
          Consumer(
            builder: (context, ref, _) {
              final params = ref.watch(createAdvanceInvoiceProvider);
              final discount = params.discount;

              List<Widget> widgets = [];

              if (params.discount != null) {
                widgets.add(
                  ListTile(
                    dense: true,
                    title: const Text('Discount'),
                    subtitle: Text('Type: ${discount?.type.name}',
                        style: textTheme.bodySmall),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                            discount!.type == DiscountType.fixed
                                ? CurrencyWidget.value(
                                    context, currencyCode, discount.value)
                                : '${discount.value}%',
                            style: textTheme.bodyMedium),
                        IconButton(
                          icon: const Icon(Icons.edit, size: 18),
                          onPressed: _startEditingDiscount,
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, size: 18),
                          onPressed: () {
                            ref
                                .read(advanceInvoiceControllerProvider.notifier)
                                .removeDiscount();
                          },
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (params.note != null && params.note!.isNotEmpty) {
                widgets.add(
                  ListTile(
                    dense: true,
                    title: const Text('Note'),
                    subtitle: Text(params.note!),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, size: 18),
                          onPressed: _startEditingNote,
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, size: 18),
                          onPressed: () {
                            ref
                                .read(advanceInvoiceControllerProvider.notifier)
                                .removeNote();
                          },
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              }

              return widgets.isEmpty
                  ? const SizedBox.shrink()
                  : Column(children: widgets);
            },
          ),
        ],
      ],
    )
  }

  void _saveDiscount() {
    final value = num.tryParse(
            _discountValueController.text.replaceAll(',', '').trim()) ??
        0;
    if (value > 0 ||
        (selectedType == DiscountType.percentage &&
            value >= 0 &&
            value <= 100)) {
      final newDiscount = Discount(type: selectedType, value: value);
      if (editingDiscount) {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .updateDiscount(newDiscount);
      } else {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .addDiscount(newDiscount);
      }
      setState(() {
        showDiscountForm = false;
        editingDiscount = false;
        _discountValueController.clear();
      });
    }
  }

  void _saveNote() {
    final value = _noteController.text.trim();
    if (value.isNotEmpty) {
      final note = ref.read(createAdvanceInvoiceProvider).note;
      if (note != null) {
        ref.read(advanceInvoiceControllerProvider.notifier).updateNote(value);
      } else {
        ref.read(advanceInvoiceControllerProvider.notifier).addNote(value);
      }
      setState(() {
        showNoteInput = false;
        _noteController.clear();
      });
    }
  }

  Widget _infoIcon(String tooltip) {
    return Tooltip(
      message: tooltip,
      child: const Icon(Icons.info_outline, size: 16),
    );
  }
}
