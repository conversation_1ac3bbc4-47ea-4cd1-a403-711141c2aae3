import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_controller.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/order/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/app/order/widgets/sliver_delegate.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/components/widgets/streamed_image.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class AdvanceInvoicePreviewWidget extends ConsumerWidget {
  const AdvanceInvoicePreviewWidget({super.key, required this.randomNumber});

  final String randomNumber;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = context.textTheme;
    final settings = ref.watch(advanceInvoiceControllerProvider).settings;
    final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;
    final selectedCustomer = ref.watch(selectedCustomerProvider);
    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final lineItems = createParams.lineItems;
    final taxes = createParams.taxes ?? [];
    final charges = createParams.charges ?? [];
    final billTo = selectedCustomer;
    final shipTo = selectedCustomer;
    final invoiceNumber = 'IVN-$randomNumber';
    final dueDate = DateTime.now().toDate();
    final subtotal =
        lineItems.fold<double>(0, (sum, item) => sum + (item.total));
    final subTotalWithTaxAndDiscount = lineItems.fold<double>(
        0, (sum, item) => sum + (item.totalWithTaxAndDiscount));
    final discount = createParams.discount;
    final note = createParams.note;
    final bankAccount = createParams.bankAccount;

    // Calculate taxes
    final taxAmount = taxes.fold<double>(0, (sum, tax) {
      final taxRate = tax.rate ?? 0;
      return sum + (subtotal * (taxRate / 100));
    });

    // Calculate charges
    final chargeAmount = charges.fold<double>(0, (sum, charge) {
      return sum + (charge.amount ?? 0);
    });

    // Calculate discount amount
    double discountAmount = 0;
    if (discount != null) {
      if (discount.type == DiscountType.fixed) {
        discountAmount = discount.value.toDouble();
      } else if (discount.type == DiscountType.percentage) {
        discountAmount = subtotal * (discount.value.toDouble() / 100);
      }
    }

    final total =
        subTotalWithTaxAndDiscount + taxAmount + chargeAmount - discountAmount;

    final currencyCode = ref.read(currencyCodeProvider);

    return Container(
      color: Palette.kFCFCFC,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 40),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8)),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 10)],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 40.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header row: Invoice, logo
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left: Invoice title and meta
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Invoice',
                            style: context.textTheme.headlineMedium
                                ?.copyWith(fontWeight: FontWeight.bold)),
                        const Gap(24),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Invoice number',
                                      style: context.textTheme.bodySmall
                                          ?.copyWith(color: Colors.black54)),
                                  Text(invoiceNumber,
                                      style: context.textTheme.bodyMedium),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Date due',
                                      style: context.textTheme.bodySmall
                                          ?.copyWith(color: Colors.black54)),
                                  Text(dueDate,
                                      style: context.textTheme.bodyMedium),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Right: Logo
                  Container(
                    width: 64,
                    height: 64,
                    margin: const EdgeInsets.only(left: 24),
                    child: settings.when(
                      data: (data) {
                        if (data == null || (data.logoUrl?.isEmpty ?? true)) {
                          return InkWell(
                            onTap: () => showDialog(
                              context: context,
                              builder: (context) => const LogoUploadDialog(),
                            ),
                            child: Material(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Palette.kE7E7E7.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(Icons.add_photo_alternate,
                                    size: 40, color: Palette.placeholder),
                              ),
                            ),
                          );
                        }
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child:
                              StreamedImageWidget(imageUrl: data.logoUrl ?? ''),
                        );
                      },
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stackTrace) => Container(
                        decoration: BoxDecoration(
                          color: Palette.kE7E7E7.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.add_photo_alternate,
                            size: 40, color: Palette.kE7E7E7),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(32),
              // Company, Bill to, Ship to
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Company info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(outlet?.outletBusinessName ?? '-',
                            style: context.textTheme.bodyLarge
                                ?.copyWith(fontWeight: FontWeight.bold)),
                        Text(outlet?.formattedAddress ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(outlet?.country ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(outlet?.contactPhone ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(outlet?.email ?? '-'),
                      ],
                    ),
                  ),
                  const Gap(8),
                  // Bill to
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Bill to',
                            style: context.textTheme.bodySmall
                                ?.copyWith(color: Colors.black54)),
                        Text(billTo?.outletBusinessName ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(billTo?.formattedAddress ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(billTo?.country ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(billTo?.contactPhone ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(billTo?.email ?? '-'),
                      ],
                    ),
                  ),
                  // Ship to
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Ship to',
                            style: context.textTheme.bodySmall
                                ?.copyWith(color: Colors.black54)),
                        Text(shipTo?.outletBusinessName ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(shipTo?.formattedAddress ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(shipTo?.country ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(billTo?.phoneNumber ?? '-',
                            style: context.textTheme.bodyMedium),
                        Text(billTo?.email ?? '-'),
                      ],
                    ),
                  ),
                ],
              ),
              const Gap(32),
              // Amount due and pay online
              Text(
                  '${CurrencyWidget.value(context, currencyCode, total)} due $dueDate',
                  style: context.textTheme.titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold)),
              const Gap(8),
              // GestureDetector(
              //   onTap: () {},
              //   child: Text('Pay online',
              //       style: context.textTheme.bodyMedium?.copyWith(
              //           color: Colors.blue,
              //           decoration: TextDecoration.underline)),
              // ),
              const Gap(32),
              // Items table
              _buildPreviewItemsTable(context, lineItems, currencyCode),
              const Gap(32),
              // Subtotal, shipping, total, amount due
              Row(
                children: [
                  Expanded(child: Container()),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildSummaryRow(context, 'Subtotal',
                            subTotalWithTaxAndDiscount, currencyCode),
                        if (taxes.isNotEmpty) ...[
                          ...taxes.map((tax) => _buildSummaryRow(
                              context,
                              '${tax.name} (${tax.rate}%)',
                              (subtotal.toDouble() *
                                      ((tax.rate?.toDouble() ?? 0.0) / 100.0))
                                  .toDouble(),
                              currencyCode)),
                        ],
                        if (charges.isNotEmpty) ...[
                          ...charges.map((charge) => _buildSummaryRow(
                              context,
                              charge.name,
                              charge.amount?.toDouble() ?? 0,
                              currencyCode)),
                        ],
                        if (discountAmount > 0)
                          _buildSummaryRow(context, 'Discount', discountAmount,
                              currencyCode),
                        _buildSummaryRow(context, 'Total', total, currencyCode),
                        const Gap(8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Amount Due',
                                style: context.textTheme.bodyLarge),
                            const Gap(8),
                            Text(
                                CurrencyWidget.value(
                                    context, currencyCode, total),
                                style: context.textTheme.titleLarge
                                    ?.copyWith(fontWeight: FontWeight.bold)),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Note
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: note != null && note.isNotEmpty
                    ? Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          key: const ValueKey('note'),
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(32),
                            Text(
                              note,
                              style: context.textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w500),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: bankAccount != null
                    ? Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          key: const ValueKey('bank-details'),
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(32),
                            Text(
                                'Account Name: ${bankAccount.accountName != null ? toTitleCase(bankAccount.accountName) : '-'}',
                                style: textTheme.bodyMedium),
                            Text(
                                'Account Number: ${bankAccount.accountNumber ?? '-'}',
                                style: textTheme.bodyMedium),
                            Text('Bank Name: ${bankAccount.bankName}',
                                style: textTheme.bodyMedium),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              const Gap(32),
              // Footer summary
              Divider(
                color: Palette.kE7E7E7,
                height: 0,
                // indent: 0,
              ),
              const Gap(4),
              Text('$invoiceNumber · due $dueDate',
                  style: context.textTheme.bodySmall
                      ?.copyWith(color: Colors.black54)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewItemsTable(
      BuildContext context, List<LineItemParam> lineItems, String currency) {
    final textTheme = context.textTheme;
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Table(
                columnWidths: const {
                  0: FlexColumnWidth(2.5), // Name
                  1: FlexColumnWidth(2), // Qty
                  2: FlexColumnWidth(2), // Unit price
                  3: FlexColumnWidth(2), // Tax rate
                  4: FlexColumnWidth(2), // Discount
                  5: FlexColumnWidth(2.5), // Amount
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: [
                  TableRow(
                    decoration: const BoxDecoration(color: Color(0xFFF5F5F5)),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Name',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Qty',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text(
                            'Unit\nPrice (${CurrencyWidget.symbol(context, currency)})',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Tax',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Discount',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text(
                            'Total (${CurrencyWidget.symbol(context, currency)})',
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                            textAlign: TextAlign.right),
                      ),
                    ],
                  ),
                  // if (lineItems.isEmpty)
                  //   TableRow(children: [
                  //     Padding(
                  //       padding: const EdgeInsets.all(0),
                  //       child: Text('No items added yet',
                  //           style: textTheme.bodyMedium
                  //               ?.copyWith(color: Palette.placeholder),
                  //           textAlign: TextAlign.left),
                  //     ),
                  //     const SizedBox(),
                  //     const SizedBox(),
                  //     const SizedBox(),
                  //     const SizedBox(),
                  //     const SizedBox(),
                  //   ])
                  // else
                  ...lineItems.map((item) {
                    return TableRow(children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 12),
                        child: Text(item.name,
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 12),
                        child: Text(item.quantity.formattedAmount(context),
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 12),
                        child: Text(item.unitPrice.formattedAmount(context),
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 12),
                        child: Text('${item.taxRate}%',
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 12),
                        child: Text(
                            item.discount != null
                                ? item.discount!.type == DiscountType.fixed
                                    ? CurrencyWidget.value(
                                        context, currency, item.discount!.value)
                                    : '${item.discount!.value}%'
                                : '-',
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 12),
                        child: Text(
                            item.totalWithTaxAndDiscount
                                .formattedAmount(context),
                            style: textTheme.bodyMedium,
                            textAlign: TextAlign.right),
                      ),
                    ]);
                  }),
                ],
              ),
            ),
          ],
        ),
        if (lineItems.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 6.0),
            child: Text('No items added yet',
                style:
                    textTheme.bodyMedium?.copyWith(color: Palette.placeholder),
                textAlign: TextAlign.left),
          ),
      ],
    );
  }

  Widget _buildSummaryRow(
      BuildContext context, String label, double value, String currencyCode) {
    final textTheme = context.textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: textTheme.bodyMedium?.copyWith(color: Colors.black54),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 12), // Space between label and value
          Text(
            CurrencyWidget.value(context, currencyCode, value),
            style: textTheme.bodyMedium,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }
}

class LogoUploadDialog extends ConsumerStatefulWidget {
  const LogoUploadDialog({super.key});

  @override
  ConsumerState<LogoUploadDialog> createState() => _LogoUploadDialogState();
}

class _LogoUploadDialogState extends ConsumerState<LogoUploadDialog> {
  String? _selectedImage;
  final _isLoading = ValueNotifier<bool>(false);
  final _disableImageUploadButton = ValueNotifier<bool>(true);

  @override
  void dispose() {
    _isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;

    return CustomScrollView(
      slivers: [
        SliverPersistentHeader(
          pinned: true,
          delegate: SliverHeaderDelegate(
            minHeight: 70,
            maxHeight: 70,
            child: Container(
              color: Colors.white,
              child: Row(
                children: [
                  IconButton(
                    icon: SvgPicture.asset(kCloseSvg),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  const Gap(10),
                  Text(
                    'Upload Logo',
                    style: textTheme.bodyLarge?.copyWith(
                      color: Palette.k6B797C,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Column(
              children: [
                const Gap(24),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: Palette.kE7E7E7,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildBreadcrumb(),
                        const Gap(24),
                        _buildLogoUploadCard(),
                      ],
                    ),
                  ),
                ),
                const Gap(200),
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _buildBreadcrumb() {
    return Text(
      'Advance invoice > Upload logo',
      style: textTheme.bodyMedium?.copyWith(
        color: HexColor('#6B797C'),
      ),
    );
  }

  Widget _buildLogoUploadCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Logo'),
        const Gap(16),
        Center(
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: Palette.stroke),
              borderRadius: BorderRadius.circular(8),
            ),
            child: _selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.memory(
                      base64Decode(_selectedImage!),
                      fit: BoxFit.contain,
                    ),
                  )
                : const Icon(Icons.add_photo_alternate, size: 64),
          ),
        ),
        const Gap(24),
        Center(
          child: CustomFilledButton(
            text: 'Choose Image',
            onPressed: _pickImage,
            loaderNotifier: _isLoading,
          ),
        ),
        const Gap(24),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            const Gap(16),
            MultiValueListenableBuilder<bool, bool, Null>(
              valueListenable1: _disableImageUploadButton,
              valueListenable2: _isLoading,
              builder: (context, disabled, loading, _, __) {
                return CustomFilledButton(
                  text: 'Upload',
                  disabledNotifier: _disableImageUploadButton,
                  loaderNotifier: _isLoading,
                  onPressed: () async {
                    _isLoading.value = true;
                    final res = await ref
                        .read(uploadLogoUseCaseProvider(_selectedImage!));
                    res.when(
                      success: (_) {
                        _isLoading.value = false;
                        Navigator.pop(context, _selectedImage!);
                      },
                      failure: (e, _) {
                        _isLoading.value = false;
                        Toast.apiError(e, context);
                      },
                    );
                  },
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: textTheme.headlineSmall,
    );
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          setState(() {
            _selectedImage = base64Encode(file.bytes!);
            _disableImageUploadButton.value = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        Toast.error("Error picking image: $e", context);
      }
    }
  }

  TextTheme get textTheme => context.textTheme;
}

// class TaxRateDialog extends ConsumerStatefulWidget {
//   const TaxRateDialog({super.key});

//   @override
//   ConsumerState<TaxRateDialog> createState() => _TaxRateDialogState();
// }

// class _TaxRateDialogState extends ConsumerState<TaxRateDialog> {
//   final _formKey = GlobalKey<FormState>();
//   final _validateMode = ValueNotifier(AutovalidateMode.disabled);
//   final _nameController = TextEditingController();
//   final _rateController = TextEditingController();
//   final _isLoading = ValueNotifier<bool>(false);

//   @override
//   void dispose() {
//     _nameController.dispose();
//     _rateController.dispose();
//     _validateMode.dispose();
//     _isLoading.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = context.textTheme;

//     return CustomScrollView(
//       slivers: [
//         SliverPersistentHeader(
//           pinned: true,
//           delegate: SliverHeaderDelegate(
//             minHeight: 70,
//             maxHeight: 70,
//             child: Container(
//               color: Colors.white,
//               child: Row(
//                 children: [
//                   IconButton(
//                     icon: SvgPicture.asset(kCloseSvg),
//                     onPressed: () => Navigator.of(context).pop(),
//                   ),
//                   const Gap(10),
//                   Text(
//                     'Add Tax Rate',
//                     style: textTheme.bodyLarge?.copyWith(
//                       color: Palette.k6B797C,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         SliverFillRemaining(
//           hasScrollBody: false,
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 40),
//             child: Column(
//               children: [
//                 const Gap(24),
//                 Container(
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     border: Border.all(
//                       color: Palette.kE7E7E7,
//                     ),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: Padding(
//                     padding: const EdgeInsets.all(16.0),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         _buildBreadcrumb(),
//                         const Gap(24),
//                         _buildFormCard(context),
//                       ],
//                     ),
//                   ),
//                 ),
//                 const Gap(200),
//               ],
//             ),
//           ),
//         )
//       ],
//     );
//   }

//   Widget _buildBreadcrumb() {
//     return Text(
//       'Advance invoice > Add tax rate',
//       style: textTheme.bodyMedium?.copyWith(
//         color: HexColor('#6B797C'),
//       ),
//     );
//   }

//   Widget _buildFormCard(BuildContext context) {
//     return ValueListenableBuilder<AutovalidateMode>(
//       valueListenable: _validateMode,
//       builder: (context, mode, _) {
//         return Form(
//           key: _formKey,
//           autovalidateMode: mode,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _buildSectionTitle('Tax details'),
//               const Gap(16),
//               _buildTaxDetailsFields(),
//               const Gap(24),
//               _buildAddTaxButton(),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildSectionTitle(String title) {
//     return Text(
//       title,
//       style: textTheme.headlineSmall,
//     );
//   }

//   Widget _buildTaxDetailsFields() {
//     return Column(
//       children: [
//         buildTextFormField(
//           label: 'Tax Name',
//           controller: _nameController,
//           validator: (input) => Validators.verifyInput(
//             input,
//             field: 'tax name',
//             length: 3,
//           ),
//         ),
//         const Gap(16),
//         buildTextFormField(
//           label: 'Tax Rate (%)',
//           controller: _rateController,
//           validator: (input) {
//             input = input?.replaceAll(',', '');
//             if (input == null || input.isEmpty) {
//               return 'Please enter a tax rate';
//             }
//             final rate = double.tryParse(input);
//             if (rate == null) {
//               return 'Please enter a valid number';
//             }
//             if (rate < 0 || rate > 100) {
//               return 'Tax rate must be between 0 and 100';
//             }
//             return null;
//           },
//           keyboardType: TextInputType.number,
//           inputFormatters: [Validators.validDecimalNumberInput()],
//         ),
//       ],
//     );
//   }

//   Widget _buildAddTaxButton() {
//     return SizedBox(
//       width: double.maxFinite,
//       child: CustomFilledButton(
//         text: 'Add Tax Rate',
//         loaderNotifier: _isLoading,
//         onPressed: _submitForm,
//       ),
//     );
//   }

//   void _submitForm() {
//     final FormState form = _formKey.currentState!;
//     if (form.validate()) {
//       form.save();
//       final taxRate = TaxRate(
//         id: '',
//         name: _nameController.text,
//         rate: double.parse(_rateController.text),
//         amount: 0, // This will be calculated on the server
//       );
//       Navigator.pop(context, taxRate);
//     } else {
//       _validateMode.value = AutovalidateMode.always;
//       Toast.error("Kindly attend to the error(s)", context);
//     }
//   }

//   double get defaultHeight => 55;
//   TextTheme get textTheme => context.textTheme;
// }

// class AdditionalChargeDialog extends ConsumerStatefulWidget {
//   const AdditionalChargeDialog({super.key});

//   @override
//   ConsumerState<AdditionalChargeDialog> createState() =>
//       _AdditionalChargeDialogState();
// }

// class _AdditionalChargeDialogState
//     extends ConsumerState<AdditionalChargeDialog> {
//   final _formKey = GlobalKey<FormState>();
//   final _validateMode = ValueNotifier(AutovalidateMode.disabled);
//   final _nameController = TextEditingController();
//   final _amountController = TextEditingController();
//   final _isLoading = ValueNotifier<bool>(false);

//   @override
//   void dispose() {
//     _nameController.dispose();
//     _amountController.dispose();
//     _validateMode.dispose();
//     _isLoading.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = context.textTheme;

//     return CustomScrollView(
//       slivers: [
//         SliverPersistentHeader(
//           pinned: true,
//           delegate: SliverHeaderDelegate(
//             minHeight: 70,
//             maxHeight: 70,
//             child: Container(
//               color: Colors.white,
//               child: Row(
//                 children: [
//                   IconButton(
//                     icon: SvgPicture.asset(kCloseSvg),
//                     onPressed: () => Navigator.of(context).pop(),
//                   ),
//                   const Gap(10),
//                   Text(
//                     'Add Additional Charge',
//                     style: textTheme.bodyLarge?.copyWith(
//                       color: Palette.k6B797C,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         SliverFillRemaining(
//           hasScrollBody: false,
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 40),
//             child: Column(
//               children: [
//                 const Gap(24),
//                 Container(
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     border: Border.all(
//                       color: Palette.kE7E7E7,
//                     ),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: Padding(
//                     padding: const EdgeInsets.all(16.0),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         _buildBreadcrumb(),
//                         const Gap(24),
//                         _buildFormCard(context),
//                       ],
//                     ),
//                   ),
//                 ),
//                 const Gap(200),
//               ],
//             ),
//           ),
//         )
//       ],
//     );
//   }

//   Widget _buildBreadcrumb() {
//     return Text(
//       'Advance invoice > Add additional charge',
//       style: textTheme.bodyMedium?.copyWith(
//         color: HexColor('#6B797C'),
//       ),
//     );
//   }

//   Widget _buildFormCard(BuildContext context) {
//     return ValueListenableBuilder<AutovalidateMode>(
//       valueListenable: _validateMode,
//       builder: (context, mode, _) {
//         return Form(
//           key: _formKey,
//           autovalidateMode: mode,
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _buildSectionTitle('Charge details'),
//               const Gap(16),
//               _buildChargeDetailsFields(),
//               const Gap(24),
//               _buildAddChargeButton(),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildSectionTitle(String title) {
//     return Text(
//       title,
//       style: textTheme.headlineSmall,
//     );
//   }

//   Widget _buildChargeDetailsFields() {
//     return Column(
//       children: [
//         buildTextFormField(
//           label: 'Charge Name',
//           controller: _nameController,
//           validator: (input) => Validators.verifyInput(
//             input,
//             field: 'charge name',
//             length: 3,
//           ),
//         ),
//         const Gap(16),
//         buildTextFormField(
//           label: 'Amount',
//           controller: _amountController,
//           validator: (input) {
//             input = input?.replaceAll(',', '');
//             if (input == null || input.isEmpty) {
//               return 'Please enter an amount';
//             }
//             if (double.tryParse(input) == null) {
//               return 'Please enter a valid number';
//             }
//             return null;
//           },
//           keyboardType: TextInputType.number,
//           inputFormatters: [Validators.validDecimalNumberInput()],
//         ),
//       ],
//     );
//   }

//   Widget _buildAddChargeButton() {
//     return SizedBox(
//       width: double.maxFinite,
//       child: CustomFilledButton(
//         text: 'Add Charge',
//         loaderNotifier: _isLoading,
//         onPressed: _submitForm,
//       ),
//     );
//   }

//   void _submitForm() {
//     final FormState form = _formKey.currentState!;
//     if (form.validate()) {
//       form.save();
//       final charge = AdditionalCharge(
//         id: '',
//         name: _nameController.text,
//         amount: double.parse(_amountController.text),
//       );
//       Navigator.pop(context, charge);
//     } else {
//       _validateMode.value = AutovalidateMode.always;
//       Toast.error("Kindly attend to the error(s)", context);
//     }
//   }

//   double get defaultHeight => 55;
//   TextTheme get textTheme => context.textTheme;
// }
