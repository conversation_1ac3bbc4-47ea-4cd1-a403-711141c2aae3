import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/advance_invoice/data/models/tax_rate.dart';
import 'package:td_procurement/app/order/widgets/sliver_delegate.dart';
import 'package:td_procurement/src/components/buttons/custom_filled_button.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/validators/validators.dart';

import 'custom_widgets.dart';

class TaxRateForm extends ConsumerStatefulWidget {
  final TaxRate? initialTax;

  const TaxRateForm({super.key, this.initialTax});

  @override
  ConsumerState<TaxRateForm> createState() => _TaxRateFormDialogState();
}

class _TaxRateFormDialogState extends ConsumerState<TaxRateForm> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _rateController;
  final _isLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(
        text: widget.initialTax?.name == null ? '' : widget.initialTax!.name);
    _rateController = TextEditingController(
        text: widget.initialTax?.rate == null
            ? ''
            : widget.initialTax!.rate.toString());
  }

  @override
  void dispose() {
    _nameController.dispose();
    _rateController.dispose();
    _isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverPersistentHeader(
            pinned: true,
            delegate: SliverHeaderDelegate(
              minHeight: 60,
              maxHeight: 60,
              child: Container(
                color: Colors.white,
                child: Row(
                  children: [
                    const Gap(18),
                    IconButton(
                      icon: SvgPicture.asset(kCloseSvg),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const Gap(10),
                    Text(
                      widget.initialTax == null
                          ? 'Add Tax Rate'
                          : 'Edit Tax Rate',
                      style: textTheme.bodyLarge?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40),
              child: Column(
                children: [
                  const Gap(24),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Palette.kE7E7E7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Tax Rate Details'),
                            const Gap(16),
                            buildTextFormField(
                              controller: _nameController,
                              label: 'Tax Name',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a tax name';
                                }
                                return null;
                              },
                            ),
                            const Gap(16),
                            buildTextFormField(
                              controller: _rateController,
                              label: 'Tax Rate (%)',
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                Validators.validDecimalNumberInput(),
                              ],
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter a tax rate';
                                }
                                final rate = double.tryParse(value);
                                if (rate == null) {
                                  return 'Please enter a valid number';
                                }
                                if (rate < 0 || rate > 100) {
                                  return 'Tax rate must be between 0 and 100';
                                }
                                return null;
                              },
                            ),
                            const Gap(24),
                            SizedBox(
                              width: double.maxFinite,
                              child: CustomFilledButton(
                                text:
                                    widget.initialTax == null ? 'Add' : 'Save',
                                loaderNotifier: _isLoading,
                                onPressed: _submitForm,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Gap(200),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final textTheme = context.textTheme;
    return Text(
      title,
      style: textTheme.headlineSmall,
    );
  }

  void _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      _isLoading.value = true;
      final taxRate = TaxRate(
        id: widget.initialTax?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        rate: double.parse(_rateController.text),
        amount: 0, // This will be calculated based on the line items
      );
      _isLoading.value = false;
      Navigator.pop(context, taxRate);
    }
  }
}
