import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/advance_invoice_params.dart';

@immutable
class AdvanceInvoiceState extends Equatable {
  final AsyncValue<List<RetailInvoice>> advanceInvoices;
  final FetchAdvanceInvoiceParams fetchAdvanceInvoiceParams;
  final AsyncValue<RetailInvoice?> advanceInvoiceDetails;
  final AsyncValue<AdvanceInvoiceSettings?> settings;
  final AsyncValue<LoanContract?> loanContract;
  final AsyncValue<List<WalletBank>> bankAccounts;
  final AsyncValue<List<SettlementBank>> settlementBanks;

  const AdvanceInvoiceState({
    required this.advanceInvoices,
    required this.fetchAdvanceInvoiceParams,
    this.advanceInvoiceDetails = const AsyncData(null),
    this.settings = const AsyncData(null),
    this.loanContract = const AsyncData(null),
    this.bankAccounts = const AsyncData([]),
    this.settlementBanks = const AsyncData([]),
  });

  factory AdvanceInvoiceState.initial() {
    return AdvanceInvoiceState(
      advanceInvoices: const AsyncData([]),
      fetchAdvanceInvoiceParams: FetchAdvanceInvoiceParams.defaultValue(),
      bankAccounts: const AsyncData([]),
      settlementBanks: const AsyncData([]),
    );
  }

  AdvanceInvoiceState copyWith({
    AsyncValue<List<RetailInvoice>>? advanceInvoices,
    FetchAdvanceInvoiceParams? fetchAdvanceInvoiceParams,
    AsyncValue<RetailInvoice?>? advanceInvoiceDetails,
    AsyncValue<AdvanceInvoiceSettings?>? settings,
    AsyncValue<LoanContract?>? loanContract,
    AsyncValue<List<WalletBank>>? bankAccounts,
    AsyncValue<List<SettlementBank>>? settlementBanks,
  }) {
    return AdvanceInvoiceState(
      advanceInvoices: advanceInvoices ?? this.advanceInvoices,
      fetchAdvanceInvoiceParams:
          fetchAdvanceInvoiceParams ?? this.fetchAdvanceInvoiceParams,
      advanceInvoiceDetails:
          advanceInvoiceDetails ?? this.advanceInvoiceDetails,
      settings: settings ?? this.settings,
      loanContract: loanContract ?? this.loanContract,
      bankAccounts: bankAccounts ?? this.bankAccounts,
      settlementBanks: settlementBanks ?? this.settlementBanks,
    );
  }

  @override
  List<Object?> get props => [
        advanceInvoices,
        fetchAdvanceInvoiceParams,
        advanceInvoiceDetails,
        settings,
        loanContract,
        bankAccounts,
        settlementBanks,
      ];
}
