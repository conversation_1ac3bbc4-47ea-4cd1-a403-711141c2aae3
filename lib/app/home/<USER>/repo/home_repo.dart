import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/home/<USER>/data_source/home_data_source.dart';
import 'package:td_procurement/app/home/<USER>/models/procurement_stats_data.dart';
import 'package:td_procurement/app/home/<USER>/params/stat_params.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

abstract class HomeRepo {
  Future<ApiResponse<ProcurementStatsData>> getStats(StatParams params);
}

final homeRepoProvider = Provider<HomeRepo>((ref) {
  return HomeRepoImplementation(ref);
});

class HomeRepoImplementation extends HomeRepo {
  final Ref _ref;

  HomeRepoImplementation(this._ref);

  late final HomeDataSource _dataSource = _ref.read(homeDataProvider);

  @override
  Future<ApiResponse<ProcurementStatsData>> getStats(StatParams params) {
    return dioInterceptor(() => _dataSource.getStats(params), _ref);
  }
}
