// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
// import 'package:td_procurement/app/business_verification/presentation/logic/states/business_verification_state.dart';
// import 'package:td_procurement/core/router/routes.dart';

// class HomeScreen extends ConsumerWidget {
//   const HomeScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;

//     bool step1Complete = outlet?.company?.kyb?.businessType != null &&
//         outlet?.company?.kyb?.rcNumber != null;
//     bool step2Complete = outlet?.company?.kyb?.bvn != null &&
//         outlet?.walletAccount?.accountNumber != null &&
//         outlet?.walletAccount?.bankName != null;
//     bool? step4Complete = outlet?.kyc?.idVerified;

//     bool verificationDone = outlet?.country?.toLowerCase() == "ng"
//         ? step1Complete == true && step2Complete == true
//         : step1Complete == true && step4Complete == true;

//     return Scaffold(
//       body: !verificationDone
//           ? FilledButton(
//               onPressed: () {
//                 int nextStep = 0;
//                 if (!step1Complete) {
//                   nextStep = 0;
//                 } else if (step1Complete && step2Complete) {
//                   if (outlet?.country!.toLowerCase() != "ng") {
//                     nextStep = 3;
//                   } else {
//                     nextStep = 2;
//                   }
//                 } else if (step1Complete) {
//                   if (outlet?.country!.toLowerCase() != "ng") {
//                     nextStep = 3;
//                   } else {
//                     nextStep = 1;
//                   }
//                 } else if (step4Complete == true) {}

//                 ref
//                     .read(businessVerificationProvider.notifier)
//                     .updateNavigationState(nextStep);

//                 context.pushNamed(kBusinessVerificationRoute);
//               },
//               child: const Text('Add Company Information'))
//           : const SizedBox.shrink(),
//     );
//   }
// }
