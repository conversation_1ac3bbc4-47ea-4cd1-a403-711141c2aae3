import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/presentation/views/widgets/invoice_status_chip.dart';
import 'package:td_procurement/app/invoices/presentation/views/widgets/invoices_table.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class HomeInvoice extends StatelessWidget {
  const HomeInvoice(this.invoiceList, {super.key});

  final List<Invoice> invoiceList;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final currentInvoiceList =
        invoiceList.length > 5 ? invoiceList.sublist(0, 5) : invoiceList;
    return LayoutBuilder(builder: (context, constraints) {
      return Theme(
        data: Theme.of(context).copyWith(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Palette.kE7E7E7,
          ),
        ),
        child: DataTable(
          showCheckboxColumn: false,
          headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
          columns: [
            DataColumn(
              label: _buildHeaderCell('Amount', textTheme),
            ),
            DataColumn(
              label: _buildHeaderCell('Invoice Number', textTheme),
            ),
            DataColumn(
              label: _buildHeaderCell('Bill To', textTheme),
            ),
            DataColumn(
              label: _buildHeaderCell('Created on', textTheme),
            ),
            DataColumn(
              label: _buildHeaderCell('Due by', textTheme),
            ),
            DataColumn(
              label: ConstrainedBox(
                constraints: BoxConstraints(
                    maxWidth: constraints.maxWidth > kInvoiceTableMinSize
                        ? double.infinity
                        : 50),
                child: const SizedBox.shrink(),
              ),
            )
          ],
          rows: currentInvoiceList
              .map(
                (element) => DataRow(
                  onSelectChanged: (isSelected) {
                    if (isSelected ?? false) {
                      InvoiceOption.download.action(
                        context,
                        element,
                        InvoiceType.purchase,
                        element.orders?.first.id ?? '',
                      );
                    }
                  },
                  cells: [
                    DataCell(
                      _buildContentAmount(
                          element.total, textTheme, element.currency.iso),
                    ),
                    DataCell(
                      _buildContentText(
                          element.invoiceNumber.toString(), textTheme),
                    ),
                    DataCell(
                      _buildContentText(element.outletBusinessName, textTheme),
                    ),
                    DataCell(
                      _buildContentText(element.createdAt.toDate(), textTheme),
                    ),
                    DataCell(
                      _buildContentBadge(
                          element.dueAt.toDate(), element.status, textTheme),
                    ),
                    /*   DataCell(
                      (hoverState == element.invoiceNumber
                          ? child!
                          : const SizedBox.shrink()),
                    )*/
                    DataCell(
                      constraints.maxWidth > kInvoiceTableMinSize
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                FilledButton.tonal(
                                  onPressed: () =>
                                      InvoiceOption.download.action(
                                    context,
                                    element,
                                    InvoiceType.purchase,
                                    element.orders?.first.id ?? '',
                                    true,
                                  ),
                                  style: FilledButton.styleFrom(
                                      minimumSize: const Size(50, 40),
                                      fixedSize: const Size(140, 35),
                                      textStyle: textTheme.bodyMedium?.copyWith(
                                          fontWeight: FontWeight.w500),
                                      backgroundColor: Palette.primary
                                          .withValues(alpha: 0.1),
                                      foregroundColor: Palette.primaryBlack),
                                  child: const Text('Download Invoice'),
                                ),
                                const Gap(5),
                                OutlinedButton(
                                  onPressed: () => InvoiceOption.view.action(
                                    context,
                                    element,
                                    InvoiceType.purchase,
                                    element.orders?.first.id ?? '',
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    textStyle: textTheme.bodyMedium
                                        ?.copyWith(fontWeight: FontWeight.w500),
                                    backgroundColor: Colors.white,
                                    side: BorderSide(color: Palette.stroke),
                                    foregroundColor: Palette.primaryBlack,
                                    minimumSize: const Size(50, 40),
                                    fixedSize: const Size(130, 35),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: const Text('View Order'),
                                )
                              ],
                            )
                          : Align(
                              alignment: Alignment.centerRight,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 15),
                                child: MenuAnchor(
                                  alignmentOffset: const Offset(250, -40),
                                  style: MenuStyle(
                                    backgroundColor:
                                        const WidgetStatePropertyAll(
                                            Colors.white),
                                    side: WidgetStatePropertyAll(
                                      BorderSide(color: Palette.kE7E7E7),
                                    ),
                                    shape: WidgetStatePropertyAll(
                                      RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                  builder: (BuildContext context,
                                      MenuController controller,
                                      Widget? child) {
                                    return InkWell(
                                      onTap: () {
                                        if (controller.isOpen) {
                                          controller.close();
                                        } else {
                                          controller.open();
                                        }
                                      },
                                      hoverColor: Colors.transparent,
                                      splashColor: Colors.transparent,
                                      child: child,
                                    );
                                  },
                                  menuChildren: InvoiceOption.values
                                      .map(
                                        (option) => MenuItemButton(
                                          requestFocusOnHover: false,
                                          onPressed: () => option.action(
                                            context,
                                            element,
                                            InvoiceType.purchase,
                                            element.orders?.first.id ?? '',
                                          ),
                                          child: Text(
                                            option.label,
                                            style: textTheme.bodyMedium,
                                          ),
                                        ),
                                      )
                                      .toList(),
                                  child: SizedBox(
                                    width: 42,
                                    height: 28,
                                    child: DecoratedBox(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Palette.kFCFCFC),
                                      child: Center(
                                        child: Text(
                                          "•••",
                                          style: textTheme.bodySmall?.copyWith(
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    )
                  ],
                ),
              )
              .toList(),
        ),
      );
    });
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme, String currency) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Row(
        children: [
          CurrencyWidget(
            amount,
            currency,
            amountStyle: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const Gap(5),
          Text(
            currency,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
            child: Text(
              text,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentBadge(
      String date, InvoiceStatus status, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Row(
        children: [
          Text(
            date,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
          const Gap(3),
          InvoiceStatusChip(status: status)
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }
}
