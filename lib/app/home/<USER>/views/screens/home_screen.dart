import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/business_verification/presentation/logic/states/business_verification_state.dart';
import 'package:td_procurement/app/home/<USER>/logic/home_state.dart';
import 'package:td_procurement/app/home/<USER>/views/widgets/empty_state.dart';
import 'package:td_procurement/app/home/<USER>/views/widgets/home_invoice.dart';
import 'package:td_procurement/app/home/<USER>/views/widgets/home_tab_bar.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/domain/logic/invoices_controller.dart';
import 'package:td_procurement/app/order/order_controller.dart';
import 'package:td_procurement/app/order/order_state.dart';
import 'package:td_procurement/app/order/widgets/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

const timeFilters = ["Last 30 Days", "Last 3 Months", "Last 6 Months"];

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final LayerLink homePageLayerLink = LayerLink();
  final ValueNotifier<bool> fetchState = ValueNotifier(false);
  final ValueNotifier<HomeTab> _selectedOption =
      ValueNotifier(HomeTab.invoices);
  OverlayEntry? overlayEntry;
  late OrderState orderState;
  HomeState homeState = HomeState(
      loading: true,
      presentDay: DateTime.now(),
      pastDay: DateTime.now().subtract(const Duration(days: 30)),
      selectedTimeFilter: 'Last 30 Days');

  @override
  void dispose() {
    fetchState.dispose();
    _selectedOption.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    orderState = ref.watch(orderControllerProvider);
    WidgetsBinding.instance.addPostFrameCallback((_) => fetchOrders());
  }

  Future<void> fetchOrders() async {
    fetchState.value = true;
    await ref.read(orderControllerProvider.notifier).fetchTransactions(
        ref.read(orderControllerProvider).fetchTransactionsParam);
    fetchState.value = false;
  }

  void _showDatePickerOverlay(BuildContext context) {
    overlayEntry = _createOverlayEntry(
      context,
      const SizedItem(width: 620, height: 400, top: 0.15, right: 0.012),
      _buildDatePicker(),
    );
    Overlay.of(context).insert(overlayEntry!);
  }

  void closeOverlay() {
    overlayEntry?.remove();
    overlayEntry = null;
  }

  String getFormattedDateRange(DateTime startDate, DateTime endDate) {
    return '${DateFormat.MMMd().format(startDate)} - ${DateFormat.MMMd().format(endDate)}';
  }

  // UI Building Methods
  Widget _buildDatePicker() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: _MultiDatePickerOverlay(
        startDate: ref.watch(homeProvider).pastDay,
        endDate: ref.watch(homeProvider).presentDay,
        onSelectRange: (startDate, endDate) async {
          if (startDate != null && endDate != null) {
            ref.watch(homeProvider.notifier).fetchStats(
                startDate, endDate, ref.watch(homeProvider).selectedTimeFilter);
            closeOverlay();
          }
        },
        onCancel: () async {
          closeOverlay();
          ref.watch(homeProvider.notifier).fetchStats(
              DateTime.now().subtract(const Duration(days: 30)),
              DateTime.now(),
              'Last 30 Days');
        },
      ),
    );
  }

  OverlayEntry _createOverlayEntry(
    BuildContext context,
    SizedItem size,
    Widget child,
  ) {
    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: closeOverlay,
            ),
          ),
          Positioned(
            right: MediaQuery.of(context).size.width - (size.width + 35),
            top: MediaQuery.of(context).size.height * (size.top + size.height),
            width: size.width,
            height: size.height + 25,
            child: CompositedTransformFollower(
              link: homePageLayerLink,
              showWhenUnlinked: false,
              child: Material(
                elevation: 8.0,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Palette.stroke),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: child,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: Palette.kF7F7F7,
      padding: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.center,
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(0.35),
          1: FlexColumnWidth(1.3),
          2: FlexColumnWidth(1.6),
          3: FlexColumnWidth(2.5),
          4: FlexColumnWidth(1.2),
          5: FlexColumnWidth(1.6),
          6: FlexColumnWidth(2.5),
          7: FlexColumnWidth(0.5),
        },
        children: [
          TableRow(
            children: [
              Container(),
              buildHeaderCell('Reference', textTheme),
              buildHeaderCell('Amount', textTheme),
              buildHeaderCell('Summary', textTheme),
              buildHeaderCell('Delivering to', textTheme),
              buildHeaderCell('Created on', textTheme),
              Container(), // Empty space for floating icon
              Container(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    Widget? widget = const SizedBox.shrink();

    var v = ref.watch(
      invoicesControllerProvider(ref.read(homeInvoicesArgProvider)),
    );

    homeState = ref.watch(homeProvider);
    fetchState.value = homeState.loading;

    v.when(
      skipLoadingOnRefresh: false,
      data: (data) {
        widget = homeWidget(
          theme,
          data.invoices.isEmpty
              ? ProcurementAppEmptyState.unPaidInvoices(context)
              : HomeInvoice(data.invoices),
        );
      },
      error: (error, __) {
        widget = homeWidget(
          theme,
          ProcurementAppEmptyState.unPaidInvoices(context),
        );
      },
      loading: () {
        widget = homeWidget(
          theme,
          const Skeletonizer(
            enabled: true,
            child: HomeInvoice([]),
          ),
        );
      },
    );

    return widget!;
  }

  Widget homeWidget(ThemeData theme, Widget invoiceWidget) {
    final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;

    // Verification checks
    final bool step1Complete = outlet?.company?.kyb?.businessType != null &&
        outlet?.company?.kyb?.rcNumber != null;
    final bool step2Complete = outlet?.company?.kyb?.bvn != null &&
        outlet?.walletAccount?.accountNumber != null &&
        outlet?.walletAccount?.bankName != null;
    final bool? step4Complete = outlet?.kyc?.idVerified;

    final bool verificationDone = outlet?.country?.toLowerCase() == "ng"
        ? step1Complete && step2Complete
        : step1Complete && step4Complete == true;

    final bool isExportCountry =
        ref.read(countryTypeProvider) == CountryType.export;

    return ValueListenableBuilder<bool>(
      valueListenable: fetchState,
      builder: (context, loadingState, child) {
        return Skeletonizer(
          enabled: loadingState,
          child: DefaultTabController(
            length: 2,
            child: _buildHomeContent(
                theme,
                invoiceWidget,
                verificationDone,
                step1Complete,
                step2Complete,
                step4Complete,
                outlet,
                isExportCountry),
          ),
        );
      },
    );
  }

  Widget _buildHomeContent(
    ThemeData theme,
    Widget invoiceWidget,
    bool verificationDone,
    bool step1Complete,
    bool step2Complete,
    bool? step4Complete,
    dynamic outlet,
    bool isExportCountry,
  ) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTopContainer(theme, verificationDone, step1Complete,
                step2Complete, step4Complete, outlet),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: _buildMainContent(theme, invoiceWidget, isExportCountry),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopContainer(
    ThemeData theme,
    bool verificationDone,
    bool step1Complete,
    bool step2Complete,
    bool? step4Complete,
    dynamic outlet,
  ) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
        image: DecorationImage(
          image: AssetImage('assets/images/packs/console_top_bg.png'),
          fit: BoxFit.cover,
        ),
      ),
      padding: const EdgeInsets.all(30),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!verificationDone) ...[
            _buildOnboardingSection(
                theme, step1Complete, step2Complete, step4Complete, outlet),
            const Gap(60),
          ],
          _buildOverviewSection(theme),
        ],
      ),
    );
  }

  Widget _buildOnboardingSection(
    ThemeData theme,
    bool step1Complete,
    bool step2Complete,
    bool? step4Complete,
    dynamic outlet,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Finish your onboarding',
          style: theme.textTheme.headlineMedium?.copyWith(fontSize: 20),
        ),
        Text(
          "Provide your organization's information and invite teammates to complete onboarding",
          style: theme.textTheme.bodyMedium
              ?.copyWith(color: Palette.blackSecondary),
        ),
        const Gap(15),
        FilledButton(
          onPressed: () => _handleOnboardingNavigation(
              step1Complete, step2Complete, step4Complete, outlet),
          style: FilledButton.styleFrom(
              minimumSize: const Size(214, 40),
              textStyle: theme.textTheme.bodyMedium),
          child: const Text('Add company information'),
        ),
      ],
    );
  }

  void _handleOnboardingNavigation(
    bool step1Complete,
    bool step2Complete,
    bool? step4Complete,
    dynamic outlet,
  ) {
    int nextStep = 0;
    if (!step1Complete) {
      nextStep = 0;
    } else if (step1Complete && step2Complete) {
      nextStep = outlet?.country!.toLowerCase() != "ng" ? 3 : 2;
    } else if (step1Complete) {
      nextStep = outlet?.country!.toLowerCase() != "ng" ? 3 : 1;
    }

    ref
        .read(businessVerificationProvider.notifier)
        .updateNavigationState(nextStep);
    context.pushNamed(kBusinessVerificationRoute);
  }

  Widget _buildOverviewSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overview',
          style: theme.textTheme.headlineMedium?.copyWith(fontSize: 20),
        ),
        const Gap(10),
        _buildTimeFilterContainer(),
        const Gap(20),
        _buildStatsCards(),
      ],
    );
  }

  Widget _buildTimeFilterContainer() {
    return Container(
      width: 250,
      height: 30,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        border: Border.all(color: Palette.kE7E7E7, width: 1),
        boxShadow: const [
          BoxShadow(
              spreadRadius: -1,
              blurRadius: 2,
              offset: Offset(0, 2),
              color: Palette.k0000000A)
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Expanded(child: _buildTimeFilterDropdown()),
          VerticalDivider(color: Palette.kE7E7E7),
          Expanded(child: _buildDateRangePicker()),
        ],
      ),
    );
  }

  Widget _buildTimeFilterDropdown() {
    return Theme(
      data: ThemeData(
        popupMenuTheme: PopupMenuThemeData(color: Palette.kFFFFFF),
      ),
      child: PopupMenuButton<String>(
        itemBuilder: _buildTimeFilterItems,
        elevation: 0.0,
        padding: EdgeInsets.zero,
        onSelected: _handleTimeFilterSelection,
        tooltip: 'Show days menu',
        child: _buildTimeFilterDisplay(),
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildTimeFilterItems(BuildContext context) {
    return timeFilters
        .map((e) => PopupMenuItem(
              value: e,
              child: Padding(
                padding: const EdgeInsets.only(left: 18),
                child: Text(e, maxLines: 1),
              ),
            ))
        .toList();
  }

  void _handleTimeFilterSelection(String val) {
    DateTime pastDay = DateTime.now();
    final presentDay = DateTime.now();

    setState(() {
      switch (val) {
        case "Last 30 Days":
          pastDay = getXMonthsBefore(DateTime.now(), 1);
        case "Last 3 Months":
          pastDay = getXMonthsBefore(DateTime.now(), 3);
        case "Last 6 Months":
          pastDay = getXMonthsBefore(DateTime.now(), 6);
      }
    });

    ref.watch(homeProvider.notifier).fetchStats(pastDay, presentDay, val);
  }

  Widget _buildTimeFilterDisplay() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Text(
            ref.watch(homeProvider).selectedTimeFilter,
            style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
          ),
        ),
        SvgPicture.asset(kChevronDownSvg),
      ],
    );
  }

  Widget _buildDateRangePicker() {
    return InkWell(
      onTap: () => _showDatePickerOverlay(context),
      child: CompositedTransformTarget(
        link: homePageLayerLink,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                getFormattedDateRange(ref.watch(homeProvider).pastDay,
                    ref.watch(homeProvider).presentDay),
                style:
                    const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
              ),
            ),
            const Gap(5),
            const Icon(Icons.calendar_month_outlined, size: 15),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () =>
                context.goNamed(kOrdersRoute, extra: {'isRefreshing': true}),
            child: _buildStatsCard('Order Volume', homeState.data?.orderVolume),
          ),
        ),
        const Gap(10),
        Expanded(
          child: InkWell(
            onTap: () => context.goNamed(kOrdersRoute,
                extra: {'isRefreshing': true, "pending": true}),
            child: _buildStatsCard(
                'Orders in progress', homeState.data?.openOrders),
          ),
        ),
        const Gap(10),
        Expanded(
          child: InkWell(
            onTap: () =>
                context.goNamed(kInvoicesRoute, extra: InvoiceStatus.unpaid),
            child: _buildStatsCard(
                'Unpaid Invoices', homeState.data?.openInvoices),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCard(String title, List<dynamic>? data) {
    return CustomCard(
      title: title,
      amount: data?.isNotEmpty == true ? data!.first.amount : null,
      iso: data?.isNotEmpty == true ? data!.first.currency?.iso : null,
    );
  }

  Widget _buildMainContent(
    ThemeData theme,
    Widget invoiceWidget,
    bool isExportCountry,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(30),
        _buildTabContent(invoiceWidget),
        const Gap(70),
        if (!isExportCountry) _buildFrequentlyOrdered(theme),
      ],
    );
  }

  Widget _buildTabContent(Widget invoiceWidget) {
    return ValueListenableBuilder(
      valueListenable: _selectedOption,
      builder: (context, state, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: HomeTab.values
                  .map((option) => InkWell(
                        splashColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        onTap: () => _selectedOption.value = option,
                        child: HomeTabBar(
                          option: option,
                          isActive: state == option,
                        ),
                      ))
                  .toList(),
            ),
            const Gap(20),
            _buildTabView(state, invoiceWidget),
          ],
        );
      },
    );
  }

  Widget _buildTabView(HomeTab state, Widget invoiceWidget) {
    switch (state) {
      case HomeTab.invoices:
        return invoiceWidget;
      case HomeTab.orders:
        final transactions =
            ref.watch(orderControllerProvider).transactions.value;
        if (transactions == null || transactions.isEmpty) {
          return ProcurementAppEmptyState.recentOrders(context);
        }
        return Column(
          children: [
            _buildTableHeader(),
            SizedBox(
              height: 280,
              child: OrdersTableWidget(transactions.length > 5
                  ? transactions.sublist(0, 5)
                  : transactions),
            )
          ],
        );
    }
  }

  Widget _buildFrequentlyOrdered(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Frequently Ordered',
          style: theme.textTheme.headlineMedium?.copyWith(fontSize: 20),
        ),
        const Gap(20),
        Divider(color: Palette.kE7E7E7),
        if (homeState.data?.frequentlyOrdered?.isNotEmpty == true)
          _buildFrequentlyOrderedList()
        else
          ProcurementAppEmptyState.frequentlyOrdered(context)
      ],
    );
  }

  Widget _buildFrequentlyOrderedList() {
    return ConstrainedBox(
      constraints: const BoxConstraints.tightFor(height: 200),
      child: ListView.builder(
        padding: const EdgeInsets.only(left: 20),
        scrollDirection: Axis.horizontal,
        itemCount: homeState.data!.frequentlyOrdered!.length > 5
            ? 5
            : homeState.data!.frequentlyOrdered!.length,
        itemBuilder: (context, i) {
          return Padding(
            padding: const EdgeInsets.only(right: 50),
            child: SizedBox(
              width: 200,
              child: VariantGridItem(
                onHomeScreen: true,
                useEditingCart: false,
                Variant.fromMap(
                  homeState.data!.frequentlyOrdered![i].toJson(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Helper Classes
class CustomCard extends StatelessWidget {
  const CustomCard({
    super.key,
    required this.title,
    this.amount,
    this.iso,
  });

  final String title;
  final num? amount;
  final String? iso;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.kE7E7E7),
        boxShadow: const [
          BoxShadow(
            color: Palette.k0000000A,
            blurRadius: 2,
            spreadRadius: -1,
            offset: Offset(0, 2),
          )
        ],
      ),
      height: 157,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.headlineSmall?.copyWith(fontSize: 14),
          ),
          const Spacer(),
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: amount != null
                    ? CurrencyWidget(
                        amount!,
                        iso!,
                        amountStyle: textTheme.bodyLarge?.copyWith(
                            fontSize: 20, color: Palette.blackSecondary),
                      )
                    : Text(
                        '0',
                        style: textTheme.bodyLarge?.copyWith(
                            fontSize: 20, color: Palette.blackSecondary),
                      ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class SizedItem {
  final double width;
  final double height;
  final double top;
  final double right;

  const SizedItem({
    required this.width,
    required this.height,
    required this.top,
    required this.right,
  });
}

class _MultiDatePickerOverlay extends ConsumerStatefulWidget {
  final void Function(DateTime?, DateTime?) onSelectRange;
  final VoidCallback onCancel;
  final DateTime startDate;
  final DateTime endDate;

  const _MultiDatePickerOverlay({
    required this.onSelectRange,
    required this.onCancel,
    required this.startDate,
    required this.endDate,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _MultiDatePickerOverlayState();
}

class _MultiDatePickerOverlayState
    extends ConsumerState<_MultiDatePickerOverlay> {
  DateTime _focusedLeftMonth =
      DateTime.now().subtract(const Duration(days: 30));
  DateTime _focusedRightMonth = DateTime.now();
  DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;

  @override
  void initState() {
    super.initState();
    _focusedLeftMonth = widget.startDate;
    _focusedRightMonth = widget.endDate;
    _selectedStartDate = widget.startDate;
    _selectedEndDate = widget.endDate;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: _buildCalendar(_focusedLeftMonth, isLeftCalendar: true),
            ),
            const Gap(16),
            Flexible(
              child: _buildCalendar(_focusedRightMonth, isLeftCalendar: false),
            ),
          ],
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.only(top: 15),
          decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Palette.stroke))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(
                onPressed: widget.onCancel,
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  foregroundColor: Palette.primary,
                  backgroundColor: Palette.primary.withValues(alpha: 0.2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Cancel'),
              ),
              const Gap(20),
              ElevatedButton(
                onPressed: () =>
                    widget.onSelectRange(_selectedStartDate, _selectedEndDate),
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  foregroundColor: Colors.white,
                  backgroundColor: Palette.primaryBlack,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _moveCalendars(bool moveBackwards) {
    setState(() {
      if (moveBackwards) {
        _focusedLeftMonth =
            DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month - 1, 1);
        _focusedRightMonth =
            DateTime(_focusedRightMonth.year, _focusedRightMonth.month - 1, 1);
      } else if (_focusedRightMonth.isBefore(DateTime.now())) {
        _focusedLeftMonth =
            DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month + 1, 1);
        _focusedRightMonth =
            DateTime(_focusedRightMonth.year, _focusedRightMonth.month + 1, 1);
      }
    });
  }

  Widget _buildCalendar(DateTime focusedMonth, {required bool isLeftCalendar}) {
    final now = DateTime.now();
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (isLeftCalendar)
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => _moveCalendars(true),
              )
            else
              const Gap(48),
            Text(
              '${focusedMonth.toMonthName()} ${focusedMonth.year}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            if (!isLeftCalendar)
              IconButton(
                icon: const Icon(Icons.arrow_forward),
                onPressed: _focusedRightMonth
                        .isBefore(DateTime(now.year, now.month, 1))
                    ? () => _moveCalendars(false)
                    : null,
              )
            else
              const Gap(48),
          ],
        ),
        const Gap(10),
        TableCalendar(
          firstDay: DateTime.now().subtract(const Duration(days: 365 * 5)),
          lastDay: DateTime.now().add(const Duration(days: 365)),
          focusedDay: focusedMonth,
          rowHeight: 45.0,
          daysOfWeekHeight: 16.0,
          enabledDayPredicate: (day) {
            if (!isLeftCalendar && day.isAfter(now)) {
              return false; // Disable future dates on the right calendar
            }
            return true;
          },
          selectedDayPredicate: (day) {
            if (_selectedStartDate != null && _selectedEndDate != null) {
              return day.isAfter(_selectedStartDate!) &&
                  day.isBefore(_selectedEndDate!);
            }
            return false;
          },
          rangeStartDay: _selectedStartDate,
          rangeEndDay: _selectedEndDate,
          onDaySelected: (selectedDay, _) {
            setState(() {
              if (_selectedStartDate == null || _selectedEndDate != null) {
                _selectedStartDate = selectedDay;
                _selectedEndDate = null;
              } else {
                _selectedEndDate = selectedDay.isAfter(_selectedStartDate!)
                    ? selectedDay
                    : _selectedStartDate;
                _selectedStartDate = selectedDay.isAfter(_selectedStartDate!)
                    ? _selectedStartDate
                    : selectedDay;
              }
            });
          },
          calendarFormat: CalendarFormat.month,
          startingDayOfWeek: StartingDayOfWeek.monday,
          headerVisible: false,
          calendarStyle: CalendarStyle(
            cellMargin: const EdgeInsets.all(1),
            rangeHighlightColor: Colors.blue.withValues(alpha: 0.5),
            selectedDecoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            rangeStartDecoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            rangeEndDecoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ],
    );
  }
}

/* context,
                                        child: VariantDetailsWidget(variant),
                                        percentage: 0.44,
                                        minRightSectionWidth: 630,
                                      ) */
