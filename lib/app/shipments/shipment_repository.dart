import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

import 'shipment_data_source.dart';
import 'shipment_params.dart';

final shipmentRepoProvider = Provider.autoDispose<ShipmentRepo>((ref) {
  final dataSource = ref.read(shipmentDataSourceProvider);
  return ShipmentRepoImplementation(dataSource, ref);
});

abstract class ShipmentRepo {
  Future<ApiResponse<FetchShipmentsResponse>> fetchShipments(
      FetchShipmentsParam params);
  Future<ApiResponse<Shipment>> getShipment(String shippingId);
  Future<ApiResponse<List<Document>>> fetchDocuments(String shippingId);
  Future<ApiResponse<Invoice>> getInvoice(String orderId);
  Future<ApiResponse<String>> getInvoiceUrl(String orderId);
}

class ShipmentRepoImplementation implements ShipmentRepo {
  final ShipmentDataSource _shipmentDataSource;
  final Ref _ref;
  ShipmentRepoImplementation(this._shipmentDataSource, this._ref);

  @override
  Future<ApiResponse<FetchShipmentsResponse>> fetchShipments(
      FetchShipmentsParam params) async {
    return dioInterceptor(
        () => _shipmentDataSource.fetchShipments(params), _ref);
  }

  @override
  Future<ApiResponse<Shipment>> getShipment(String shippingId) async {
    return dioInterceptor(
        () => _shipmentDataSource.getShipment(shippingId), _ref);
  }

  @override
  Future<ApiResponse<List<Document>>> fetchDocuments(String shippingId) async {
    return dioInterceptor(
        () => _shipmentDataSource.fetchDocuments(shippingId), _ref);
  }

  @override
  Future<ApiResponse<Invoice>> getInvoice(String orderId) async {
    return dioInterceptor(() => _shipmentDataSource.getInvoice(orderId), _ref);
  }

  @override
  Future<ApiResponse<String>> getInvoiceUrl(String orderId) async {
    return dioInterceptor(
        () => _shipmentDataSource.getInvoiceUrl(orderId), _ref);
  }
}
