import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/app/shipments/shipment_controller.dart';
import 'package:td_procurement/app/shipments/shipment_params.dart';
import 'package:td_procurement/app/shipments/shipment_state.dart';
import 'package:td_procurement/app/shipments/widgets/index.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/status_filtering.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class ShipmentsScreen extends ConsumerStatefulWidget {
  const ShipmentsScreen(this.refreshShipments, {super.key});

  final bool refreshShipments;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ShipmentsScreenState();
}

class _ShipmentsScreenState extends ConsumerState<ShipmentsScreen> {
  final _searchController = TextEditingController();
  Timer? _searchDebounceTimer;
  int _selectedStatusIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  void _initializeScreen() {
    final shipmentState = ref.read(shipmentControllerProvider);
    _selectedStatusIndex = shipmentState.fetchShipmentsParams.activeIndex;
    _searchController.text = shipmentState.fetchShipmentsParams.searchText;
    _fetchShipments();
  }

  @override
  void didUpdateWidget(ShipmentsScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    _fetchShipments();
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _fetchShipments() {
    final params = ref.read(shipmentControllerProvider).fetchShipmentsParams;
    Future.microtask(() {
      ref
          .read(shipmentControllerProvider.notifier)
          .fetchShipments(params, forced: widget.refreshShipments);
    });
  }

  void _handleStatusFilter(int index, ShipmentState state) {
    if (state.shipments.isLoading || index == _selectedStatusIndex) return;

    setState(() => _selectedStatusIndex = index);
    _updateShipmentsForStatus(index);
  }

  void _updateShipmentsForStatus(int index) {
    final status =
        ref.read(shipmentControllerProvider).shipmentStatusOptions[index];
    final params = _getParamsForStatus(status);

    final shipmentNotifier = ref.read(shipmentControllerProvider.notifier);
    shipmentNotifier
      ..setDateFilter(null, null)
      ..fetchShipments(params.copyWith(activeIndex: index), forced: true);
  }

  FetchShipmentsParam _getParamsForStatus(String status) {
    final baseParams = FetchShipmentsParam.defaultValue();
    return switch (status.toLowerCase()) {
      'all shipments' => baseParams.copyWith(status: 'all'),
      'pending' => baseParams.copyWith(status: 'pending'),
      'processing' => baseParams.copyWith(status: 'processing'),
      'dispatched' => baseParams.copyWith(status: 'dispatched'),
      'delivered' => baseParams.copyWith(status: 'delivered'),
      _ => baseParams.copyWith(status: status.toLowerCase()),
    };
  }

  void _handleSearch(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      final params = ref.read(shipmentControllerProvider).fetchShipmentsParams;
      ref.read(shipmentControllerProvider.notifier).fetchShipments(
            params.copyWith(
              searchText: query.trim(),
              currentPage: 1,
            ),
            forced: true,
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final shipmentState = ref.watch(shipmentControllerProvider);
    final statusOptions = shipmentState.shipmentStatusOptions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(textTheme),
        _buildStatusFilters(shipmentState, statusOptions),
        _buildSearchSection(shipmentState),
        _buildTableHeader(shipmentState),
        _buildShipmentsContent(shipmentState),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(20),
        Padding(
          padding: const EdgeInsets.only(left: 40),
          child: Text('Shipments', style: textTheme.headlineMedium),
        ),
        const Gap(20),
      ],
    );
  }

  Widget _buildStatusFilters(ShipmentState state, List<String> statusOptions) {
    return Container(
      padding: const EdgeInsets.only(left: 40, bottom: 12),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Palette.stroke)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            flex: 3,
            child: Row(
              children: [
                for (var i = 0; i < statusOptions.length; i++)
                  Flexible(
                    child: StatusFilteringWidget(
                      statusOptions[i],
                      _selectedStatusIndex == i,
                      width: 130,
                      padding: const EdgeInsets.symmetric(vertical: 19),
                      onPressed: () => _handleStatusFilter(i, state),
                    ),
                  ),
              ],
            ),
          ),
          Flexible(
            flex: 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                DatePickerWidget(
                  initialDate: state.selectedStartDate,
                  selectedStartDate: state.selectedStartDate,
                  selectedEndDate: state.selectedEndDate,
                  getValueLabel: () => getFormattedDateRange(
                    state.selectedStartDate,
                    state.selectedEndDate,
                  ),
                  onDatesSelected: ({startDate, endDate}) async {
                    final params = ref
                        .read(shipmentControllerProvider)
                        .fetchShipmentsParams;
                    final notifier =
                        ref.read(shipmentControllerProvider.notifier);

                    notifier.setDateFilter(
                        startDate ??= endDate, endDate ??= startDate);
                    await Future.delayed(Duration.zero);
                    notifier.fetchShipments(
                      params.copyWith(selectedDates: [startDate, endDate]),
                      forced: true,
                    );
                  },
                  onCancel: () async {
                    final stateParams = ref
                        .read(shipmentControllerProvider)
                        .fetchShipmentsParams;
                    if (!stateParams.selectedDates.any((date) => date != null))
                      return;

                    final params = FetchShipmentsParam.defaultValue().copyWith(
                      status: stateParams.status,
                      searchText: stateParams.searchText,
                      activeIndex: stateParams.activeIndex,
                    );

                    final notifier =
                        ref.read(shipmentControllerProvider.notifier);
                    notifier.setDateFilter(null, null);
                    await Future.delayed(Duration.zero);
                    notifier.fetchShipments(params, forced: true);
                  },
                ),
                const Gap(20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection(ShipmentState state) {
    return Skeletonizer(
      enabled: state.shipments.isLoading && !state.fetchShipmentsParams.loaded,
      child: _buildSearchBar(),
    );
  }

  Widget _buildSearchBar() {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            height: 48,
            padding: const EdgeInsets.only(left: 40),
            child: TextFormField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Type to search by shipment number',
                prefixIcon: Padding(
                  padding: const EdgeInsets.only(top: 2.0, right: 10),
                  child: Skeleton.replace(
                    child: SvgPicture.asset(
                      '$kSvgDir/order/search.svg',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                prefixIconConstraints: const BoxConstraints(
                  minWidth: 22,
                  minHeight: 22,
                ),
                hintStyle: textTheme.bodyMedium?.copyWith(
                  color: Palette.placeholder,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
              ),
              cursorColor: Palette.strokePressed,
              cursorHeight: 18,
              onChanged: _handleSearch,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTableHeader(ShipmentState state) {
    final isVisible = state.shipments.isLoading ||
        (state.shipments.hasValue && state.shipments.value!.isNotEmpty);

    if (!isVisible) return const SizedBox.shrink();

    return Skeletonizer(
      enabled: state.shipments.isLoading,
      child: _buildTableHeaderContent(),
    );
  }

  Widget _buildTableHeaderContent() {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: Palette.kF7F7F7,
      padding: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.center,
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(0.3),
          1: FlexColumnWidth(1.6),
          2: FlexColumnWidth(1.3),
          3: FlexColumnWidth(1.4),
          4: FlexColumnWidth(1.6),
          5: FlexColumnWidth(4),
        },
        children: [
          TableRow(
            children: [
              Container(),
              _buildHeaderCell('Shipment number', textTheme),
              _buildHeaderCell('Date', textTheme),
              _buildHeaderCell('Status', textTheme),
              _buildHeaderCell('Invoice numbers', textTheme),
              Container(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildShipmentsContent(ShipmentState state) {
    return Expanded(
      child: state.shipments.when(
        data: (shipments) {
          if (shipments.isEmpty) {
            return const EmptyShipmentsWidget();
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: ShipmentsTableWidget(shipments)),
              const Gap(10),
              const ShipmentPagination(),
              const Gap(10),
            ],
          );
        },
        loading: () => _buildLoadingContent(),
        error: (error, stack) => _buildErrorContent(error),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ShipmentsTableWidget(
              List.filled(10, Shipment.defaultValue()),
            ),
          ),
          const ShipmentPagination(),
          const Gap(20),
        ],
      ),
    );
  }

  Widget _buildErrorContent(Object error) {
    if (kIsWeb || kIsWasm) {
      return Skeletonizer(
        enabled: true,
        child: ShipmentsTableWidget(
          List.filled(20, Shipment.defaultValue()),
        ),
      );
    }

    return FailureWidget(
      fullScreen: true,
      heightFactor: 0.7,
      e: error,
      retry: () {
        final params =
            ref.read(shipmentControllerProvider).fetchShipmentsParams;
        ref.read(shipmentControllerProvider.notifier).fetchShipments(params);
      },
    );
  }
}
