import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

import 'shipment_params.dart';
import 'shipment_use_cases.dart';

final shipmentServiceProvider = Provider.autoDispose<ShipmentService>((ref) {
  final fetchShipmentsUseCase = ref.read(fetchShipmentsUseCaseProvider);
  final getShipmentUseCase = ref.read(getShipmentUseCaseProvider);
  final fetchDocumentsUseCase = ref.read(fetchDocumentsUseCaseProvider);

  return ShipmentServiceImplementation(
    fetchShipmentsUseCase,
    getShipmentUseCase,
    fetchDocumentsUseCase,
  );
});

abstract class ShipmentService {
  Future<ApiResponse<FetchShipmentsResponse>> fetchShipments(
      FetchShipmentsParam params);
  Future<ApiResponse<Shipment>> getShipment(String shippingId);
  Future<ApiResponse<List<Document>>> fetchDocuments(String shippingId);
}

class ShipmentServiceImplementation implements ShipmentService {
  ShipmentServiceImplementation(
    this._fetchShipmentsUseCase,
    this._getShipmentUseCase,
    this._fetchDocumentsUseCase,
  );

  final FetchShipmentsUseCase _fetchShipmentsUseCase;
  final GetShipmentUseCase _getShipmentUseCase;
  final FetchDocumentsUseCase _fetchDocumentsUseCase;

  @override
  Future<ApiResponse<FetchShipmentsResponse>> fetchShipments(
      FetchShipmentsParam params) async {
    return _fetchShipmentsUseCase.call(params);
  }

  @override
  Future<ApiResponse<Shipment>> getShipment(String shippingId) async {
    return _getShipmentUseCase.call(shippingId);
  }

  @override
  Future<ApiResponse<List<Document>>> fetchDocuments(String shippingId) async {
    return _fetchDocumentsUseCase.call(shippingId);
  }
}
