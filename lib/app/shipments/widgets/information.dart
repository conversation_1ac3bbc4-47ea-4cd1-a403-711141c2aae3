import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_commons_flutter/utils/index.dart';
import 'package:td_procurement/app/shipments/shipment_controller.dart';
import 'package:td_procurement/app/shipments/shipment_use_cases.dart';
import 'package:td_procurement/app/shipments/widgets/invoice_details.dart';
import 'package:td_procurement/core/models/document.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

import 'tile.dart';

class InformationWidget extends ConsumerWidget {
  const InformationWidget(this.shipment, {super.key});

  final Shipment shipment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;
    final documents = ref.watch(shipmentControllerProvider).documents;

    return Column(
      children: [
        _buildDetailsSection(context, textTheme, documents),
        const Gap(20),
        _buildContainersSection(context, textTheme),
        const Gap(20),
        _buildInvoicesSection(context, textTheme, ref),
        const Gap(40),
      ],
    );
  }

  Widget _buildDetailsSection(
      BuildContext context, TextTheme textTheme, List<Document> documents) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Details', style: textTheme.headlineSmall),
            const Gap(16),
            ...[
              _buildRow([
                TileWidget(
                  title: 'Customer Name',
                  subtitle: shipment.shippingAddress?.company,
                ),
                TileWidget(
                  title: 'Customer Address',
                  subtitle: shipment.shippingAddress?.address1,
                ),
              ]),
              _buildRow([
                TileWidget(
                  title: 'Shipment Number',
                  subtitle: shipment.shipmentNumber?.toString(),
                ),
                TileWidget(
                  title: 'Created Date',
                  subtitle: shipment.createdAt?.toFixedDate(),
                ),
              ]),
              _buildRow([
                TileWidget(
                  title: 'Bill of Lading',
                  subtitle: shipment.shippingDetails?.billOfLading,
                ),
                TileWidget(
                  title: 'Documents Status',
                  subtitle: '${documents.length} Available',
                ),
              ]),
              _buildRow([
                TileWidget(
                  title: 'Shipping Status',
                  subtitle: shipment.shippingStatus,
                ),
                TileWidget(
                  title: 'Documents Available',
                  subtitle: documents.isEmpty
                      ? '-'
                      : documents.map((x) => x.documentType).join(', '),
                ),
              ]),
              _buildRow([
                TileWidget(
                  title: 'Carrier',
                  subtitle: shipment.shippingDetails?.carrierName,
                ),
                TileWidget(
                  title: 'Ship At',
                  subtitle: shipment.shippingDetails?.shipAt?.toFixedDate(),
                ),
              ]),
              _buildRow([
                TileWidget(
                  title: 'Order Number(s)',
                  subtitle: (shipment.orders != null &&
                          shipment.orders!.isNotEmpty)
                      ? shipment.orders!.map((x) => x.orderNumber).join(', ')
                      : '-',
                ),
              ]),
            ].expand((widget) => [widget, const Gap(12)]),
          ],
        ),
      ),
    );
  }

  Widget _buildRow(List<Widget> children) {
    return Row(
      children: children
          .map((widget) => Expanded(flex: children.length, child: widget))
          .toList(),
    );
  }

  Widget _buildContainersSection(BuildContext context, TextTheme textTheme) {
    return _buildSection(
      context,
      title: 'Containers',
      headerCells: ['Container number', 'Container name', 'Details'],
      rowBuilder: () =>
          shipment.shippingDetails?.containers
              ?.map((container) => [
                    container.containerNumber.toString(),
                    container.name,
                    container.details,
                  ])
              .toList() ??
          [],
      textTheme: textTheme,
    );
  }

  Widget _buildInvoicesSection(
      BuildContext context, TextTheme textTheme, WidgetRef ref) {
    return _buildSection(
      context,
      title: 'Invoices',
      headerCells: ['Invoice Number', 'Invoice Date', ''],
      rowBuilder: () {
        final invoices = shipment.invoices ?? [];
        return invoices
            .map(
              (invoice) => [
                invoice,
                invoice.createdAt?.toFixedDate() ?? '-',
                _buildDownloadButton(
                  ref,
                  (invoice.orderIds != null && invoice.orderIds!.isNotEmpty)
                      ? invoice.orderIds!.first
                      : '',
                  textTheme,
                ),
              ],
            )
            .toList();
      },
      textTheme: textTheme,
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<String> headerCells,
    required List<List<dynamic>> Function() rowBuilder,
    required TextTheme textTheme,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Palette.stroke),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 16, left: 14),
            child: Text(title, style: textTheme.headlineSmall),
          ),
          const Gap(16),
          _buildTableHeader(headerCells, textTheme),
          Table(
            columnWidths: const {
              0: FlexColumnWidth(3),
              1: FlexColumnWidth(3),
              2: FlexColumnWidth(3),
            },
            children: rowBuilder()
                .map((row) => _buildTableRow(row, textTheme, context))
                .toList(),
          )
        ],
      ),
    );
  }

  Widget _buildTableHeader(List<String> headers, TextTheme textTheme) {
    return Container(
      color: Palette.kF7F7F7,
      padding: const EdgeInsets.symmetric(vertical: 10),
      alignment: Alignment.center,
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(3),
          1: FlexColumnWidth(3),
          2: FlexColumnWidth(3),
        },
        children: [
          TableRow(
            children: headers
                .map(
                    (header) => _buildHeaderCell(header, textTheme.bodyMedium!))
                .toList(),
          ),
        ],
      ),
    );
  }

  TableRow _buildTableRow(
      List<dynamic> cells, TextTheme textTheme, BuildContext context) {
    return TableRow(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Palette.stroke)),
      ),
      children: cells.map((cell) {
        if (cell is Widget) return cell;
        if (cell is Invoice) {
          return _buildContentCell(
              cell.invoiceNumber != null ? '#${cell.invoiceNumber}' : '-',
              textTheme,
              context,
              cell);
        }
        return _buildContentCell(cell?.toString() ?? '-', textTheme, context);
      }).toList(),
    );
  }

  Widget _buildHeaderCell(String text, TextStyle textStyle) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(text, style: textStyle.copyWith(fontWeight: FontWeight.w500)),
    );
  }

  Widget _buildContentCell(
      String content, TextTheme textTheme, BuildContext context,
      [Invoice? invoice]) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: content.startsWith('#')
          ? InkWell(
              onTap: () {
                if (invoice != null) {
                  showCustomGeneralDialog(
                    context,
                    // percentage: 0.6,
                    child: InvoiceDetailsWidget(invoice, isCloseIcon: true),
                  );
                }
              },
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              focusColor: Colors.transparent,
              splashColor: Colors.transparent,
              child: Text(
                capitalize(content),
                style: textTheme.bodyMedium?.copyWith(
                  color: Palette.strokePressed,
                  decoration: TextDecoration.underline,
                  decorationColor: Palette.strokePressed,
                ),
              ),
            )
          : Text(capitalize(content), style: textTheme.bodyMedium),
    );
  }

  Widget _buildDownloadButton(
      WidgetRef ref, String orderId, TextTheme textTheme) {
    final isDownloadingProvider = StateProvider<bool>((ref) => false);

    return Consumer(
      builder: (context, ref, _) {
        final isDownloading = ref.watch(isDownloadingProvider);

        return Row(
          children: [
            OutlinedButton(
              onPressed: isDownloading
                  ? null
                  : () async {
                      if (orderId.isEmpty) {
                        // ToastBox.showError(context, 'orderId is empty');
                        return;
                      }

                      ref.read(isDownloadingProvider.notifier).state = true;

                      final res =
                          await ref.read(getInvoiceUrlUseCaseProvider(orderId));

                      res.when(
                        success: (invoiceUrl) {
                          ref.read(isDownloadingProvider.notifier).state =
                              false;
                          openUri(invoiceUrl);
                        },
                        failure: (error, _) {
                          ref.read(isDownloadingProvider.notifier).state =
                              false;
                          Toast.apiError(error, context);
                        },
                      );
                    },
              style: OutlinedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                side: BorderSide(color: Palette.primaryBlack, width: 2),
              ),
              child: SizedBox(
                width: 70,
                child: Center(
                  child: isDownloading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child:
                              CircularProgressIndicator(color: Palette.primary),
                        )
                      : Text(
                          'Download',
                          style: textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
