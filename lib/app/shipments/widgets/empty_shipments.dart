import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/shipments/shipment_controller.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class EmptyShipmentsWidget extends ConsumerWidget {
  const EmptyShipmentsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = Theme.of(context).textTheme;

    final shipmentState = ref.watch(shipmentControllerProvider);

    final status = shipmentState
        .shipmentStatusOptions[shipmentState.fetchShipmentsParams.activeIndex];

    final text = status.toLowerCase().contains('all')
        ? 'No shipments matching your filter criteria'
        : 'No $status shipments available';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset('$kSvgDir/order/box.svg'),
          Gap(20.h),
          Text(
            text,
            style: textTheme.headlineSmall,
          ),
          Gap(8.h),
          // Text.rich(
          //   TextSpan(
          //       text: 'You can track and receive inventory\nordered from ',
          //       children: [
          //         TextSpan(
          //             text: 'suppliers',
          //             style: textTheme.bodyLarge
          //                 ?.copyWith(color: Palette.primary)),
          //         const TextSpan(text: ' here'),
          //       ]),
          //   textAlign: TextAlign.center,
          //   style: textTheme.bodyLarge?.copyWith(color: Palette.blackSecondary),
          // ),
          // Gap(10.h),
          TextButton(
            onPressed: () => context.pushNamed(kCreateOrderRoute),
            child: Text(
              'Create a new order →',
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
