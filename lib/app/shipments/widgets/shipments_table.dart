import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

import 'shipment_status_badge.dart';

final deletingIdProvider = StateProvider<String>((_) => '');

class ShipmentsTableWidget extends ConsumerStatefulWidget {
  final List<Shipment> shipments;

  const ShipmentsTableWidget(this.shipments, {super.key});

  @override
  ConsumerState<ShipmentsTableWidget> createState() =>
      _ShipmentsTableWidgetState();
}

class _ShipmentsTableWidgetState extends ConsumerState<ShipmentsTableWidget> {
  final loading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildShipmentsList(textTheme),
        ),
      ],
    );
  }

  Widget _buildShipmentsList(TextTheme textTheme) {
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final shipment = widget.shipments[index];
              return _buildShipmentRow(shipment, textTheme, index);
            },
            childCount: widget.shipments.length,
          ),
        ),
      ],
    );
  }

  Widget _buildShipmentRow(Shipment shipment, TextTheme textTheme, int index) {
    return HoverableContainer(
      index: index,
      builder: (isHovered) => InkWell(
        onTap: () => _navigateToShipmentDetails(shipment.id),
        child: _buildTableRow(shipment, isHovered, textTheme),
      ),
    );
  }

  void _navigateToShipmentDetails(String shipmentId) {
    context.goNamed(
      kShippingDetailsRoute,
      pathParameters: {'id': shipmentId},
    );
  }

  Widget _buildTableRow(
      Shipment shipment, bool isHovered, TextTheme textTheme) {
    return Table(
      columnWidths: const {
        0: FlexColumnWidth(0.3), // Empty column for spacing
        1: FlexColumnWidth(1.6), // Shipment number
        2: FlexColumnWidth(1.3), // Created date
        3: FlexColumnWidth(1.4), // Shipping status
        4: FlexColumnWidth(1.6), // Invoice numbers
        5: FlexColumnWidth(4.0), // Actions
      },
      children: [
        TableRow(
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Palette.stroke)),
          ),
          children: [
            Container(),
            _buildContentCell(shipment.shipmentNumber.toString(), textTheme),
            _buildContentCell(shipment.createdAt?.toFixedDate(), textTheme),
            _buildContentCell(
                ShipmentStatusBadge(shipment.shippingStatus), textTheme),
            _buildInvoiceCell(shipment.invoices, textTheme),
            _buildActionsCell(shipment, textTheme),
          ],
        ),
      ],
    );
  }

  Widget _buildInvoiceCell(List<dynamic>? invoices, TextTheme textTheme) {
    final invoiceNumbers = (invoices ?? [])
        .map((x) => (x.invoiceNumber ?? '').toString())
        .join(', ');

    return _buildContentCell(
      invoiceNumbers.isEmpty ? '-' : invoiceNumbers,
      textTheme,
    );
  }

  Widget _buildActionsCell(Shipment shipment, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(top: 5.5),
      child: Align(
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: _buildMoreOptionsButton(shipment),
            ),
            const Gap(40),
          ],
        ),
      ),
    );
  }

  Widget _buildMoreOptionsButton(Shipment shipment) {
    return Align(
      alignment: Alignment.centerRight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          PopupMenuButton<String>(
            useRootNavigator: true,
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onSelected: (value) => _handleMenuSelection(value, shipment),
            itemBuilder: (context) => _buildMenuItems(shipment),
            color: Colors.white,
            tooltip: '',
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            padding: EdgeInsets.zero,
            menuPadding: EdgeInsets.zero,
            enableFeedback: false,
          ),
          const Gap(10),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value, Shipment shipment) {
    if (value == 'copy') {
      if (shipment.shipmentNumber != null) {
        Clipboard.setData(
            ClipboardData(text: shipment.shipmentNumber.toString()));
        Toast.show("Shipping number copied to clipboard", context,
            duration: 2, title: '');
        // ScaffoldMessenger.of(context).showSnackBar(
        //   const SnackBar(
        //     content: Text('Shipping number copied to clipboard'),
        //     duration: Duration(seconds: 2),
        //   ),
        // );
      }
    }
  }

  List<PopupMenuItem<String>> _buildMenuItems(Shipment shipment) {
    final items = <PopupMenuItem<String>>[
      const PopupMenuItem(
        value: 'copy',
        child: ListTile(
          leading: Icon(Icons.copy_outlined),
          title: Text(
            'Copy Shipping Number',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
    ];

    return items;
  }

  Widget _buildContentCell(dynamic content, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: content is String
          ? Text(
              content,
              style: textTheme.bodyMedium?.copyWith(
                color: Palette.blackSecondary,
              ),
              overflow: TextOverflow.ellipsis,
            )
          : content,
    );
  }
}
