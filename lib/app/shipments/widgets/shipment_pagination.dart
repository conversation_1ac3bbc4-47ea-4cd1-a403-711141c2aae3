import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:td_procurement/app/shipments/shipment_controller.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class ShipmentPagination extends ConsumerWidget {
  final int? total;
  const ShipmentPagination({super.key, this.total});

  @override
  Widget build(BuildContext context, ref) {
    final shipmentState = ref.watch(shipmentControllerProvider);
    final params = shipmentState.fetchShipmentsParams;
    final perPage = params.perPage;
    final start = ((params.currentPage - 1) * perPage) + 1;
    final end = params.currentPage * perPage;
    final actualTotalCount = shipmentState.shipments.maybeWhen(
      data: (data) => data.length,
      orElse: () => 0,
    );

    return Padding(
      padding: const EdgeInsets.only(left: 56, right: 75),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$start - ${end > actualTotalCount ? actualTotalCount : end} of $actualTotalCount',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: Palette.blackSecondary),
          ),
          if (params.totalPages > 0)
            IntrinsicWidth(
              child: NumberPagination(
                onPageChanged: (int pageNumber) {
                  ref.read(shipmentControllerProvider.notifier).fetchShipments(
                      params.copyWith(currentPage: pageNumber),
                      forced: true);
                },
                visiblePagesCount: perPage <= 1
                    ? 1
                    : perPage > 5
                        ? 5
                        : perPage,
                buttonElevation: 0.5,
                totalPages: params.totalPages,
                currentPage: params.currentPage,
                buttonRadius: 6,
                selectedButtonColor: Palette.primaryBlack,
                selectedNumberColor: Colors.white,
                unSelectedButtonColor: Colors.white,
                unSelectedNumberColor: Palette.blackSecondary,
                fontSize: 14,
                numberButtonSize: const Size(35, 35),
                controlButtonSize: const Size(40, 40),
                firstPageIcon: SvgPicture.asset(kDoubleChevronLeftSvg,
                    width: 25, height: 25),
                previousPageIcon: SvgPicture.asset(kChevronLeftSvg,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn),
                    width: 16,
                    height: 16),
                lastPageIcon: SvgPicture.asset(kDoubleChevronRightSvg,
                    width: 25, height: 25),
                nextPageIcon: SvgPicture.asset(kChevronRightSvg,
                    width: 16,
                    height: 16,
                    colorFilter: ColorFilter.mode(
                        Palette.blackSecondary, BlendMode.srcIn)),
              ),
            ),
        ],
      ),
    );
  }
}
