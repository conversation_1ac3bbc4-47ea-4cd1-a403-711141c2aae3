import 'package:flutter/material.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

class TileWidget extends StatelessWidget {
  const TileWidget({
    super.key,
    this.title,
    this.subtitle,
    this.hyperLink = false,
  });
  final String? title;
  final dynamic subtitle;
  final bool? hyperLink;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title ?? '-',
          style: textTheme.bodyMedium?.copyWith(
            color: Palette.blackSecondary,
          ),
        ),
        InkWell(
          onTap: hyperLink! ? () => openUri(subtitle!) : null,
          child: subtitle is Widget
              ? subtitle
              : Text(
                  subtitle ?? '-',
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: hyperLink!
                        ? Palette.strokePressed
                        : Palette.blackSecondary,
                  ),
                ),
        )
      ],
    );
  }
}
