import 'package:flutter/material.dart';
import 'package:td_commons_flutter/utils/index.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class ShipmentStatusBadge extends StatelessWidget {
  const ShipmentStatusBadge(this.shipmentStatus, {super.key});

  final String? shipmentStatus;

  @override
  Widget build(BuildContext context) {
    String status;
    Color color;

    switch ((shipmentStatus ?? '').toLowerCase()) {
      case 'pending':
        status = 'Pending';
        color = HexColor('#6B797C');
        break;
      case 'open':
        status = 'Open';
        color = HexColor('#6B797C');
        break;
      case 'ready':
        status = 'Ready';
        color = HexColor('#0610FF');
        break;
      case 'processing':
        status = 'Processing';
        color = HexColor('#FF8C06');
        break;
      case 'skipped':
        status = 'Skipped';
        color = HexColor('#FF4E47');
        break;
      case 'dispatched':
        status = 'Dispatched';
        color = HexColor('#0610FF');
        break;
      case 'delivered':
        status = 'Delivered';
        color = HexColor('#08AA49');
        break;
      case 'scheduled':
        status = 'Scheduled';
        color = HexColor('#117BFF');
      default:
        status = '-';
        color = HexColor('#6B797C');
        break;
    }

    return Row(
      children: [
        Flexible(
          child: DecoratedBox(
            // alignment: Alignment.center,
            // padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              child: Text(
                capitalize(status),
                style: TextStyle(color: color, fontSize: 12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
