import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/core/models/index.dart';

class FetchShipmentsResponse {
  final List<Shipment> shipments;
  final QueryParameters queryParams;

  FetchShipmentsResponse({
    required this.shipments,
    required this.queryParams,
  });
}

class FetchShipmentsParam {
  final String status;
  final List<DateTime?> selectedDates;
  final String searchText;
  final int currentPage;
  int perPage;
  int totalPages;
  bool loaded;
  bool loadingMore;
  final int activeIndex;

  FetchShipmentsParam({
    required this.status,
    required this.selectedDates,
    required this.searchText,
    required this.currentPage,
    this.perPage = 10,
    this.totalPages = 1,
    this.loaded = false,
    this.loadingMore = false,
    this.activeIndex = 0,
  });

  factory FetchShipmentsParam.defaultValue() {
    return FetchShipmentsParam(
      status: 'all',
      selectedDates: [],
      searchText: '',
      currentPage: 1,
      perPage: 10,
      totalPages: 0,
      loaded: false,
      loadingMore: false,
      activeIndex: 0,
    );
  }

  int get totalCount => totalPages * perPage;

  FetchShipmentsParam copyWith({
    String? status,
    List<DateTime?>? selectedDates,
    String? searchText,
    int? currentPage,
    int? perPage,
    int? totalPages,
    bool? loaded,
    bool? loadingMore,
    int? activeIndex,
  }) {
    return FetchShipmentsParam(
      status: status ?? this.status,
      selectedDates: selectedDates ?? this.selectedDates,
      searchText: searchText ?? this.searchText,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      totalPages: totalPages ?? this.totalPages,
      loaded: loaded ?? this.loaded,
      loadingMore: loadingMore ?? this.loadingMore,
      activeIndex: activeIndex ?? this.activeIndex,
    );
  }

  FetchShipmentsParam fromQueryParams(QueryParameters params) {
    return FetchShipmentsParam(
      status: status,
      selectedDates: selectedDates,
      searchText: searchText,
      loaded: loaded,
      loadingMore: loadingMore,
      currentPage: params.page,
      perPage: params.perPage,
      totalPages: params.totalPages,
      activeIndex: activeIndex,
    );
  }
}

class TrackingStatus {
  final String status;
  final bool completed;
  final bool pending;
  final DateTime? createdAt;

  TrackingStatus({
    required this.status,
    this.completed = false,
    this.pending = false,
    this.createdAt,
  });

  Map<String, dynamic> toMap() => {
        'status': status,
        'completed': completed,
        'pending': pending,
        'createdAt': createdAt?.toIso8601String(),
      };

  TrackingStatus copyWith({
    String? status,
    bool? completed,
    bool? pending,
    DateTime? createdAt,
  }) {
    return TrackingStatus(
      status: status ?? this.status,
      completed: completed ?? this.completed,
      pending: pending ?? this.pending,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
