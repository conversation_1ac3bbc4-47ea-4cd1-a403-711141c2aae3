import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/shell/domain/shell_repo.dart';
import 'package:td_procurement/app/shell/models/global_search.model.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final globalSearchUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<GlobalSearch>>, String>(
  (ref, arg) => UseCase<GlobalSearch>()
      .call(() => ref.read(shellRepoProvider).globalSearch(arg)),
);
