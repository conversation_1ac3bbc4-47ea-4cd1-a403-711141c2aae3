import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/core/models/index.dart';

class GlobalSearch {
  final List<Transaction>? orders;
  final List<Invoice>? invoices;

  GlobalSearch({this.orders, this.invoices});

  factory GlobalSearch.fromJson(List<dynamic> json) {
    return GlobalSearch(
      orders: json[0] != null
          ? List<Transaction>.from(
              json[0]['orders'].map((item) => Transaction.fromMap(item)))
          : null,
      invoices: json[3] != null
          ? List<Invoice>.from(
              json[3]['invoices'].map((item) => Invoice.fromJson(item)))
          : null,
    );
  }
}

class Order {
  final String id;
  final List<Item> items;
  final String producerId;
  final String issuedAt;
  final Currency currency;
  final String extChannel;
  final double taxRate;
  final String customerName;
  final String? name;
  final String? orderNumber;

  Order(
      {required this.id,
      required this.items,
      required this.producerId,
      required this.issuedAt,
      required this.currency,
      required this.extChannel,
      required this.taxRate,
      required this.customerName,
      required this.name,
      required this.orderNumber});

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
        id: json['_id'],
        items:
            List<Item>.from(json['items'].map((item) => Item.fromJson(item))),
        producerId: json['producerId'],
        issuedAt: json['issuedAt'],
        currency: Currency.fromJson(json['currency']),
        extChannel: json['extChannel'],
        taxRate: json['taxRate'].toDouble(),
        customerName: json['customerName'],
        name: json['name'],
        orderNumber: json['globalOrderNumber']);
  }
}

class Item {
  final String id;
  final String variantId;
  final String name;
  final double price;
  final int quantity;

  Item(
      {required this.id,
      required this.variantId,
      required this.name,
      required this.price,
      required this.quantity});

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json['_id'],
      variantId: json['variantId'],
      name: json['name'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
    );
  }
}

class Currency {
  final String iso;
  final String symbol;

  Currency({required this.iso, required this.symbol});

  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      iso: json['iso'],
      symbol: json['symbol'],
    );
  }
}
