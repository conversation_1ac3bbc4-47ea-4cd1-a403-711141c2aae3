import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/shell/data/data_source.dart/shell_data_source.dart';
import 'package:td_procurement/app/shell/models/global_search.model.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

abstract class ShellRepo {
  Future<ApiResponse<GlobalSearch>> globalSearch(String params);
}

class ShellRepoImplementation extends ShellRepo {
  final Ref _ref;

  ShellRepoImplementation(this._ref);

  late final ShellDataSource _dataSource = _ref.read(shellProvider);

  @override
  Future<ApiResponse<GlobalSearch>> globalSearch(String params) {
    return dioInterceptor(() => _dataSource.globalSearch(params), _ref);
  }
}

final shellRepoProvider = Provider<ShellRepo>((ref) {
  return ShellRepoImplementation(ref);
});
