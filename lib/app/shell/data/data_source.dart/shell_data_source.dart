import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/shell/data/data_source.dart/shell_data_source_impl.dart';
import 'package:td_procurement/app/shell/models/global_search.model.dart';

abstract class ShellDataSource {
  Future<GlobalSearch> globalSearch(String params);
}

final shellProvider = Provider<ShellDataSource>((ref) {
  return ShellDataSourceImplementation(ref);
});
