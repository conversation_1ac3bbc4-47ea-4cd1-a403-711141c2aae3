import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/shell/presentation/ui/widgets/side_bar.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class SideBarTileController {
  final GoRouter router;
  final SideBarItem item;
  bool _isActive = false;

  SideBarTileController(this.router, this.item) {
    final currentRoute = router.routerDelegate.currentConfiguration.fullPath;
    _isActive = currentRoute.startsWith("/${item.route.toLowerCase()}");
  }

  bool get isActive => _isActive;

  void updateActiveState() {
    final currentRoute = router.routerDelegate.currentConfiguration.fullPath;
    _isActive = currentRoute.startsWith("/${item.route.toLowerCase()}");
  }

  void navigate() {
    router.goNamed(item.route.toLowerCase());
  }
}

class SideBarTile extends StatefulWidget {
  final SideBarItem item;
  final bool isActive;
  const SideBarTile({super.key, required this.item, required this.isActive});

  @override
  State<SideBarTile> createState() => _SideBarTileState();
}

class _SideBarTileState extends State<SideBarTile> {
  late final SideBarTileController _controller;
  late final VoidCallback _routeListener;

  @override
  void initState() {
    super.initState();
    _controller = SideBarTileController(GoRouter.of(context), widget.item);
    _routeListener = () => setState(() => _controller.updateActiveState());
    _controller.router.routerDelegate.addListener(_routeListener);
  }

  @override
  void dispose() {
    _controller.router.routerDelegate.removeListener(_routeListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final isExtended = MediaQuery.of(context).size.width > kSideBarMinWidth;

    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: InkWell(
        onTap: _controller.navigate,
        // onTap: _controller.isActive ? null : _controller.navigate,
        child: Container(
          constraints: BoxConstraints.loose(
            Size(isExtended ? 194 : 70, 55),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: _controller.isActive
                ? Border.all(color: Palette.kE7E7E7)
                : null,
            boxShadow: _controller.isActive
                ? [
                    const BoxShadow(
                        offset: Offset(0, 4),
                        blurRadius: 12,
                        spreadRadius: 0,
                        color: Palette.k0000000A),
                  ]
                : null,
            color: _controller.isActive ? Colors.white : Colors.transparent,
          ),
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisAlignment:
                isExtended ? MainAxisAlignment.start : MainAxisAlignment.center,
            crossAxisAlignment: isExtended
                ? CrossAxisAlignment.start
                : CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.item.icon,
                colorFilter: ColorFilter.mode(
                    _controller.isActive
                        ? Palette.primary
                        : Palette.blackSecondary,
                    BlendMode.srcIn),
              ),
              if (isExtended) ...[
                const Gap(10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Text(
                          widget.item.title,
                          style: textTheme.bodyMedium?.copyWith(
                              fontWeight: _controller.isActive
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: _controller.isActive
                                  ? null
                                  : Palette.blackSecondary),
                        ),
                      ),
                      Flexible(
                        child: Text(
                          widget.item.subTitle,
                          style: textTheme.bodySmall?.copyWith(
                              fontWeight: _controller.isActive
                                  ? FontWeight.w500
                                  : FontWeight.w400,
                              color: _controller.isActive
                                  ? null
                                  : Palette.blackSecondary),
                        ),
                      ),
                    ],
                  ),
                )
              ]
            ],
          ),
        ),
      ),
    );
  }
}
