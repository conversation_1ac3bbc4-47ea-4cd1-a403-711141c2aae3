import 'dart:io';

import 'package:deep_pick/deep_pick.dart';
import 'package:dio/dio.dart';

sealed class AppException {
  final Object error;
  AppException(this.error);

  static int? customStatusCode(Response? response) {
    return pick(response?.data, 'data', 'customCode').asIntOrNull();
  }

  static String? customMessage(Response? response) {
    return pick(response?.data, 'message').asStringOrNull();
  }

  static AppException? getException(error) {
    if (error is Exception) {
      try {
        AppException? networkExceptions;
        if (error is DioException) {
          switch (error.type) {
            case DioExceptionType.cancel:
              networkExceptions = RequestCancelled();
              break;
            case DioExceptionType.receiveTimeout:
            case DioExceptionType.sendTimeout:
              networkExceptions = SendTimeout();
              break;
            case DioExceptionType.connectionTimeout:
              networkExceptions = RequestTimeout();
              break;
            case DioExceptionType.badResponse:
              networkExceptions = UnableToProcess();
              break;
            case DioExceptionType.badCertificate:
              networkExceptions = BadRequest();
              break;
            case DioExceptionType.connectionError:
              networkExceptions = ServiceUnavailable();
              break;
            case DioExceptionType.unknown:
              networkExceptions = null;
              break;
          }

          final int? code = customStatusCode(error.response);
          final String? message = customMessage(error.response);

          if (code != null && message != null) {
            networkExceptions =
                CustomErrorWithMessage(code.toString(), message);
          } else if (code != null) {
            networkExceptions = CustomError(code.toString());
          } else if (message != null) {
            networkExceptions = DefaultError(message);
          } else {
            switch (error.response?.statusCode) {
              case 400:
                networkExceptions = _fetchAppException(
                    networkExceptions ?? UnexpectedError(), error);
                break;
              case 401:
              case 422:
                networkExceptions = _fetchAppException(
                    networkExceptions ?? UnauthorisedRequest(), error);
                break;
              case 403:
                networkExceptions ??= UnauthorisedRequest();
                break;
              case 404:
                networkExceptions = _fetchAppException(
                    networkExceptions ?? NotFound('Not found'), error);
                break;
              case 409:
                networkExceptions ??= Conflict();
                break;
              case 408:
                networkExceptions ??= RequestTimeout();
                break;
              case 500:
              case 501:
              case 502:
              case 504:
              case 505:
                networkExceptions = InternalServerError();
                break;
              case 503:
                networkExceptions = ServiceUnavailable();
                break;
              default:
                networkExceptions = _fetchAppException(
                    networkExceptions ?? UnexpectedError(), error);
            }
          }
        } else if (error is SocketException) {
          networkExceptions = NoInternetConnection();
        } else if (error is ServerException) {
          networkExceptions = ServerException();
        } else if (error is CacheException) {
          networkExceptions = CacheException();
        } else {
          networkExceptions = UnexpectedError();
        }
        return networkExceptions;
      } on FormatException {
        return FormatException();
      } catch (_) {
        return UnexpectedError();
      }
    } else {
      if (error.toString().contains('Is not a subtype of')) {
        return UnableToProcess();
      } else {
        return UnexpectedError();
      }
    }
  }

  static const errorModels = [
    'message',
    'error',
    'error.message',
    'data.message',
    'error.data.message',
    'data.error',
    'res'
  ];

  static String? _extractMessage(Map data, String model) {
    final models = model.split('.');

    Map? extracted = data;
    try {
      for (int i = 0; i < models.length; i++) {
        final element = models[i];
        if (extracted?[element] is String) return extracted?[element];

        extracted = extracted?[element];
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  static AppException _fetchAppException(
      AppException defaultException, DioException error) {
    if (error.response?.data is Map) {
      String? errorMsg;
      for (int i = 0; i < errorModels.length; i++) {
        errorMsg = _extractMessage(error.response?.data, errorModels[i]);
        if (errorMsg != null) {
          return DefaultError(errorMsg);
        }
      }
    }
    return defaultException;
  }
}

class RequestCancelled extends AppException {
  RequestCancelled() : super(001);
}

class UnauthorisedRequest extends AppException {
  UnauthorisedRequest() : super(002);
}

class BadRequest extends AppException {
  BadRequest() : super(003);
}

class NotFound extends AppException {
  final String reason;

  NotFound(this.reason) : super(reason);
}

class MethodNotAllowed extends AppException {
  MethodNotAllowed() : super(004);
}

class NotAcceptable extends AppException {
  NotAcceptable() : super(005);
}

class RequestTimeout extends AppException {
  RequestTimeout() : super(006);
}

class SendTimeout extends AppException {
  SendTimeout() : super(007);
}

class Conflict extends AppException {
  Conflict() : super(008);
}

class InternalServerError extends AppException {
  InternalServerError() : super(009);
}

class NotImplemented extends AppException {
  NotImplemented() : super(010);
}

class ServiceUnavailable extends AppException {
  ServiceUnavailable() : super(011);
}

class NoInternetConnection extends AppException {
  NoInternetConnection() : super("No Internet Connection");
}

class FormatException extends AppException {
  FormatException() : super(013);
}

class UnableToProcess extends AppException {
  UnableToProcess() : super(014);
}

class DefaultError extends AppException {
  final String errorMessage;

  DefaultError(this.errorMessage) : super(errorMessage);
}

class UnexpectedError extends AppException {
  UnexpectedError() : super(015);
}

class ServerException extends AppException {
  ServerException() : super(016);
}

class CacheException extends AppException {
  CacheException() : super(017);
}

class ArgumentException extends AppException {
  ArgumentException() : super(018);
}

class UnknownException extends AppException {
  UnknownException() : super(019);
}

class FireBaseAuthException extends AppException {
  final String errorMessage;

  FireBaseAuthException(this.errorMessage) : super(errorMessage);
}

class CustomError extends AppException {
  final String code;

  CustomError(this.code) : super(code);
}

class CustomErrorWithMessage extends AppException {
  final String code;
  final String message;

  CustomErrorWithMessage(this.code, this.message) : super('$code: $message');
}
