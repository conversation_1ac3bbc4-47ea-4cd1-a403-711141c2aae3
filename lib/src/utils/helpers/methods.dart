import 'dart:math';

import 'package:flutter/material.dart';
import 'package:td_commons_flutter/constants/app_values.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/models/sized_item.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:url_launcher/url_launcher.dart';

Future<T?> showCustomGeneralDialog<T>(
  BuildContext context, {
  required Widget child,

  /// Percentage of the screen's width to be used for the right section.
  /// Value between `0.0` to `1.0`, default is `0.5` (50% of the screen).
  double percentage = 0.5,

  /// Determines if tapping outside the dialog should dismiss it.
  bool dismissible = true,

  /// Minimum width for the right section.
  double minRightSectionWidth = 400,

  /// Callback that is called when the dialog is dismissed
  VoidCallback? onDismiss,
}) {
  return showGeneralDialog<T>(
    context: context,
    barrierDismissible: dismissible,
    barrierLabel: 'Dismiss',
    barrierColor: Colors.black.withAlpha(128),
    transitionDuration: const Duration(milliseconds: 300),
    pageBuilder: (context, anim1, anim2) {
      return LayoutBuilder(
        builder: (context, constraints) {
          final screenWidth = constraints.maxWidth;

          // Calculate the right section width
          double rightSectionWidth = screenWidth * percentage;
          if (rightSectionWidth < minRightSectionWidth) {
            rightSectionWidth = minRightSectionWidth;
          }

          // The left section width is the remaining screen width
          final double leftSectionWidth = screenWidth - rightSectionWidth;

          return Stack(
            children: [
              if (leftSectionWidth > 0)
                GestureDetector(
                  onTap: () {
                    if (dismissible) {
                      onDismiss?.call();
                      Navigator.pop(context);
                    }
                  },
                  child: Container(
                    width: leftSectionWidth,
                    height: double.infinity,
                    color: Colors.transparent,
                  ),
                ),
              // The right section that slides in
              Positioned(
                right: 0,
                width: rightSectionWidth,
                top: 0,
                bottom: 0,
                child: hasScaffold(context) ? child : Scaffold(body: child),
              ),
            ],
          );
        },
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      final slideAnimation = Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(animation);

      return SlideTransition(
        position: slideAnimation,
        child: child,
      );
    },
  );
}

/// A utility function to create a reusable [OverlayEntry].
///
/// - [context]: The [BuildContext] for overlay positioning.
/// - [size]: An instance of [SizedItem] containing dimensions and offsets.
/// - [child]: The widget to be displayed inside the overlay.
/// - [layerLink]: A [LayerLink] for the [CompositedTransformFollower].
/// - [onCloseOverlay]: Callback to handle overlay dismissal.
OverlayEntry createOverlayEntry(
  BuildContext context, {
  required SizedItem size,
  required LayerLink layerLink,
  required VoidCallback onCloseOverlay,
  required Widget child,
  Offset? offset,
}) {
  return OverlayEntry(
    builder: (context) {
      return Stack(
        children: [
          // Background overlay to capture outside taps
          Positioned.fill(
            child: GestureDetector(
              onTap: onCloseOverlay,
              behavior: HitTestBehavior.translucent,
            ),
          ),
          // Positioned child overlay
          Positioned(
            right: MediaQuery.of(context).size.width - (size.width + 35),
            top: MediaQuery.of(context).size.height * (size.top + size.height),
            width: size.width,
            height: size.height + 25,
            child: CompositedTransformFollower(
              link: layerLink,
              offset:
                  Offset(-size.width + (offset?.dx ?? 155), (offset?.dy ?? 40)),
              showWhenUnlinked: false,
              child: Material(
                elevation: 8.0,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Palette.stroke),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: child,
                ),
              ),
            ),
          ),
        ],
      );
    },
  );
}

bool hasScaffold(BuildContext context) {
  try {
    // If Scaffold.of(context) doesn't throw an exception,
    // it means a Scaffold is present in the widget tree.
    Scaffold.of(context);
    return true;
  } catch (e) {
    // If an exception is thrown, there's no Scaffold.
    return false;
  }
}

String getSmallImageURL(String? id, ENV? env, [bool useProdUrl = false]) {
  if (id == null || id.isEmpty) return '';

  if (useProdUrl) {
    return getSmallImageUrl(id);
  } else if (env == ENV.dev) {
    return 'https://td-dev-img.s3.amazonaws.com/xs/$id.png';
  } else {
    return getSmallImageUrl(id);
  }
}

String getLargeImageURL(String? id, ENV? env, [bool useProdUrl = false]) {
  if (id == null || id.isEmpty) return '';

  if (useProdUrl) {
    return getLargeImageUrl(id);
  } else if (env == ENV.dev) {
    return 'https://td-dev-img.s3.amazonaws.com/xl/$id.png';
  } else {
    return getLargeImageUrl(id);
  }
}

Iterable<E> mapIndexed<E, T>(
    Iterable<T> items, E Function(int index, T item) f) sync* {
  var index = 0;

  for (final item in items) {
    yield f(index, item);
    index = index + 1;
  }
}

DateTime getXMonthsBefore(DateTime inputDate, int x) {
  return DateTime(
    inputDate.year,
    inputDate.month - x,
    inputDate.day,
  );
}

Future<void> openUri(String url) async {
  // Ensure the URL has a valid scheme
  final Uri uri =
      Uri.parse(url.startsWith(RegExp(r'https?://')) ? url : 'https://$url');

  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } else {
    throw 'Could not launch $url';
  }
}

final _random = Random();

String generateRandomId() {
  final timestamp = DateTime.now().microsecondsSinceEpoch;
  final randomPart =
      _random.nextInt(0xFFFFFFF).toRadixString(16).padLeft(7, '0');
  return '${timestamp}_$randomPart';
}

int? parseInt(dynamic data) {
  if (data is int) return data;
  if (data is String) return int.parse(data);
  if (data is num) return data.toInt();
  return null;
}

num? parseNum(dynamic data) {
  if (data is int) return data;
  if (data is String) return num.tryParse(data);
  if (data is num) return data;
  return null;
}

String encodeMap(Map data) {
  return data.keys
      .map((key) =>
          "${Uri.encodeComponent(key)}=${Uri.encodeComponent(data[key]?.toString() ?? '')}")
      .join("&");
}
