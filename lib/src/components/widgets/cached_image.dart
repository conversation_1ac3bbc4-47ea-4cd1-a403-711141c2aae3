import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/constants/app_values.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

enum ImageSize { small, large }

class CachedImage extends ConsumerWidget {
  final String? variantId;
  final ImageSize size;
  const CachedImage(this.variantId, this.size, {super.key});

  String imageUrl(WidgetRef ref, [bool useProdUrl = false]) {
    final config = ref.read(appConfigProvider);
    switch (size) {
      case ImageSize.small:
        return getSmallImageURL(variantId, config.env, useProdUrl);
      case ImageSize.large:
        return getLargeImageURL(variantId, config.env, useProdUrl);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Switched from CachedNetworkImage to Image.network due to CORS issues with the new image URL.
    // The previous implementation was causing SecurityErrors in WebGL when trying to load cross-origin images.
    // Specific error: "Failed to execute 'texImage2D' on 'WebGL2RenderingContext': 
    // The image element contains cross-origin data, and may not be loaded"
    return Image.network(
      imageUrl(ref, true),
      headers: const {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
      },
      errorBuilder: (context, error, stackTrace) => Image.network(
        imageUrl(ref),
        headers: const {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
        },
        errorBuilder: (context, error, stackTrace) => Image.asset(
          PLACEHOLDER_URL,
          package: PACKAGE_NAME,
        ),
      ),
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Image.asset(
          PLACEHOLDER_URL,
          package: PACKAGE_NAME,
        );
      },
    );
  }
}
