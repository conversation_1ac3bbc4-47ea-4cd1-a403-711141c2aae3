extension Strings on String {
  String get capitalize {
    String lowercase = toLowerCase();
    if (lowercase.length == 1) lowercase.toUpperCase();
    return lowercase[0].toUpperCase() + lowercase.substring(1);
  }

  String get removeSpaces {
    return replaceAll(" ", "");
  }

  String removeTrailingZerosAndDot() {
    if (!contains('.')) return this; // Return as-is if no decimal point
    return replaceFirst(
        RegExp(r'\.?0+$'), ''); // Remove trailing zeros and optional dot
  }

  String removeTrailingZeros() {
    if (!contains('.')) return this; // Return as-is if no decimal point
    return replaceFirst(RegExp(r'0+$'), ''); // Remove only trailing zeros
  }

  // String removeTrailingZerosAfterDecimal() {
  //   return replaceFirst(RegExp(r'(?<=\.\d*)0+$'), '');
  // }
}
