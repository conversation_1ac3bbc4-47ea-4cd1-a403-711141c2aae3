import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/config/service/api_config.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/env/.env.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/core/services/shared_prefs/shared_prefs.dart';

final storageProvider = Provider(
  (ref) => SharedPrefs(),
);

final appConfigProvider = Provider<AppConfig>((_) {
  return AppConfig.fromJson(appConfig);
});

final apiConfigProvider = Provider<ApiConfig>((ref) {
  return ApiConfig(ref.read(appConfigProvider).appUrl,
      apiKey: () async => ref.exists(userControllerProvider)
          ? ref.read(userControllerProvider)?.apiKey ?? ''
          : '');
});
final apiClientProvider = Provider<TdApiClient>((ref) {
  final config = ref.read(apiConfigProvider);
  return TdApiClient(config);
});

typedef UseCaseFunc<T> = Future<ApiResponse<T>> Function();

class UseCase<T> {
  Future<ApiResponse<T>> call(UseCaseFunc<T> function) async =>
      await function();

  Future<T> init(UseCaseFunc<T> function) async => (await function()).extract();
}
/*
async {
    if (ref.exists(userControllerProvider)) {
      final store = ref.read(storageProvider);
      store.refresh();
      return (json.decode(
              store.read(StorageKeys.user) ?? '{"apiKey":""}')?['apiKey'] ??
          '');
    }
    return '';
  }
 */
