import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_procurement/app/account/presentation/views/screens/account_statement_screen.dart';
import 'package:td_procurement/app/advance_invoice/presentation/views/screens/advance_invoice_list.dart';
import 'package:td_procurement/app/advance_invoice/presentation/views/screens/create_advance_invoice.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/session_controller.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_in/sign_in.dart';
import 'package:td_procurement/app/auth/presentation/views/screens/sign_up/sign_up.dart';
import 'package:td_procurement/app/business_verification/presentation/views/screens/business_verification.dart';
import 'package:td_procurement/app/deliveries/presentation/views/screens/deliveries.dart';
import 'package:td_procurement/app/deliveries/presentation/views/screens/deliveries_summary.dart';
import 'package:td_procurement/app/home/<USER>/views/screens/home_screen.dart';
import 'package:td_procurement/app/invoices/data/models/invoice.dart';
import 'package:td_procurement/app/invoices/presentation/views/screens/invoices.dart';
import 'package:td_procurement/app/invoices/presentation/views/screens/payout.dart';
import 'package:td_procurement/app/invoices/presentation/views/screens/payout_summary.dart';
import 'package:td_procurement/app/invoices/presentation/views/widgets/invoice_summary.dart';
import 'package:td_procurement/app/order/screens/index.dart';
import 'package:td_procurement/app/shell/presentation/ui/screens/console_shell.dart';
import 'package:td_procurement/app/shipments/screens/shipments.dart';
import 'package:td_procurement/app/shipments/screens/shipping_details.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/not_found/not_found.dart';
import 'package:td_procurement/src/res/values/storage_keys/storage_keys.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

import 'codec.dart';

final routerProvider = Provider((ref) => AppRouter(ref));

class AppRouter {
  Ref ref;

  final _rootNavigatorKey = GlobalKey<NavigatorState>();

  final _shellNavigatorKey = GlobalKey<NavigatorState>();

  late final router = GoRouter(
    initialLocation: '/',
    navigatorKey: _rootNavigatorKey,
    routes: [
      GoRoute(
        path: '/',
        parentNavigatorKey: _rootNavigatorKey,
        redirect: (_, __) => kLoginRoute.path,
        pageBuilder: (_, __) => const MaterialPage(
          child: SignIn(),
        ),
      ),
      GoRoute(
        path: kGetStartedRoute.path,
        name: kGetStartedRoute,
        parentNavigatorKey: _rootNavigatorKey,
        pageBuilder: (_, __) => const MaterialPage(
          child: Placeholder(),
        ),
      ),
      GoRoute(
        path: kRegisterRoute.path,
        name: kRegisterRoute,
        parentNavigatorKey: _rootNavigatorKey,
        pageBuilder: (_, __) => const MaterialPage(
          child: SignUp(),
        ),
      ),
      GoRoute(
        path: kLoginRoute.path,
        name: kLoginRoute,
        parentNavigatorKey: _rootNavigatorKey,
        pageBuilder: (_, __) => const MaterialPage(
          child: SignIn(),
        ),
      ),
      GoRoute(
        path: kBusinessVerificationRoute.path,
        name: kBusinessVerificationRoute,
        parentNavigatorKey: _rootNavigatorKey,
        pageBuilder: (_, __) =>
            const MaterialPage(child: BusinessVerification()),
      ),
      GoRoute(
        path: kInvoiceSummaryRoute.path,
        name: kInvoiceSummaryRoute,
        parentNavigatorKey: _rootNavigatorKey,
        pageBuilder: (_, state) {
          return CustomTransitionPage(
            child: InvoiceSummary(
              state.extra as String,
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOut,
                ),
                child: child,
              );
            },
          );
        },
      ),
      /*GoRoute(
            path: kStripeRoute.path,
            name: kStripeRoute,
            parentNavigatorKey: _rootNavigatorKey,
            pageBuilder: (_, state) {
              final data = state.extra as InitiateStripeResponse;
              return MaterialPage(
                child: VerificationScreen(data),
              );
            }),*/
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return ConsoleShell(child);
        },
        routes: [
          GoRoute(
            path: kHomeRoute.path,
            name: kHomeRoute,
            pageBuilder: (_, __) => const MaterialPage(
              child: HomeScreen(),
            ),
          ),
          GoRoute(
            path: kAccountRoute.path,
            name: kAccountRoute,
            pageBuilder: (_, __) => const MaterialPage(
              child: AccountStatementScreen(),
            ),
          ),
          GoRoute(
            path: kInvoicesRoute.path,
            name: kInvoicesRoute,
            pageBuilder: (_, state) {
              dynamic status;
              if (state.extra != null) {
                try {
                  status = state.extra as InvoiceStatus?;
                } catch (e) {
                  // If state.extra isn't an InvoiceStatus, we don't want to crash
                  // Instead, we'll pass null status which shows all invoices
                  status = null;
                }
              }
              return MaterialPage(
                child: Invoices(
                  InvoiceType.purchase,
                  status: status,
                ),
              );
            },
            routes: [
              GoRoute(
                parentNavigatorKey: _shellNavigatorKey,
                path: ':id',
                name: kInvoiceDetailsRoute,
                pageBuilder: (_, state) {
                  final id = state.pathParameters['id'] ?? '';
                  return CustomTransitionPage(
                    child: InvoiceSummary(
                      id,
                      invoiceType: 'Purchase',
                    ),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      // Use a fade transition for smooth navigation
                      return FadeTransition(
                        opacity: CurvedAnimation(
                          parent: animation,
                          curve: Curves.easeOut,
                        ),
                        child: child,
                      );
                    },
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: kSalesInvoicesRoute,
            name: kSalesInvoicesRoute,
            pageBuilder: (_, state) {
              dynamic status;
              if (state.extra != null) {
                try {
                  status = state.extra as InvoiceStatus?;
                } catch (e) {
                  // If state.extra isn't an InvoiceStatus, we don't want to crash
                  // Instead, we'll pass null status which shows all invoices
                  status = null;
                }
              }
              return MaterialPage(
                child: Invoices(
                  InvoiceType.sales,
                  status: status,
                ),
              );
            },
            routes: [
              GoRoute(
                parentNavigatorKey: _shellNavigatorKey,
                path: ':id',
                name: kSalesInvoiceSummaryRoute,
                pageBuilder: (_, state) {
                  final id = state.pathParameters['id'] ?? '';
                  return CustomTransitionPage(
                    child: InvoiceSummary(
                      id,
                      invoiceType: 'Sales',
                    ),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      // Use a fade transition for smooth navigation
                      return FadeTransition(
                        opacity: CurvedAnimation(
                          parent: animation,
                          curve: Curves.easeOut,
                        ),
                        child: child,
                      );
                    },
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: kForbiddenRoute.path,
            name: kForbiddenRoute,
            pageBuilder: (_, state) => const MaterialPage(
              child: NotFoundPage(),
            ),
          ),
          GoRoute(
            path: kOrdersRoute.path,
            name: kOrdersRoute,
            pageBuilder: (_, state) {
              if (state.extra == null) {
                return const MaterialPage(child: OrdersScreen(false, false));
              } else {
                dynamic data;

                try {
                  data = state.extra as Map;
                } catch (e) {
                  // If state.extra is a Transaction, we don't want to crash
                  // Instead, we'll pass an empty object.
                  data = {};
                }
                return MaterialPage(
                  child: OrdersScreen(
                      data?['isRefreshing'] == true, data?['pending'] == true),
                );
              }
            },
            routes: [
              GoRoute(
                parentNavigatorKey: _rootNavigatorKey,
                path: 'new',
                name: kCreateOrderRoute,
                pageBuilder: (_, state) => CustomTransitionPage(
                  child: CreateOrderScreen(
                      transaction: state.extra as Transaction?),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const Curve curve = Curves.easeInOut;

                    var opacityAnimation =
                        Tween<double>(begin: 0, end: 1).animate(
                      CurvedAnimation(parent: animation, curve: curve),
                    );

                    var scaleAnimation =
                        Tween<double>(begin: 0.5, end: 1.0).animate(
                      CurvedAnimation(parent: animation, curve: curve),
                    );

                    return FadeTransition(
                      opacity: opacityAnimation,
                      child: ScaleTransition(
                        scale: scaleAnimation,
                        child: child,
                      ),
                    );
                  },
                ),
              ),
              GoRoute(
                parentNavigatorKey: _shellNavigatorKey,
                path: ':id',
                name: kOrderSummaryRoute,
                pageBuilder: (_, state) {
                  final id = state.pathParameters['id'] ?? '';
                  final action = state.uri.queryParameters['action'];
                  final transaction = state.extra as Transaction?;
                  return MaterialPage(
                    child: OrderSummaryScreen(id,
                        transaction: transaction, action: action),
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: kSalesOrdersRoute.path,
            name: kSalesOrdersRoute,
            pageBuilder: (_, state) => MaterialPage(
              child: SalesOrdersScreen(
                  (state.extra is bool) ? state.extra as bool : false),
            ),
            routes: [
              GoRoute(
                parentNavigatorKey: _rootNavigatorKey,
                path: 'new',
                name: kCreateSalesOrderRoute,
                pageBuilder: (_, state) => CustomTransitionPage(
                  child: const CreateSalesOrderScreen(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const Curve curve = Curves.easeInOut;

                    var opacityAnimation =
                        Tween<double>(begin: 0, end: 1).animate(
                      CurvedAnimation(parent: animation, curve: curve),
                    );

                    var scaleAnimation =
                        Tween<double>(begin: 0.5, end: 1.0).animate(
                      CurvedAnimation(parent: animation, curve: curve),
                    );

                    return FadeTransition(
                      opacity: opacityAnimation,
                      child: ScaleTransition(
                        scale: scaleAnimation,
                        child: child,
                      ),
                    );
                  },
                ),
              ),
              GoRoute(
                parentNavigatorKey: _shellNavigatorKey,
                path: ':id',
                name: kSalesOrderSummaryRoute,
                pageBuilder: (_, state) {
                  final id = state.pathParameters['id'] ?? '';
                  final order = state.extra as Order?;
                  return MaterialPage(
                    child: SalesOrderSummaryScreen(id, order: order),
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: kAdvanceInvoiceListRoute.path,
            name: kAdvanceInvoiceListRoute,
            pageBuilder: (_, state) => const MaterialPage(
              child: AdvanceInvoiceListScreen(),
            ),
            routes: [
              GoRoute(
                parentNavigatorKey: _rootNavigatorKey,
                path: 'new',
                name: kCreateAdvanceInvoiceRoute,
                pageBuilder: (_, state) => CustomTransitionPage(
                  child: const CreateAdvanceInvoiceScreen(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    const Curve curve = Curves.easeInOut;

                    var opacityAnimation =
                        Tween<double>(begin: 0, end: 1).animate(
                      CurvedAnimation(parent: animation, curve: curve),
                    );

                    var scaleAnimation =
                        Tween<double>(begin: 0.5, end: 1.0).animate(
                      CurvedAnimation(parent: animation, curve: curve),
                    );

                    return FadeTransition(
                      opacity: opacityAnimation,
                      child: ScaleTransition(
                        scale: scaleAnimation,
                        child: child,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          GoRoute(
            path: kCatalogRoute.path,
            name: kCatalogRoute,
            pageBuilder: (_, state) => MaterialPage(
              child: CatalogScreen(
                  (state.extra is bool) ? state.extra as bool : false),
            ),
          ),
          GoRoute(
              path: kDeliveriesRoute.path,
              name: kDeliveriesRoute,
              pageBuilder: (_, state) =>
                  const MaterialPage(child: DeliveriesWidget()),
              routes: [
                GoRoute(
                    path: ':id',
                    parentNavigatorKey: _shellNavigatorKey,
                    name: kDeliveryDetailsRoute,
                    pageBuilder: (_, state) {
                      final id = state.pathParameters['id'] ?? '';
                      return MaterialPage(
                          child: DeliveriesSummary(
                        deliveryId: id,
                      ));
                    })
              ]),
          GoRoute(
            path: kPayoutRoute.path,
            name: kPayoutRoute,
            pageBuilder: (_, state) => const MaterialPage(
              child: Payout(),
            ),
            routes: [
              GoRoute(
                path: ':id',
                name: kPayoutSummaryRoute,
                pageBuilder: (_, state) => MaterialPage(
                    child:
                        PayoutSummaryScreen(state.pathParameters['id'] ?? '')),
              ),
            ],
          ),
          GoRoute(
            path: kShipmentsRoute.path,
            name: kShipmentsRoute,
            pageBuilder: (_, state) => MaterialPage(
              child: ShipmentsScreen(
                  (state.extra is bool) ? state.extra as bool : false),
            ),
            routes: [
              GoRoute(
                parentNavigatorKey: _shellNavigatorKey,
                path: ':id',
                name: kShippingDetailsRoute,
                pageBuilder: (_, state) {
                  final id = state.pathParameters['id'] ?? '';
                  return MaterialPage(child: ShippingDetails(id));
                },
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        parentNavigatorKey: _rootNavigatorKey,
        path: kContainerInfoRoute.path,
        name: kContainerInfoRoute,
        pageBuilder: (_, state) {
          return CustomTransitionPage(
            child:
                ContainerInformationScreen(state.extra as OrderPreviewDetail),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const Curve curve = Curves.easeInOut;

              var slideInAnimation = Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(parent: animation, curve: curve),
              );

              var slideOutAnimation = Tween<Offset>(
                begin: Offset.zero,
                end: const Offset(-1.0, 0.0),
              ).animate(
                CurvedAnimation(parent: secondaryAnimation, curve: curve),
              );

              return SlideTransition(
                position: slideInAnimation,
                child: SlideTransition(
                  position: slideOutAnimation,
                  child: child,
                ),
              );
            },
          );
        },
      ),
    ],
    redirect: (context, state) {
      final store = ref.read(storageProvider)..refresh();
      final loggedIn = store.check(StorageKeys.user);
      bool isOpenRoute = openRoutes.contains(state.matchedLocation);
      String? redirectedRoute;

      if (loggedIn) {
        // Handle open routes and saved redirects
        if (isOpenRoute) {
          redirectedRoute = _savedRedirectingRoute ?? kHomeRoute.path;
          _savedRedirectingRoute = null;
        } else {
          // Check if the route requires specific permissions
          final hasPermission =
              routePermissions[state.matchedLocation]?.call(ref) ?? true;
          if (!hasPermission) {
            redirectedRoute = kForbiddenRoute.path;
          }
        }
      } else {
        // Handle unauthenticated users
        if (!isOpenRoute) {
          redirectedRoute = '/'; // Redirect to login or landing page
          if (ref.read(sessionController).accessToken == null) {
            _savedRedirectingRoute = state.matchedLocation;
          }
        }
      }

      // if (loggedIn && isOpenRoute) {
      //   redirectedRoute = _savedRedirectingRoute ?? kHomeRoute.path;
      //   _savedRedirectingRoute = null;
      // } else if ((!loggedIn) && !isOpenRoute) {
      //   redirectedRoute = "/";
      //   if (ref.read(sessionController).accessToken == null) {
      //     _savedRedirectingRoute = state.matchedLocation;
      //   }
      // }

      return redirectedRoute;
    },
    redirectLimit: 5,
    debugLogDiagnostics: true,
    refreshListenable: ref.read(sessionController),
    errorPageBuilder: (context, state) {
      return const MaterialPage(
        child: NotFoundPage(),
      );
    },
    extraCodec: const AppRouterCodec(),
  );

  String? _savedRedirectingRoute;

  final List<String> openRoutes = ["/", kLoginRoute.path, kRegisterRoute.path];

  final routePermissions = {
    kOrdersRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.purchaseOrder)) ||
        ref.read(isFeatureEnabledProvider(AuthScope.globalOrder)),
    kInvoicesRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.purchaseOrder)) ||
        ref.read(isFeatureEnabledProvider(AuthScope.globalOrder)),
    kSalesOrdersRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.salesOrder)),
    kSalesInvoicesRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.salesOrder)),
    kShipmentsRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.globalOrder)),
    kPayoutRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.payout)),
    kDeliveriesRoute.path: (Ref ref) =>
        ref.read(isFeatureEnabledProvider(AuthScope.delivery)),
  };

  AppRouter(this.ref);

/*  String? _routeRedirection(Object? argument, String path) {
    return argument == null ? path : null;
  }*/
}
////
