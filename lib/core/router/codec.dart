import 'dart:convert';

import 'package:td_commons_flutter/models/order_preview_detail.dart';
import 'package:td_commons_flutter/models/shipment.dart';
import 'package:td_procurement/core/models/index.dart';

class AppRouterCodec extends Codec<Object?, Object?> {
  const AppRouterCodec();

  @override
  Converter<Object?, Object?> get decoder => const _AppRouterDecoder();

  @override
  Converter<Object?, Object?> get encoder => const _AppRouterEncoder();
}

class _AppRouterDecoder extends Converter<Object?, Object?> {
  const _AppRouterDecoder();

  @override
  Object? convert(Object? input) {
    if (input == null) {
      return null;
    }
    final List<Object?> inputAsList = input as List<Object?>;
    if (inputAsList[0] == 'Transaction') {
      return Transaction.fromMap(inputAsList[1] as Map<String, dynamic>);
    }
    if (inputAsList[0] == 'OrderPreviewDetail') {
      return OrderPreviewDetail.fromMap(inputAsList[1] as Map<String, dynamic>);
    }
    if (inputAsList[0] == 'Shipment') {
      return Shipment.fromMap(inputAsList[1] as Map<String, dynamic>);
    }
    return input;
  }
}

class _AppRouterEncoder extends Converter<Object?, Object?> {
  const _AppRouterEncoder();

  @override
  Object? convert(Object? input) {
    if (input == null) {
      return null;
    }
    switch (input) {
      case Transaction transaction:
        return <Object?>['Transaction', transaction.toMap()];
      case OrderPreviewDetail orderPreviewDetail:
        return <Object?>['OrderPreviewDetail', orderPreviewDetail.toMap()];
      case Shipment shipment:
        return <Object?>['Shipment', shipment.toMap()];
      default:
        return input;
    }
  }
}
