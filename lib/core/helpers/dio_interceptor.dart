import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/td_flutter_core.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/src/utils/error_handler/error_handler.dart';
import 'package:td_procurement/src/utils/exceptions/app_exception.dart';

const defaultIgnoredStatusCodes = [
  422,
  404,
  406,
  407,
];

/// Handle request functions
///
/// handles DioException, AppException. Returns ApiResponse.
Future<ApiResponse<T>> dioInterceptor<T>(Future<T> Function() func, Ref ref,
    {List<int> doNotReport = defaultIgnoredStatusCodes,
    bool logoutOn401 = true,
    AppException Function(DioException dioException)? customError<PERSON><PERSON><PERSON>,
    ApiResponse<T>? Function(Response?)? dioExceptionFunc}) async {
/*  final networkInfo = NetworkConnectionImpl(Connectivity());

  if (!await networkInfo.isDeviceConnected) {
    return Failure(
      NoInternetConnection(),
    );
  }*/

  try {
    final result = await func();
    return Success(result);
  } on TypeError catch (e, s) {
    _log(e, s);
    return onTypeError(e, s);
  } on DioException catch (exception) {
    return onDioException(
      exception,
      doNotReport,
      logoutOn401,
      ref,
      dioExceptionFunc: dioExceptionFunc,
      customErrorHandler: customErrorHandler,
    );
  } catch (err, str) {
    ErrorHandler.report(
      err,
      str,
      hint: {'generic': '$err'},
    );
    return Failure(
      DefaultError("Unexpected error occurred"),
    );
  }
}

ApiResponse<T> onTypeError<T>(
  TypeError e,
  StackTrace s,
) {
  ErrorHandler.report(
    e,
    s,
    hint: {'isDioException': false, 'typeError': true},
  );
  return Failure(
    ArgumentException(),
  );
}

ApiResponse<T> onDioException<T>(
    DioException exception, List<int> ignore, bool logoutOn401, Ref ref,
    {ApiResponse<T>? Function(Response?)? dioExceptionFunc,
    Function(DioException dioException)? customErrorHandler}) {
  final response = exception.response;
  final int statusCode =
      AppException.customStatusCode(response) ?? response?.statusCode ?? -2;

  if (statusCode == 401 && logoutOn401) {
    ref.read(userControllerProvider.notifier).onLogout();
  }

  if (dioExceptionFunc != null) {
    final result = dioExceptionFunc(response);
    if (result != null) return result;
  }

  final error = customErrorHandler != null
      ? customErrorHandler(exception)
      : AppException.getException(exception);

  _report(exception, error, statusCode, ignore);

  return Failure(
    error,
    statusCode,
  );
}

void _report(
  DioException exception,
  AppException appException,
  int statusCode,
  List<int> ignore,
) {
  final response = exception.response;

  // Don't report if status code is part of the ignored list
  if (ignore.contains(statusCode)) return;

  ErrorHandler.report(
    exception,
    StackTrace.current,
    hint: {
      'response': response?.data,
      'request': {
        'path': exception.requestOptions.path,
        'data': exception.requestOptions.data,
      }
    },
  );
}

_log(TypeError e, StackTrace s) {
  if (kDebugMode) {
    log(':::Failure::: $e');
    log(':::StackTrace::: $s');
  }
}
