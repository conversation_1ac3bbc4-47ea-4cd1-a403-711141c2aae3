@JS()
library places;

import 'dart:convert';
import 'dart:js_interop';

import 'package:td_procurement/core/services/google_places/model/place.dart';
import 'package:td_procurement/core/services/google_places/model/suggestion.dart';

@JS()
external JSPromise<JSString> _initPrediction(String input);

@JS()
external JSPromise<JSString> _fetchPlace(String id);

class PlacesService {
  static Future<List<dynamic>> initPrediction(String input) async {
    final promise = _initPrediction(input);
    final JSString data = await promise.toDart;
    final String json = data.toString();
    return jsonDecode(json);
  }

  static Future<Map> fetchPlace(String id) async {
    final promise = _fetchPlace(id);
    final JSString data = await promise.toDart;
    final String json = data.toString();
    return jsonDecode(json);
  }

  static Future<Place> fetchPlaceDetails(String id) async {
    final result = await fetchPlace(id);
    final place = Place();

    place.formattedAddress = result['formattedAddress'];
    place.name = result['displayName'];
    place.lat = result['location']['lat'] as double;
    place.lng = result['location']['lng'] as double;

    for (var component in result['addressComponents']) {
      final List type = component['types'];
      if (type.contains('street_address')) {
        place.streetAddress = component['longText'];
      }
      if (type.contains('street_number')) {
        place.streetNumber = component['longText'];
      }
      if (type.contains('route')) {
        place.street = component['longText'];
        place.streetShort = component['shortText'];
      }
      if (type.contains('sublocality') ||
          type.contains('sublocality_level_1')) {
        place.vicinity = component['longText'];
      }
      if (type.contains('locality')) {
        place.city = component['longText'];
      }
      if (type.contains('administrative_area_level_2')) {
        place.county = component['longText'];
      }
      if (type.contains('administrative_area_level_1')) {
        place.state = component['longText'];
        place.stateShort = component['shortText'];
      }
      if (type.contains('country')) {
        place.country = component['longText'];
        place.countryShort = component['shortText'];
      }
      if (type.contains('postal_code')) {
        place.zipCode = component['longText'];
      }
      if (type.contains('postal_code_suffix')) {
        place.zipCodeSuffix = component['shortText'];
      }
    }

    place.zipCodePlus4 ??=
        '${place.zipCode}${place.zipCodeSuffix != null ? '-${place.zipCodeSuffix}' : ''}';
    if (place.streetNumber != null) {
      place.streetAddress ??= '${place.streetNumber} ${place.streetShort}';
      place.formattedAddress ??=
          '${place.streetNumber} ${place.streetShort}, ${place.city}, ${place.stateShort} ${place.zipCode}';
      place.formattedAddressZipPlus4 ??=
          '${place.streetNumber} ${place.streetShort}, ${place.city}, ${place.stateShort} ${place.zipCodePlus4}';
    }
    return place;
  }

  static Future<List<Suggestion>> fetchPredictions(String input) async {
    final result = await initPrediction(input);
    return result.map<Suggestion>(<Map>(p) {
      return Suggestion(p['Eg'][0][1], p['Eg'][0][2][0]);
    }).toList();
  }
}
