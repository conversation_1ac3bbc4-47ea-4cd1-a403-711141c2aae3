import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_login.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_logout.dart';

class IntercomManger implements OnLogin, OnLogout {
  @override
  Future<void> onLogin(User user) async {
    await Intercom.instance.loginIdentifiedUser(
      userId: user.userId,
      email: user.userId != null ? null : (user.email ?? user.phoneEmail),
    );
    await Intercom.instance.updateUser(
      name: capitalize(user.displayName),
      phone: user.phoneNumber,
      company: user.company?.name,
      companyId: user.company?.id,
    );
  }

  @override
  Future<void> onLogout() async {
    Intercom.instance.logout();
  }
}

final intercomManager = Provider((_) => IntercomManger());
